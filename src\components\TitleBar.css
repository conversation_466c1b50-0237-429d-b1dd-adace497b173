.title-bar {
  height: 40px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-bottom: 1px solid rgba(0, 255, 136, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  -webkit-app-region: drag;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.title-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #00ff88 50%, transparent 100%);
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  border-radius: 6px;
  color: #000;
}

.app-title {
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.title-main {
  font-size: 14px;
  font-weight: 700;
  color: #00ff88;
  letter-spacing: 0.5px;
}

.title-sub {
  font-size: 12px;
  font-weight: 500;
  color: #888;
  letter-spacing: 0.3px;
}

.title-bar-center {
  display: flex;
  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: rgba(0, 255, 136, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 136, 0.2);
  font-size: 11px;
  font-weight: 500;
  color: #00ff88;
}

.perf-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
}

.perf-active {
  background: #00ff88;
  box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.title-bar-right {
  display: flex;
  align-items: center;
  gap: 2px;
  -webkit-app-region: no-drag;
}

.title-bar-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.title-bar-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.title-bar-button.close:hover {
  background: #ff4757;
  color: #fff;
}

.title-bar-button.minimize:hover {
  background: #ffa502;
  color: #000;
}

.title-bar-button.maximize:hover {
  background: #00ff88;
  color: #000;
}
