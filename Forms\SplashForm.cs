using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RodeyPremiumTweaker.Forms
{
    public partial class SplashForm : Form
    {
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private Label _titleLabel;
        private Label _versionLabel;
        private System.Windows.Forms.Timer _progressTimer;
        private int _currentProgress = 0;

        public SplashForm()
        {
            InitializeComponent();
            SetupSplashScreen();
            StartLoadingSequence();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // SplashForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 400);
            this.FormBorderStyle = FormBorderStyle.None;
            this.Name = "SplashForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.TopMost = true;

            this.ResumeLayout(false);
        }

        private void SetupSplashScreen()
        {
            // Set dark theme
            this.BackColor = Color.FromArgb(10, 10, 10);
            this.ForeColor = Color.White;

            // Create gradient background
            this.Paint += SplashForm_Paint;

            // Title label
            _titleLabel = new Label
            {
                Text = "RODEY PREMIUM TWEAKER",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(50, 80)
            };

            // Version label
            _versionLabel = new Label
            {
                Text = "Professional Edition v1.0",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(50, 120)
            };

            // Subtitle
            var subtitleLabel = new Label
            {
                Text = "Maximum FPS Performance Optimization",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(50, 160)
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "Initializing...",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(50, 280)
            };

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(50, 310),
                Size = new Size(500, 20),
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.FromArgb(0, 255, 136),
                BackColor = Color.FromArgb(40, 40, 40)
            };

            // Copyright label
            var copyrightLabel = new Label
            {
                Text = "© 2024 Rodey Premium. All rights reserved.",
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(50, 360)
            };

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                _titleLabel,
                _versionLabel,
                subtitleLabel,
                _statusLabel,
                _progressBar,
                copyrightLabel
            });

            // Center title
            _titleLabel.Location = new Point((this.Width - _titleLabel.Width) / 2, 80);
            _versionLabel.Location = new Point((this.Width - _versionLabel.Width) / 2, 120);
            subtitleLabel.Location = new Point((this.Width - subtitleLabel.Width) / 2, 160);
        }

        private void SplashForm_Paint(object sender, PaintEventArgs e)
        {
            // Create gradient background
            using (var brush = new LinearGradientBrush(
                this.ClientRectangle,
                Color.FromArgb(10, 10, 10),
                Color.FromArgb(26, 26, 26),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }

            // Draw border
            using (var pen = new Pen(Color.FromArgb(0, 255, 136), 2))
            {
                e.Graphics.DrawRectangle(pen, 1, 1, this.Width - 3, this.Height - 3);
            }
        }

        private void StartLoadingSequence()
        {
            var loadingSteps = new[]
            {
                "Checking administrator privileges...",
                "Loading system information...",
                "Initializing optimization engine...",
                "Loading performance monitor...",
                "Preparing user interface...",
                "Ready!"
            };

            _progressTimer = new System.Windows.Forms.Timer();
            _progressTimer.Interval = 800; // 800ms per step
            _progressTimer.Tick += (sender, e) =>
            {
                if (_currentProgress < loadingSteps.Length)
                {
                    _statusLabel.Text = loadingSteps[_currentProgress];
                    _progressBar.Value = (int)((float)(_currentProgress + 1) / loadingSteps.Length * 100);
                    _currentProgress++;
                }
                else
                {
                    _progressTimer.Stop();
                    _progressTimer.Dispose();

                    // Close splash screen after a short delay
                    Task.Delay(500).ContinueWith(_ =>
                    {
                        this.Invoke(new Action(() => this.Close()));
                    });
                }
            };

            _progressTimer.Start();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // Add fade-in effect
            this.Opacity = 0;
            var fadeTimer = new System.Windows.Forms.Timer();
            fadeTimer.Interval = 50;
            fadeTimer.Tick += (sender, e) =>
            {
                if (this.Opacity < 1)
                {
                    this.Opacity += 0.1;
                }
                else
                {
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
            };
            fadeTimer.Start();
        }
    }
}
