import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import './App.css';

// Components
import TitleBar from './components/TitleBar';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import SystemOptimizer from './components/SystemOptimizer';
import CpuOptimization from './components/CpuOptimization';
import GpuOptimization from './components/GpuOptimization';
import MemoryOptimization from './components/MemoryOptimization';
import NetworkOptimization from './components/NetworkOptimization';
import AdvancedTweaks from './components/AdvancedTweaks';
import Debloater from './components/Debloater';
import BiosTweaks from './components/BiosTweaks';
import PerformanceMonitor from './components/PerformanceMonitor';
import Settings from './components/Settings';
import LoadingScreen from './components/LoadingScreen';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [systemInfo, setSystemInfo] = useState(null);
  const [currentPage, setCurrentPage] = useState('dashboard');

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check admin privileges
      const adminStatus = await window.electronAPI.checkAdmin();
      setIsAdmin(adminStatus);

      // Get system information
      const sysInfo = await window.electronAPI.getSystemInfo();
      setSystemInfo(sysInfo);

      // Simulate loading time for smooth UX
      setTimeout(() => {
        setIsLoading(false);
      }, 2000);
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app">
      <TitleBar />

      <div className="app-content">
        <Sidebar
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          isAdmin={isAdmin}
        />

        <main className="main-content">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPage}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="page-container"
            >
              {renderCurrentPage()}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>

      {!isAdmin && (
        <div className="admin-warning">
          <div className="warning-content">
            <div className="warning-icon">⚠️</div>
            <div className="warning-text">
              <strong>Administrator privileges required</strong>
              <p>Some features may be limited without admin rights. Please restart as administrator for full functionality.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  function renderCurrentPage() {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard systemInfo={systemInfo} isAdmin={isAdmin} />;
      case 'optimizer':
        return <SystemOptimizer isAdmin={isAdmin} />;
      case 'cpu':
        return <CpuOptimization isAdmin={isAdmin} systemInfo={systemInfo} />;
      case 'gpu':
        return <GpuOptimization isAdmin={isAdmin} systemInfo={systemInfo} />;
      case 'memory':
        return <MemoryOptimization isAdmin={isAdmin} systemInfo={systemInfo} />;
      case 'network':
        return <NetworkOptimization isAdmin={isAdmin} />;
      case 'bios':
        return <BiosTweaks isAdmin={isAdmin} systemInfo={systemInfo} />;
      case 'debloater':
        return <Debloater isAdmin={isAdmin} />;
      case 'advanced':
        return <AdvancedTweaks isAdmin={isAdmin} />;
      case 'monitor':
        return <PerformanceMonitor systemInfo={systemInfo} />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard systemInfo={systemInfo} isAdmin={isAdmin} />;
    }
  }
}

export default App;
