{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Cpu, HardDrive, Zap, Activity, Shield, Gauge, TrendingUp, CheckCircle, AlertTriangle, Info } from 'lucide-react';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = ({\n  systemInfo,\n  isAdmin\n}) => {\n  _s();\n  var _systemInfo$graphics$;\n  const [performanceScore, setPerformanceScore] = useState(0);\n  const [optimizationStatus, setOptimizationStatus] = useState('checking');\n  const [quickStats, setQuickStats] = useState({\n    fpsBoost: 0,\n    ramFreed: 0,\n    processesOptimized: 0,\n    tweaksApplied: 0\n  });\n  useEffect(() => {\n    // Simulate performance calculation\n    setTimeout(() => {\n      setPerformanceScore(87);\n      setOptimizationStatus('optimized');\n      setQuickStats({\n        fpsBoost: 247,\n        ramFreed: 2.4,\n        processesOptimized: 43,\n        tweaksApplied: 156\n      });\n    }, 1500);\n  }, []);\n  const getPerformanceColor = score => {\n    if (score >= 80) return '#00ff88';\n    if (score >= 60) return '#ffa502';\n    return '#ff4757';\n  };\n  const getOptimizationStatusInfo = () => {\n    switch (optimizationStatus) {\n      case 'checking':\n        return {\n          icon: Activity,\n          color: '#74b9ff',\n          text: 'Analyzing System...'\n        };\n      case 'optimized':\n        return {\n          icon: CheckCircle,\n          color: '#00ff88',\n          text: 'System Optimized'\n        };\n      case 'needs-optimization':\n        return {\n          icon: AlertTriangle,\n          color: '#ffa502',\n          text: 'Optimization Recommended'\n        };\n      default:\n        return {\n          icon: Info,\n          color: '#888',\n          text: 'Unknown Status'\n        };\n    }\n  };\n  const statusInfo = getOptimizationStatusInfo();\n  const StatusIcon = statusInfo.icon;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"System Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Real-time overview of your system's performance and optimization status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card performance-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Performance Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Gauge, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"performance-score\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"score-circle\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 100 100\",\n              className: \"score-svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"45\",\n                fill: \"none\",\n                stroke: \"rgba(255,255,255,0.1)\",\n                strokeWidth: \"8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"45\",\n                fill: \"none\",\n                stroke: getPerformanceColor(performanceScore),\n                strokeWidth: \"8\",\n                strokeLinecap: \"round\",\n                strokeDasharray: `${performanceScore * 2.83} 283`,\n                transform: \"rotate(-90 50 50)\",\n                className: \"score-progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"score-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"score-number\",\n                children: performanceScore\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"score-label\",\n                children: \"/ 100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"score-status\",\n            children: [/*#__PURE__*/_jsxDEV(StatusIcon, {\n              size: 16,\n              style: {\n                color: statusInfo.color\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: statusInfo.color\n              },\n              children: statusInfo.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card system-info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"System Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Cpu, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"system-info\",\n          children: systemInfo ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"CPU:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: systemInfo.cpu.brand\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Cores:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: [systemInfo.cpu.cores, \" (\", systemInfo.cpu.physicalCores, \" physical)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Memory:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: [(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1), \" GB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"GPU:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: ((_systemInfo$graphics$ = systemInfo.graphics[0]) === null || _systemInfo$graphics$ === void 0 ? void 0 : _systemInfo$graphics$.model) || 'Unknown'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"OS:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: systemInfo.os.distro\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-info\",\n            children: \"Loading system information...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card stats-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Optimization Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon fps\",\n              children: /*#__PURE__*/_jsxDEV(Zap, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: [\"+\", quickStats.fpsBoost, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: \"FPS Boost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon ram\",\n              children: /*#__PURE__*/_jsxDEV(HardDrive, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: [quickStats.ramFreed, \" GB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: \"RAM Freed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon processes\",\n              children: /*#__PURE__*/_jsxDEV(Activity, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: quickStats.processesOptimized\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: \"Processes Optimized\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-icon tweaks\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-value\",\n                children: quickStats.tweaksApplied\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-label\",\n                children: \"Tweaks Applied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card actions-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Zap, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn primary\",\n            disabled: !isAdmin,\n            children: [/*#__PURE__*/_jsxDEV(Zap, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Quick Optimize\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Activity, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Performance Monitor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn secondary\",\n            disabled: !isAdmin,\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Advanced Tweaks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn danger\",\n            disabled: !isAdmin,\n            children: [/*#__PURE__*/_jsxDEV(HardDrive, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"System Cleanup\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-notice\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Limited Mode Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Some features require administrator privileges. Restart the application as administrator to unlock all optimization features.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"Yhy+jmvj2Vdq1Gc5cmH1j7z8s+s=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Cpu", "HardDrive", "Zap", "Activity", "Shield", "Gauge", "TrendingUp", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Info", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "systemInfo", "isAdmin", "_s", "_systemInfo$graphics$", "performanceScore", "setPerformanceScore", "optimizationStatus", "setOptimizationStatus", "quickStats", "setQuickStats", "fpsBoost", "ram<PERSON><PERSON>", "processesOptimized", "tweaksApplied", "setTimeout", "getPerformanceColor", "score", "getOptimizationStatusInfo", "icon", "color", "text", "statusInfo", "StatusIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "viewBox", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "strokeLinecap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "style", "cpu", "brand", "cores", "physicalCores", "memory", "total", "toFixed", "graphics", "model", "os", "distro", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Cpu, \n  HardDrive, \n  Zap, \n  Activity, \n  Shield, \n  Gauge,\n  TrendingUp,\n  CheckCircle,\n  AlertTriangle,\n  Info\n} from 'lucide-react';\nimport './Dashboard.css';\n\nconst Dashboard = ({ systemInfo, isAdmin }) => {\n  const [performanceScore, setPerformanceScore] = useState(0);\n  const [optimizationStatus, setOptimizationStatus] = useState('checking');\n  const [quickStats, setQuickStats] = useState({\n    fpsBoost: 0,\n    ramFreed: 0,\n    processesOptimized: 0,\n    tweaksApplied: 0\n  });\n\n  useEffect(() => {\n    // Simulate performance calculation\n    setTimeout(() => {\n      setPerformanceScore(87);\n      setOptimizationStatus('optimized');\n      setQuickStats({\n        fpsBoost: 247,\n        ramFreed: 2.4,\n        processesOptimized: 43,\n        tweaksApplied: 156\n      });\n    }, 1500);\n  }, []);\n\n  const getPerformanceColor = (score) => {\n    if (score >= 80) return '#00ff88';\n    if (score >= 60) return '#ffa502';\n    return '#ff4757';\n  };\n\n  const getOptimizationStatusInfo = () => {\n    switch (optimizationStatus) {\n      case 'checking':\n        return { icon: Activity, color: '#74b9ff', text: 'Analyzing System...' };\n      case 'optimized':\n        return { icon: CheckCircle, color: '#00ff88', text: 'System Optimized' };\n      case 'needs-optimization':\n        return { icon: AlertTriangle, color: '#ffa502', text: 'Optimization Recommended' };\n      default:\n        return { icon: Info, color: '#888', text: 'Unknown Status' };\n    }\n  };\n\n  const statusInfo = getOptimizationStatusInfo();\n  const StatusIcon = statusInfo.icon;\n\n  return (\n    <div className=\"dashboard\">\n      <div className=\"dashboard-header\">\n        <h1>System Dashboard</h1>\n        <p>Real-time overview of your system's performance and optimization status</p>\n      </div>\n\n      <div className=\"dashboard-grid\">\n        {/* Performance Score */}\n        <div className=\"card performance-card\">\n          <div className=\"card-header\">\n            <h3>Performance Score</h3>\n            <Gauge size={20} />\n          </div>\n          <div className=\"performance-score\">\n            <div className=\"score-circle\">\n              <svg viewBox=\"0 0 100 100\" className=\"score-svg\">\n                <circle\n                  cx=\"50\"\n                  cy=\"50\"\n                  r=\"45\"\n                  fill=\"none\"\n                  stroke=\"rgba(255,255,255,0.1)\"\n                  strokeWidth=\"8\"\n                />\n                <circle\n                  cx=\"50\"\n                  cy=\"50\"\n                  r=\"45\"\n                  fill=\"none\"\n                  stroke={getPerformanceColor(performanceScore)}\n                  strokeWidth=\"8\"\n                  strokeLinecap=\"round\"\n                  strokeDasharray={`${performanceScore * 2.83} 283`}\n                  transform=\"rotate(-90 50 50)\"\n                  className=\"score-progress\"\n                />\n              </svg>\n              <div className=\"score-text\">\n                <span className=\"score-number\">{performanceScore}</span>\n                <span className=\"score-label\">/ 100</span>\n              </div>\n            </div>\n            <div className=\"score-status\">\n              <StatusIcon size={16} style={{ color: statusInfo.color }} />\n              <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* System Information */}\n        <div className=\"card system-info-card\">\n          <div className=\"card-header\">\n            <h3>System Information</h3>\n            <Cpu size={20} />\n          </div>\n          <div className=\"system-info\">\n            {systemInfo ? (\n              <>\n                <div className=\"info-item\">\n                  <span className=\"info-label\">CPU:</span>\n                  <span className=\"info-value\">{systemInfo.cpu.brand}</span>\n                </div>\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Cores:</span>\n                  <span className=\"info-value\">{systemInfo.cpu.cores} ({systemInfo.cpu.physicalCores} physical)</span>\n                </div>\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Memory:</span>\n                  <span className=\"info-value\">{(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB</span>\n                </div>\n                <div className=\"info-item\">\n                  <span className=\"info-label\">GPU:</span>\n                  <span className=\"info-value\">{systemInfo.graphics[0]?.model || 'Unknown'}</span>\n                </div>\n                <div className=\"info-item\">\n                  <span className=\"info-label\">OS:</span>\n                  <span className=\"info-value\">{systemInfo.os.distro}</span>\n                </div>\n              </>\n            ) : (\n              <div className=\"loading-info\">Loading system information...</div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"card stats-card\">\n          <div className=\"card-header\">\n            <h3>Optimization Results</h3>\n            <TrendingUp size={20} />\n          </div>\n          <div className=\"quick-stats\">\n            <div className=\"stat-item\">\n              <div className=\"stat-icon fps\">\n                <Zap size={16} />\n              </div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">+{quickStats.fpsBoost}%</div>\n                <div className=\"stat-label\">FPS Boost</div>\n              </div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-icon ram\">\n                <HardDrive size={16} />\n              </div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">{quickStats.ramFreed} GB</div>\n                <div className=\"stat-label\">RAM Freed</div>\n              </div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-icon processes\">\n                <Activity size={16} />\n              </div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">{quickStats.processesOptimized}</div>\n                <div className=\"stat-label\">Processes Optimized</div>\n              </div>\n            </div>\n            <div className=\"stat-item\">\n              <div className=\"stat-icon tweaks\">\n                <Shield size={16} />\n              </div>\n              <div className=\"stat-content\">\n                <div className=\"stat-value\">{quickStats.tweaksApplied}</div>\n                <div className=\"stat-label\">Tweaks Applied</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"card actions-card\">\n          <div className=\"card-header\">\n            <h3>Quick Actions</h3>\n            <Zap size={20} />\n          </div>\n          <div className=\"quick-actions\">\n            <button className=\"action-btn primary\" disabled={!isAdmin}>\n              <Zap size={16} />\n              <span>Quick Optimize</span>\n            </button>\n            <button className=\"action-btn secondary\">\n              <Activity size={16} />\n              <span>Performance Monitor</span>\n            </button>\n            <button className=\"action-btn secondary\" disabled={!isAdmin}>\n              <Shield size={16} />\n              <span>Advanced Tweaks</span>\n            </button>\n            <button className=\"action-btn danger\" disabled={!isAdmin}>\n              <HardDrive size={16} />\n              <span>System Cleanup</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {!isAdmin && (\n        <div className=\"admin-notice\">\n          <AlertTriangle size={20} />\n          <div>\n            <strong>Limited Mode Active</strong>\n            <p>Some features require administrator privileges. Restart the application as administrator to unlock all optimization features.</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,GAAG,EACHC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,IAAI,QACC,cAAc;AACrB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC7C,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACuB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC;IAC3C2B,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,kBAAkB,EAAE,CAAC;IACrBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF7B,SAAS,CAAC,MAAM;IACd;IACA8B,UAAU,CAAC,MAAM;MACfT,mBAAmB,CAAC,EAAE,CAAC;MACvBE,qBAAqB,CAAC,WAAW,CAAC;MAClCE,aAAa,CAAC;QACZC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE,GAAG;QACbC,kBAAkB,EAAE,EAAE;QACtBC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,mBAAmB,GAAIC,KAAK,IAAK;IACrC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;IACjC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;IACjC,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,QAAQX,kBAAkB;MACxB,KAAK,UAAU;QACb,OAAO;UAAEY,IAAI,EAAE9B,QAAQ;UAAE+B,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAsB,CAAC;MAC1E,KAAK,WAAW;QACd,OAAO;UAAEF,IAAI,EAAE1B,WAAW;UAAE2B,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAmB,CAAC;MAC1E,KAAK,oBAAoB;QACvB,OAAO;UAAEF,IAAI,EAAEzB,aAAa;UAAE0B,KAAK,EAAE,SAAS;UAAEC,IAAI,EAAE;QAA2B,CAAC;MACpF;QACE,OAAO;UAAEF,IAAI,EAAExB,IAAI;UAAEyB,KAAK,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAiB,CAAC;IAChE;EACF,CAAC;EAED,MAAMC,UAAU,GAAGJ,yBAAyB,CAAC,CAAC;EAC9C,MAAMK,UAAU,GAAGD,UAAU,CAACH,IAAI;EAElC,oBACEtB,OAAA;IAAK2B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5B,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B5B,OAAA;QAAA4B,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBhC,OAAA;QAAA4B,QAAA,EAAG;MAAuE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eAENhC,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7B5B,OAAA;QAAK2B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAA4B,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BhC,OAAA,CAACN,KAAK;YAACuC,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5B,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5B,OAAA;cAAKkC,OAAO,EAAC,aAAa;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAC9C5B,OAAA;gBACEmC,EAAE,EAAC,IAAI;gBACPC,EAAE,EAAC,IAAI;gBACPC,CAAC,EAAC,IAAI;gBACNC,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,uBAAuB;gBAC9BC,WAAW,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACFhC,OAAA;gBACEmC,EAAE,EAAC,IAAI;gBACPC,EAAE,EAAC,IAAI;gBACPC,CAAC,EAAC,IAAI;gBACNC,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAEpB,mBAAmB,CAACX,gBAAgB,CAAE;gBAC9CgC,WAAW,EAAC,GAAG;gBACfC,aAAa,EAAC,OAAO;gBACrBC,eAAe,EAAE,GAAGlC,gBAAgB,GAAG,IAAI,MAAO;gBAClDmC,SAAS,EAAC,mBAAmB;gBAC7BhB,SAAS,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5B,OAAA;gBAAM2B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEpB;cAAgB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxDhC,OAAA;gBAAM2B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5B,OAAA,CAAC0B,UAAU;cAACO,IAAI,EAAE,EAAG;cAACW,KAAK,EAAE;gBAAErB,KAAK,EAAEE,UAAU,CAACF;cAAM;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DhC,OAAA;cAAM4C,KAAK,EAAE;gBAAErB,KAAK,EAAEE,UAAU,CAACF;cAAM,CAAE;cAAAK,QAAA,EAAEH,UAAU,CAACD;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAA4B,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhC,OAAA,CAACX,GAAG;YAAC4C,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBxB,UAAU,gBACTJ,OAAA,CAAAE,SAAA;YAAA0B,QAAA,gBACE5B,OAAA;cAAK2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5B,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChC,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAExB,UAAU,CAACyC,GAAG,CAACC;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5B,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ChC,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAExB,UAAU,CAACyC,GAAG,CAACE,KAAK,EAAC,IAAE,EAAC3C,UAAU,CAACyC,GAAG,CAACG,aAAa,EAAC,YAAU;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5B,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3ChC,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAE,CAACxB,UAAU,CAAC6C,MAAM,CAACC,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5B,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChC,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE,EAAArB,qBAAA,GAAAH,UAAU,CAACgD,QAAQ,CAAC,CAAC,CAAC,cAAA7C,qBAAA,uBAAtBA,qBAAA,CAAwB8C,KAAK,KAAI;cAAS;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5B,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvChC,OAAA;gBAAM2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAExB,UAAU,CAACkD,EAAE,CAACC;cAAM;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA,eACN,CAAC,gBAEHhC,OAAA;YAAK2B,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACjE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAA4B,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BhC,OAAA,CAACL,UAAU;YAACsC,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAK2B,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B5B,OAAA,CAACT,GAAG;gBAAC0C,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAAChB,UAAU,CAACE,QAAQ,EAAC,GAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDhC,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAK2B,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B5B,OAAA,CAACV,SAAS;gBAAC2C,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAEhB,UAAU,CAACG,QAAQ,EAAC,KAAG;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DhC,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAK2B,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClC5B,OAAA,CAACR,QAAQ;gBAACyC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEhB,UAAU,CAACI;cAAkB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjEhC,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5B,OAAA;cAAK2B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B5B,OAAA,CAACP,MAAM;gBAACwC,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACNhC,OAAA;cAAK2B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5B,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEhB,UAAU,CAACK;cAAa;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DhC,OAAA;gBAAK2B,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAA4B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBhC,OAAA,CAACT,GAAG;YAAC0C,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNhC,OAAA;UAAK2B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5B,OAAA;YAAQ2B,SAAS,EAAC,oBAAoB;YAAC6B,QAAQ,EAAE,CAACnD,OAAQ;YAAAuB,QAAA,gBACxD5B,OAAA,CAACT,GAAG;cAAC0C,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjBhC,OAAA;cAAA4B,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACThC,OAAA;YAAQ2B,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACtC5B,OAAA,CAACR,QAAQ;cAACyC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtBhC,OAAA;cAAA4B,QAAA,EAAM;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACThC,OAAA;YAAQ2B,SAAS,EAAC,sBAAsB;YAAC6B,QAAQ,EAAE,CAACnD,OAAQ;YAAAuB,QAAA,gBAC1D5B,OAAA,CAACP,MAAM;cAACwC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBhC,OAAA;cAAA4B,QAAA,EAAM;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACThC,OAAA;YAAQ2B,SAAS,EAAC,mBAAmB;YAAC6B,QAAQ,EAAE,CAACnD,OAAQ;YAAAuB,QAAA,gBACvD5B,OAAA,CAACV,SAAS;cAAC2C,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBhC,OAAA;cAAA4B,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAAC3B,OAAO,iBACPL,OAAA;MAAK2B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B5B,OAAA,CAACH,aAAa;QAACoC,IAAI,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BhC,OAAA;QAAA4B,QAAA,gBACE5B,OAAA;UAAA4B,QAAA,EAAQ;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpChC,OAAA;UAAA4B,QAAA,EAAG;QAA6H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAxNIH,SAAS;AAAAsD,EAAA,GAATtD,SAAS;AA0Nf,eAAeA,SAAS;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}