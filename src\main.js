const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    frame: false,
    titleBarStyle: 'hidden',
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false,
    backgroundColor: '#0a0a0a'
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC Handlers for system operations
ipcMain.handle('get-system-info', async () => {
  const si = require('systeminformation');
  try {
    const [cpu, mem, graphics, osInfo] = await Promise.all([
      si.cpu(),
      si.mem(),
      si.graphics(),
      si.osInfo()
    ]);

    return {
      cpu: {
        manufacturer: cpu.manufacturer,
        brand: cpu.brand,
        cores: cpu.cores,
        physicalCores: cpu.physicalCores,
        speed: cpu.speed
      },
      memory: {
        total: mem.total,
        free: mem.free,
        used: mem.used
      },
      graphics: graphics.controllers.map(gpu => ({
        model: gpu.model,
        vendor: gpu.vendor,
        vram: gpu.vram
      })),
      os: {
        platform: osInfo.platform,
        distro: osInfo.distro,
        release: osInfo.release,
        arch: osInfo.arch
      }
    };
  } catch (error) {
    console.error('Error getting system info:', error);
    return null;
  }
});

// Registry operations
ipcMain.handle('read-registry', async (event, keyPath, valueName) => {
  const Registry = require('winreg');
  return new Promise((resolve, reject) => {
    const regKey = new Registry({
      hive: Registry.HKLM,
      key: keyPath
    });

    regKey.get(valueName, (err, item) => {
      if (err) {
        reject(err);
      } else {
        resolve(item ? item.value : null);
      }
    });
  });
});

ipcMain.handle('write-registry', async (event, keyPath, valueName, value, type = 'REG_DWORD') => {
  const Registry = require('winreg');
  return new Promise((resolve, reject) => {
    const regKey = new Registry({
      hive: Registry.HKLM,
      key: keyPath
    });

    regKey.set(valueName, type, value, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve(true);
      }
    });
  });
});

// Service management
ipcMain.handle('get-services', async () => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', 'Get-Service | Select-Object Name, Status, StartType | ConvertTo-Json'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    ps.stdout.on('data', (data) => {
      output += data.toString();
    });

    ps.on('close', (code) => {
      try {
        const services = JSON.parse(output);
        resolve(services);
      } catch (error) {
        reject(error);
      }
    });
  });
});

ipcMain.handle('set-service-startup', async (event, serviceName, startupType) => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', `Set-Service -Name "${serviceName}" -StartupType ${startupType}`], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    ps.on('close', (code) => {
      if (code === 0) {
        resolve(true);
      } else {
        reject(new Error(`Failed to set service startup type. Exit code: ${code}`));
      }
    });
  });
});

ipcMain.handle('execute-powershell', async (event, script) => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', script], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let error = '';

    ps.stdout.on('data', (data) => {
      output += data.toString();
    });

    ps.stderr.on('data', (data) => {
      error += data.toString();
    });

    ps.on('close', (code) => {
      if (code === 0) {
        resolve(output);
      } else {
        reject(error);
      }
    });
  });
});

ipcMain.handle('check-admin', async () => {
  try {
    const result = await new Promise((resolve, reject) => {
      const ps = spawn('powershell.exe', ['-Command', '([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      ps.stdout.on('data', (data) => {
        output += data.toString();
      });

      ps.on('close', (code) => {
        resolve(output.trim() === 'True');
      });
    });

    return result;
  } catch (error) {
    return false;
  }
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Process management
ipcMain.handle('get-processes', async () => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', 'Get-Process | Select-Object Name, CPU, WorkingSet, Id | ConvertTo-Json'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    ps.stdout.on('data', (data) => {
      output += data.toString();
    });

    ps.on('close', (code) => {
      try {
        const processes = JSON.parse(output);
        resolve(processes);
      } catch (error) {
        reject(error);
      }
    });
  });
});

ipcMain.handle('kill-process', async (event, processName) => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', `Stop-Process -Name "${processName}" -Force`], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    ps.on('close', (code) => {
      if (code === 0) {
        resolve(true);
      } else {
        reject(new Error(`Failed to kill process. Exit code: ${code}`));
      }
    });
  });
});

// System optimization functions
ipcMain.handle('apply-gaming-tweaks', async () => {
  const tweaks = [
    // Disable Game Bar
    'Set-ItemProperty -Path "HKCU:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR" -Name "AppCaptureEnabled" -Value 0',
    // Disable Fullscreen Optimizations
    'Set-ItemProperty -Path "HKCU:\\System\\GameConfigStore" -Name "GameDVR_Enabled" -Value 0',
    // Enable Game Mode
    'Set-ItemProperty -Path "HKCU:\\SOFTWARE\\Microsoft\\GameBar" -Name "AutoGameModeEnabled" -Value 1',
    // Set High Performance Power Plan
    'powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'
  ];

  for (const tweak of tweaks) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', tweak], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          if (code === 0) {
            resolve(true);
          } else {
            reject(new Error(`Failed to apply tweak: ${tweak}`));
          }
        });
      });
    } catch (error) {
      console.error('Error applying tweak:', error);
    }
  }

  return true;
});

// CPU Optimization
ipcMain.handle('optimize-cpu', async () => {
  const cpuTweaks = [
    // Disable CPU Core Parking
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583" -Name "ValueMax" -Value 0',
    // Set CPU Priority
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl" -Name "Win32PrioritySeparation" -Value 38',
    // Disable Superfetch
    'Set-Service -Name "SysMain" -StartupType Disabled'
  ];

  for (const tweak of cpuTweaks) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', tweak], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          resolve(code === 0);
        });
      });
    } catch (error) {
      console.error('Error applying CPU tweak:', error);
    }
  }

  return true;
});

// GPU Optimization
ipcMain.handle('optimize-gpu', async () => {
  const gpuTweaks = [
    // Enable Hardware Accelerated GPU Scheduling
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers" -Name "HwSchMode" -Value 2',
    // Disable GPU Power Saving
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000" -Name "PowerMizerEnable" -Value 0'
  ];

  for (const tweak of gpuTweaks) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', tweak], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          resolve(code === 0);
        });
      });
    } catch (error) {
      console.error('Error applying GPU tweak:', error);
    }
  }

  return true;
});

// Memory Optimization
ipcMain.handle('optimize-memory', async () => {
  const memoryTweaks = [
    // Optimize System Cache
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" -Name "LargeSystemCache" -Value 1',
    // Disable Pagefile Clearing
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management" -Name "ClearPageFileAtShutdown" -Value 0'
  ];

  for (const tweak of memoryTweaks) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', tweak], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          resolve(code === 0);
        });
      });
    } catch (error) {
      console.error('Error applying memory tweak:', error);
    }
  }

  return true;
});

// Network Optimization
ipcMain.handle('optimize-network', async () => {
  const networkTweaks = [
    // Enable TCP Chimney Offload
    'Set-ItemProperty -Path "HKLM:\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters" -Name "EnableTCPChimney" -Value 1',
    // Disable Nagle Algorithm
    'netsh int tcp set global autotuninglevel=disabled'
  ];

  for (const tweak of networkTweaks) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', tweak], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          resolve(code === 0);
        });
      });
    } catch (error) {
      console.error('Error applying network tweak:', error);
    }
  }

  return true;
});

// System Cleanup
ipcMain.handle('clean-system', async () => {
  const cleanupCommands = [
    // Clean temp files
    'Remove-Item -Path "$env:TEMP\\*" -Recurse -Force -ErrorAction SilentlyContinue',
    // Clean Windows temp
    'Remove-Item -Path "C:\\Windows\\Temp\\*" -Recurse -Force -ErrorAction SilentlyContinue',
    // Clean prefetch
    'Remove-Item -Path "C:\\Windows\\Prefetch\\*" -Force -ErrorAction SilentlyContinue',
    // Run disk cleanup
    'cleanmgr /sagerun:1'
  ];

  for (const command of cleanupCommands) {
    try {
      await new Promise((resolve, reject) => {
        const ps = spawn('powershell.exe', ['-Command', command], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ps.on('close', (code) => {
          resolve(true);
        });
      });
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  return true;
});

// Create System Restore Point
ipcMain.handle('create-backup', async (event, backupName) => {
  return new Promise((resolve, reject) => {
    const ps = spawn('powershell.exe', ['-Command', `Checkpoint-Computer -Description "${backupName}" -RestorePointType "MODIFY_SETTINGS"`], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    ps.on('close', (code) => {
      if (code === 0) {
        resolve(true);
      } else {
        reject(new Error('Failed to create restore point'));
      }
    });
  });
});
