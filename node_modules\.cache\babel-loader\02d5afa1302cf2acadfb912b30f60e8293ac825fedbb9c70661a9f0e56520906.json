{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\GpuOptimization.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Monitor, Zap, Thermometer, Activity, Settings, Play, AlertTriangle, CheckCircle, TrendingUp, BarChart3, Gauge } from 'lucide-react';\nimport './GpuOptimization.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GpuOptimization = ({\n  isAdmin,\n  systemInfo\n}) => {\n  _s();\n  var _systemInfo$graphics;\n  const [gpuTweaks, setGpuTweaks] = useState({\n    powerLimit: false,\n    memoryOverclock: false,\n    coreOverclock: false,\n    fanCurve: false,\n    msiMode: false,\n    preRenderedFrames: false,\n    powerManagement: false,\n    textureFiltering: false,\n    vsync: false,\n    gsync: false,\n    hags: false,\n    gameMode: false\n  });\n  const [gpuMetrics, setGpuMetrics] = useState({\n    temperature: 42,\n    usage: 8,\n    memoryUsage: 15,\n    coreClock: 1920,\n    memoryClock: 7000,\n    powerDraw: 85,\n    fanSpeed: 35\n  });\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n  const gpuOptimizations = [{\n    id: 'powerLimit',\n    title: 'Increase Power Limit',\n    description: 'Raise GPU power limit to maximum for sustained performance',\n    impact: 'Very High',\n    category: 'Power Management',\n    riskLevel: 'Medium',\n    estimatedGain: '+20-40% performance',\n    fpsGain: '+15-30 FPS'\n  }, {\n    id: 'coreOverclock',\n    title: 'GPU Core Overclock',\n    description: 'Safely overclock GPU core for increased performance',\n    impact: 'Very High',\n    category: 'Overclocking',\n    riskLevel: 'High',\n    estimatedGain: '+15-25% performance',\n    fpsGain: '+20-35 FPS'\n  }, {\n    id: 'memoryOverclock',\n    title: 'Memory Overclock',\n    description: 'Overclock VRAM for better texture streaming and bandwidth',\n    impact: 'High',\n    category: 'Memory',\n    riskLevel: 'High',\n    estimatedGain: '+10-20% in memory-bound games',\n    fpsGain: '+10-25 FPS'\n  }, {\n    id: 'msiMode',\n    title: 'Enable MSI Mode',\n    description: 'Enable Message Signaled Interrupts for reduced latency',\n    impact: 'High',\n    category: 'Latency',\n    riskLevel: 'Low',\n    estimatedGain: '+5-15% latency reduction',\n    fpsGain: '+5-10 FPS'\n  }, {\n    id: 'preRenderedFrames',\n    title: 'Optimize Pre-rendered Frames',\n    description: 'Set optimal pre-rendered frames for reduced input lag',\n    impact: 'Medium',\n    category: 'Latency',\n    riskLevel: 'Low',\n    estimatedGain: 'Reduced input lag',\n    fpsGain: '+2-8 FPS'\n  }, {\n    id: 'powerManagement',\n    title: 'Maximum Performance Mode',\n    description: 'Set GPU to prefer maximum performance over power saving',\n    impact: 'High',\n    category: 'Power',\n    riskLevel: 'Low',\n    estimatedGain: '+10-20% consistent performance',\n    fpsGain: '+8-15 FPS'\n  }, {\n    id: 'textureFiltering',\n    title: 'Optimize Texture Filtering',\n    description: 'Set texture filtering to performance mode',\n    impact: 'Medium',\n    category: 'Graphics',\n    riskLevel: 'Low',\n    estimatedGain: '+5-10% in texture-heavy games',\n    fpsGain: '+3-12 FPS'\n  }, {\n    id: 'hags',\n    title: 'Hardware Accelerated GPU Scheduling',\n    description: 'Enable HAGS for reduced CPU overhead and better frame pacing',\n    impact: 'Medium',\n    category: 'Scheduling',\n    riskLevel: 'Low',\n    estimatedGain: '+3-8% CPU-bound scenarios',\n    fpsGain: '+5-15 FPS'\n  }, {\n    id: 'gameMode',\n    title: 'Windows Game Mode',\n    description: 'Enable Windows Game Mode for gaming optimizations',\n    impact: 'Medium',\n    category: 'System',\n    riskLevel: 'Low',\n    estimatedGain: '+2-5% overall gaming performance',\n    fpsGain: '+2-8 FPS'\n  }, {\n    id: 'fanCurve',\n    title: 'Aggressive Fan Curve',\n    description: 'Set aggressive fan curve to maintain lower temperatures',\n    impact: 'Medium',\n    category: 'Cooling',\n    riskLevel: 'Low',\n    estimatedGain: 'Better thermal throttling prevention',\n    fpsGain: '+5-15 FPS (thermal limited)'\n  }];\n  const handleTweakToggle = tweakId => {\n    if (!isAdmin) return;\n    setGpuTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n  const applyOptimizations = async () => {\n    if (!isAdmin) return;\n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n    const enabledTweaks = Object.entries(gpuTweaks).filter(([_, enabled]) => enabled).map(([id, _]) => id);\n    for (let i = 0; i < enabledTweaks.length; i++) {\n      const tweakId = enabledTweaks[i];\n      try {\n        // Simulate applying tweak\n        await new Promise(resolve => setTimeout(resolve, 1200));\n\n        // Here you would call the actual optimization functions\n        // await window.electronAPI.applyGpuTweak(tweakId);\n\n        setOptimizationProgress((i + 1) / enabledTweaks.length * 100);\n      } catch (error) {\n        console.error(`Failed to apply ${tweakId}:`, error);\n      }\n    }\n    setIsOptimizing(false);\n\n    // Calculate estimated FPS gain\n    const totalFpsGain = enabledTweaks.reduce((total, tweakId) => {\n      var _tweak$fpsGain;\n      const tweak = gpuOptimizations.find(t => t.id === tweakId);\n      const fpsRange = tweak === null || tweak === void 0 ? void 0 : (_tweak$fpsGain = tweak.fpsGain) === null || _tweak$fpsGain === void 0 ? void 0 : _tweak$fpsGain.match(/\\+(\\d+)-(\\d+)/);\n      if (fpsRange) {\n        const avgGain = (parseInt(fpsRange[1]) + parseInt(fpsRange[2])) / 2;\n        return total + avgGain;\n      }\n      return total;\n    }, 0);\n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'GPU Optimization Complete',\n      message: `Successfully applied ${enabledTweaks.length} GPU optimizations!\\nEstimated FPS gain: +${Math.round(totalFpsGain)} FPS`,\n      buttons: ['OK']\n    });\n  };\n  const getRiskColor = risk => {\n    switch (risk) {\n      case 'Low':\n        return '#00ff88';\n      case 'Medium':\n        return '#ffa502';\n      case 'High':\n        return '#ff4757';\n      default:\n        return '#888';\n    }\n  };\n  const getImpactColor = impact => {\n    switch (impact) {\n      case 'Very High':\n        return '#ff6b35';\n      case 'High':\n        return '#00ff88';\n      case 'Medium':\n        return '#74b9ff';\n      case 'Low':\n        return '#888';\n      default:\n        return '#888';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gpu-optimization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-icon\",\n          children: /*#__PURE__*/_jsxDEV(Monitor, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"GPU Optimization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Graphics card tweaks and overclocking for maximum FPS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), (systemInfo === null || systemInfo === void 0 ? void 0 : (_systemInfo$graphics = systemInfo.graphics) === null || _systemInfo$graphics === void 0 ? void 0 : _systemInfo$graphics[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpu-info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: systemInfo.graphics[0].model\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpu-specs\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: systemInfo.graphics[0].vendor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), systemInfo.graphics[0].vram && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(systemInfo.graphics[0].vram / 1024), \" GB VRAM\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gpu-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Real-time GPU Metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metrics-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon temp\",\n              children: /*#__PURE__*/_jsxDEV(Thermometer, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.temperature, \"\\xB0C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Temperature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon usage\",\n              children: /*#__PURE__*/_jsxDEV(Activity, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.usage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"GPU Usage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon memory\",\n              children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.memoryUsage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"VRAM Usage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon clock\",\n              children: /*#__PURE__*/_jsxDEV(Gauge, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.coreClock, \" MHz\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Core Clock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon power\",\n              children: /*#__PURE__*/_jsxDEV(Zap, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.powerDraw, \"W\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Power Draw\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon fan\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [gpuMetrics.fanSpeed, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Fan Speed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"optimizations-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"GPU Optimizations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"apply-btn\",\n            onClick: applyOptimizations,\n            disabled: !isAdmin || isOptimizing || Object.values(gpuTweaks).every(v => !v),\n            children: isOptimizing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                size: 16,\n                className: \"spinning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), \"Optimizing... \", Math.round(optimizationProgress), \"%\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), \"Apply Selected Tweaks\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tweaks-grid\",\n          children: gpuOptimizations.map(tweak => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `tweak-card ${gpuTweaks[tweak.id] ? 'enabled' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tweak-title\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: tweak.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tweak-badges\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"impact-badge\",\n                    style: {\n                      backgroundColor: getImpactColor(tweak.impact)\n                    },\n                    children: [tweak.impact, \" Impact\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"risk-badge\",\n                    style: {\n                      backgroundColor: getRiskColor(tweak.riskLevel)\n                    },\n                    children: [tweak.riskLevel, \" Risk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: gpuTweaks[tweak.id],\n                  onChange: () => handleTweakToggle(tweak.id),\n                  disabled: !isAdmin\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"tweak-description\",\n              children: tweak.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-label\",\n                  children: \"Category:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-value\",\n                  children: tweak.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-label\",\n                  children: \"Performance Gain:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-value gain\",\n                  children: tweak.estimatedGain\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-label\",\n                  children: \"FPS Boost:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-value fps-gain\",\n                  children: tweak.fpsGain\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, tweak.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-required\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Administrator privileges required for GPU optimizations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(GpuOptimization, \"SwV6uoABRtEo4QS8LM1vCByV+NI=\");\n_c = GpuOptimization;\nexport default GpuOptimization;\nvar _c;\n$RefreshReg$(_c, \"GpuOptimization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Monitor", "Zap", "Thermometer", "Activity", "Settings", "Play", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "TrendingUp", "BarChart3", "Gauge", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GpuOptimization", "isAdmin", "systemInfo", "_s", "_systemInfo$graphics", "gpuTweaks", "setGpuTweaks", "powerLimit", "memoryOverclock", "coreOverclock", "fanCurve", "msiMode", "preRenderedFrames", "powerManagement", "textureFiltering", "vsync", "gsync", "hags", "gameMode", "gpuMetrics", "setGpuMetrics", "temperature", "usage", "memoryUsage", "coreClock", "memoryClock", "powerDraw", "fanSpeed", "isOptimizing", "setIsOptimizing", "optimizationProgress", "setOptimizationProgress", "gpuOptimizations", "id", "title", "description", "impact", "category", "riskLevel", "estimatedGain", "fpsGain", "handleTweakToggle", "tweakId", "prev", "applyOptimizations", "enabledTweaks", "Object", "entries", "filter", "_", "enabled", "map", "i", "length", "Promise", "resolve", "setTimeout", "error", "console", "totalFpsGain", "reduce", "total", "_tweak$fpsGain", "tweak", "find", "t", "fpsRange", "match", "avgGain", "parseInt", "window", "electronAPI", "showMessageBox", "type", "message", "Math", "round", "buttons", "getRiskColor", "risk", "getImpactColor", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "graphics", "model", "vendor", "vram", "onClick", "disabled", "values", "every", "v", "style", "backgroundColor", "checked", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/GpuOptimization.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Monitor, \n  Zap, \n  Thermometer, \n  Activity, \n  Settings, \n  Play, \n  AlertTriangle,\n  CheckCircle,\n  TrendingUp,\n  BarChart3,\n  Gauge\n} from 'lucide-react';\nimport './GpuOptimization.css';\n\nconst GpuOptimization = ({ isAdmin, systemInfo }) => {\n  const [gpuTweaks, setGpuTweaks] = useState({\n    powerLimit: false,\n    memoryOverclock: false,\n    coreOverclock: false,\n    fanCurve: false,\n    msiMode: false,\n    preRenderedFrames: false,\n    powerManagement: false,\n    textureFiltering: false,\n    vsync: false,\n    gsync: false,\n    hags: false,\n    gameMode: false\n  });\n\n  const [gpuMetrics, setGpuMetrics] = useState({\n    temperature: 42,\n    usage: 8,\n    memoryUsage: 15,\n    coreClock: 1920,\n    memoryClock: 7000,\n    powerDraw: 85,\n    fanSpeed: 35\n  });\n\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n\n  const gpuOptimizations = [\n    {\n      id: 'powerLimit',\n      title: 'Increase Power Limit',\n      description: 'Raise GPU power limit to maximum for sustained performance',\n      impact: 'Very High',\n      category: 'Power Management',\n      riskLevel: 'Medium',\n      estimatedGain: '+20-40% performance',\n      fpsGain: '+15-30 FPS'\n    },\n    {\n      id: 'coreOverclock',\n      title: 'GPU Core Overclock',\n      description: 'Safely overclock GPU core for increased performance',\n      impact: 'Very High',\n      category: 'Overclocking',\n      riskLevel: 'High',\n      estimatedGain: '+15-25% performance',\n      fpsGain: '+20-35 FPS'\n    },\n    {\n      id: 'memoryOverclock',\n      title: 'Memory Overclock',\n      description: 'Overclock VRAM for better texture streaming and bandwidth',\n      impact: 'High',\n      category: 'Memory',\n      riskLevel: 'High',\n      estimatedGain: '+10-20% in memory-bound games',\n      fpsGain: '+10-25 FPS'\n    },\n    {\n      id: 'msiMode',\n      title: 'Enable MSI Mode',\n      description: 'Enable Message Signaled Interrupts for reduced latency',\n      impact: 'High',\n      category: 'Latency',\n      riskLevel: 'Low',\n      estimatedGain: '+5-15% latency reduction',\n      fpsGain: '+5-10 FPS'\n    },\n    {\n      id: 'preRenderedFrames',\n      title: 'Optimize Pre-rendered Frames',\n      description: 'Set optimal pre-rendered frames for reduced input lag',\n      impact: 'Medium',\n      category: 'Latency',\n      riskLevel: 'Low',\n      estimatedGain: 'Reduced input lag',\n      fpsGain: '+2-8 FPS'\n    },\n    {\n      id: 'powerManagement',\n      title: 'Maximum Performance Mode',\n      description: 'Set GPU to prefer maximum performance over power saving',\n      impact: 'High',\n      category: 'Power',\n      riskLevel: 'Low',\n      estimatedGain: '+10-20% consistent performance',\n      fpsGain: '+8-15 FPS'\n    },\n    {\n      id: 'textureFiltering',\n      title: 'Optimize Texture Filtering',\n      description: 'Set texture filtering to performance mode',\n      impact: 'Medium',\n      category: 'Graphics',\n      riskLevel: 'Low',\n      estimatedGain: '+5-10% in texture-heavy games',\n      fpsGain: '+3-12 FPS'\n    },\n    {\n      id: 'hags',\n      title: 'Hardware Accelerated GPU Scheduling',\n      description: 'Enable HAGS for reduced CPU overhead and better frame pacing',\n      impact: 'Medium',\n      category: 'Scheduling',\n      riskLevel: 'Low',\n      estimatedGain: '+3-8% CPU-bound scenarios',\n      fpsGain: '+5-15 FPS'\n    },\n    {\n      id: 'gameMode',\n      title: 'Windows Game Mode',\n      description: 'Enable Windows Game Mode for gaming optimizations',\n      impact: 'Medium',\n      category: 'System',\n      riskLevel: 'Low',\n      estimatedGain: '+2-5% overall gaming performance',\n      fpsGain: '+2-8 FPS'\n    },\n    {\n      id: 'fanCurve',\n      title: 'Aggressive Fan Curve',\n      description: 'Set aggressive fan curve to maintain lower temperatures',\n      impact: 'Medium',\n      category: 'Cooling',\n      riskLevel: 'Low',\n      estimatedGain: 'Better thermal throttling prevention',\n      fpsGain: '+5-15 FPS (thermal limited)'\n    }\n  ];\n\n  const handleTweakToggle = (tweakId) => {\n    if (!isAdmin) return;\n    \n    setGpuTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n\n  const applyOptimizations = async () => {\n    if (!isAdmin) return;\n    \n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n\n    const enabledTweaks = Object.entries(gpuTweaks)\n      .filter(([_, enabled]) => enabled)\n      .map(([id, _]) => id);\n\n    for (let i = 0; i < enabledTweaks.length; i++) {\n      const tweakId = enabledTweaks[i];\n      \n      try {\n        // Simulate applying tweak\n        await new Promise(resolve => setTimeout(resolve, 1200));\n        \n        // Here you would call the actual optimization functions\n        // await window.electronAPI.applyGpuTweak(tweakId);\n        \n        setOptimizationProgress(((i + 1) / enabledTweaks.length) * 100);\n      } catch (error) {\n        console.error(`Failed to apply ${tweakId}:`, error);\n      }\n    }\n\n    setIsOptimizing(false);\n    \n    // Calculate estimated FPS gain\n    const totalFpsGain = enabledTweaks.reduce((total, tweakId) => {\n      const tweak = gpuOptimizations.find(t => t.id === tweakId);\n      const fpsRange = tweak?.fpsGain?.match(/\\+(\\d+)-(\\d+)/);\n      if (fpsRange) {\n        const avgGain = (parseInt(fpsRange[1]) + parseInt(fpsRange[2])) / 2;\n        return total + avgGain;\n      }\n      return total;\n    }, 0);\n\n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'GPU Optimization Complete',\n      message: `Successfully applied ${enabledTweaks.length} GPU optimizations!\\nEstimated FPS gain: +${Math.round(totalFpsGain)} FPS`,\n      buttons: ['OK']\n    });\n  };\n\n  const getRiskColor = (risk) => {\n    switch (risk) {\n      case 'Low': return '#00ff88';\n      case 'Medium': return '#ffa502';\n      case 'High': return '#ff4757';\n      default: return '#888';\n    }\n  };\n\n  const getImpactColor = (impact) => {\n    switch (impact) {\n      case 'Very High': return '#ff6b35';\n      case 'High': return '#00ff88';\n      case 'Medium': return '#74b9ff';\n      case 'Low': return '#888';\n      default: return '#888';\n    }\n  };\n\n  return (\n    <div className=\"gpu-optimization\">\n      <div className=\"page-header\">\n        <div className=\"header-content\">\n          <div className=\"header-icon\">\n            <Monitor size={32} />\n          </div>\n          <div className=\"header-text\">\n            <h1>GPU Optimization</h1>\n            <p>Graphics card tweaks and overclocking for maximum FPS</p>\n          </div>\n        </div>\n        \n        {systemInfo?.graphics?.[0] && (\n          <div className=\"gpu-info-card\">\n            <h3>{systemInfo.graphics[0].model}</h3>\n            <div className=\"gpu-specs\">\n              <span>{systemInfo.graphics[0].vendor}</span>\n              {systemInfo.graphics[0].vram && (\n                <>\n                  <span>•</span>\n                  <span>{Math.round(systemInfo.graphics[0].vram / 1024)} GB VRAM</span>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      <div className=\"gpu-content\">\n        {/* GPU Metrics */}\n        <div className=\"metrics-section\">\n          <h2>Real-time GPU Metrics</h2>\n          <div className=\"metrics-grid\">\n            <div className=\"metric-card\">\n              <div className=\"metric-icon temp\">\n                <Thermometer size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.temperature}°C</div>\n                <div className=\"metric-label\">Temperature</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon usage\">\n                <Activity size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.usage}%</div>\n                <div className=\"metric-label\">GPU Usage</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon memory\">\n                <BarChart3 size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.memoryUsage}%</div>\n                <div className=\"metric-label\">VRAM Usage</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon clock\">\n                <Gauge size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.coreClock} MHz</div>\n                <div className=\"metric-label\">Core Clock</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon power\">\n                <Zap size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.powerDraw}W</div>\n                <div className=\"metric-label\">Power Draw</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon fan\">\n                <Settings size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{gpuMetrics.fanSpeed}%</div>\n                <div className=\"metric-label\">Fan Speed</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Optimization Controls */}\n        <div className=\"optimizations-section\">\n          <div className=\"section-header\">\n            <h2>GPU Optimizations</h2>\n            <button \n              className=\"apply-btn\"\n              onClick={applyOptimizations}\n              disabled={!isAdmin || isOptimizing || Object.values(gpuTweaks).every(v => !v)}\n            >\n              {isOptimizing ? (\n                <>\n                  <Activity size={16} className=\"spinning\" />\n                  Optimizing... {Math.round(optimizationProgress)}%\n                </>\n              ) : (\n                <>\n                  <Play size={16} />\n                  Apply Selected Tweaks\n                </>\n              )}\n            </button>\n          </div>\n\n          <div className=\"tweaks-grid\">\n            {gpuOptimizations.map((tweak) => (\n              <div key={tweak.id} className={`tweak-card ${gpuTweaks[tweak.id] ? 'enabled' : ''}`}>\n                <div className=\"tweak-header\">\n                  <div className=\"tweak-title\">\n                    <h3>{tweak.title}</h3>\n                    <div className=\"tweak-badges\">\n                      <span \n                        className=\"impact-badge\"\n                        style={{ backgroundColor: getImpactColor(tweak.impact) }}\n                      >\n                        {tweak.impact} Impact\n                      </span>\n                      <span \n                        className=\"risk-badge\"\n                        style={{ backgroundColor: getRiskColor(tweak.riskLevel) }}\n                      >\n                        {tweak.riskLevel} Risk\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <label className=\"toggle\">\n                    <input\n                      type=\"checkbox\"\n                      checked={gpuTweaks[tweak.id]}\n                      onChange={() => handleTweakToggle(tweak.id)}\n                      disabled={!isAdmin}\n                    />\n                    <span className=\"toggle-slider\"></span>\n                  </label>\n                </div>\n                \n                <p className=\"tweak-description\">{tweak.description}</p>\n                \n                <div className=\"tweak-details\">\n                  <div className=\"detail-item\">\n                    <span className=\"detail-label\">Category:</span>\n                    <span className=\"detail-value\">{tweak.category}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <span className=\"detail-label\">Performance Gain:</span>\n                    <span className=\"detail-value gain\">{tweak.estimatedGain}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <span className=\"detail-label\">FPS Boost:</span>\n                    <span className=\"detail-value fps-gain\">{tweak.fpsGain}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {!isAdmin && (\n        <div className=\"admin-required\">\n          <AlertTriangle size={20} />\n          <span>Administrator privileges required for GPU optimizations</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default GpuOptimization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EACPC,GAAG,EACHC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,KAAK,QACA,cAAc;AACrB,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,oBAAA;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC;IACzCwB,UAAU,EAAE,KAAK;IACjBC,eAAe,EAAE,KAAK;IACtBC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,KAAK;IACdC,iBAAiB,EAAE,KAAK;IACxBC,eAAe,EAAE,KAAK;IACtBC,gBAAgB,EAAE,KAAK;IACvBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC;IAC3CsC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,IAAI;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEnE,MAAMiD,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,4DAA4D;IACzEC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE,qBAAqB;IACpCC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,qDAAqD;IAClEC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,qBAAqB;IACpCC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,2DAA2D;IACxEC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,+BAA+B;IAC9CC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,wDAAwD;IACrEC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,0BAA0B;IACzCC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,mBAAmB;IACvBC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,uDAAuD;IACpEC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,mBAAmB;IAClCC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,iBAAiB;IACrBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,yDAAyD;IACtEC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,gCAAgC;IAC/CC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,2CAA2C;IACxDC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,+BAA+B;IAC9CC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,MAAM;IACVC,KAAK,EAAE,qCAAqC;IAC5CC,WAAW,EAAE,8DAA8D;IAC3EC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,2BAA2B;IAC1CC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,mDAAmD;IAChEC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,kCAAkC;IACjDC,OAAO,EAAE;EACX,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,yDAAyD;IACtEC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,sCAAsC;IACrDC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAIC,OAAO,IAAK;IACrC,IAAI,CAACzC,OAAO,EAAE;IAEdK,YAAY,CAACqC,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAC3C,OAAO,EAAE;IAEd4B,eAAe,CAAC,IAAI,CAAC;IACrBE,uBAAuB,CAAC,CAAC,CAAC;IAE1B,MAAMc,aAAa,GAAGC,MAAM,CAACC,OAAO,CAAC1C,SAAS,CAAC,CAC5C2C,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,OAAO,CAAC,KAAKA,OAAO,CAAC,CACjCC,GAAG,CAAC,CAAC,CAAClB,EAAE,EAAEgB,CAAC,CAAC,KAAKhB,EAAE,CAAC;IAEvB,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,aAAa,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMV,OAAO,GAAGG,aAAa,CAACO,CAAC,CAAC;MAEhC,IAAI;QACF;QACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA;;QAEAxB,uBAAuB,CAAE,CAACqB,CAAC,GAAG,CAAC,IAAIP,aAAa,CAACQ,MAAM,GAAI,GAAG,CAAC;MACjE,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmBf,OAAO,GAAG,EAAEe,KAAK,CAAC;MACrD;IACF;IAEA5B,eAAe,CAAC,KAAK,CAAC;;IAEtB;IACA,MAAM8B,YAAY,GAAGd,aAAa,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEnB,OAAO,KAAK;MAAA,IAAAoB,cAAA;MAC5D,MAAMC,KAAK,GAAG/B,gBAAgB,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKS,OAAO,CAAC;MAC1D,MAAMwB,QAAQ,GAAGH,KAAK,aAALA,KAAK,wBAAAD,cAAA,GAALC,KAAK,CAAEvB,OAAO,cAAAsB,cAAA,uBAAdA,cAAA,CAAgBK,KAAK,CAAC,eAAe,CAAC;MACvD,IAAID,QAAQ,EAAE;QACZ,MAAME,OAAO,GAAG,CAACC,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACnE,OAAOL,KAAK,GAAGO,OAAO;MACxB;MACA,OAAOP,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMS,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC;MACtCC,IAAI,EAAE,MAAM;MACZvC,KAAK,EAAE,2BAA2B;MAClCwC,OAAO,EAAE,wBAAwB7B,aAAa,CAACQ,MAAM,6CAA6CsB,IAAI,CAACC,KAAK,CAACjB,YAAY,CAAC,MAAM;MAChIkB,OAAO,EAAE,CAAC,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAI5C,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,MAAM;MACzB;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,oBACEvC,OAAA;IAAKoF,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BrF,OAAA;MAAKoF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrF,OAAA;QAAKoF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrF,OAAA;UAAKoF,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrF,OAAA,CAACZ,OAAO;YAACkG,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACN1F,OAAA;UAAKoF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrF,OAAA;YAAAqF,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB1F,OAAA;YAAAqF,QAAA,EAAG;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL,CAAArF,UAAU,aAAVA,UAAU,wBAAAE,oBAAA,GAAVF,UAAU,CAAEsF,QAAQ,cAAApF,oBAAA,uBAApBA,oBAAA,CAAuB,CAAC,CAAC,kBACxBP,OAAA;QAAKoF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrF,OAAA;UAAAqF,QAAA,EAAKhF,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAAC,CAACC;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC1F,OAAA;UAAKoF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrF,OAAA;YAAAqF,QAAA,EAAOhF,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAAC,CAACE;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CrF,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAAC,CAACG,IAAI,iBAC1B9F,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACErF,OAAA;cAAAqF,QAAA,EAAM;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACd1F,OAAA;cAAAqF,QAAA,GAAOP,IAAI,CAACC,KAAK,CAAC1E,UAAU,CAACsF,QAAQ,CAAC,CAAC,CAAC,CAACG,IAAI,GAAG,IAAI,CAAC,EAAC,UAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACrE,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN1F,OAAA;MAAKoF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1BrF,OAAA;QAAKoF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrF,OAAA;UAAAqF,QAAA,EAAI;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9B1F,OAAA;UAAKoF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrF,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BrF,OAAA,CAACV,WAAW;gBAACgG,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACE,WAAW,EAAC,OAAE;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9D1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCrF,OAAA,CAACT,QAAQ;gBAAC+F,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACG,KAAK,EAAC,GAAC;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvD1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACjCrF,OAAA,CAACH,SAAS;gBAACyF,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACI,WAAW,EAAC,GAAC;cAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCrF,OAAA,CAACF,KAAK;gBAACwF,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACK,SAAS,EAAC,MAAI;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9D1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCrF,OAAA,CAACX,GAAG;gBAACiG,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACO,SAAS,EAAC,GAAC;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3D1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrF,OAAA;cAAKoF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrF,OAAA,CAACR,QAAQ;gBAAC8F,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACN1F,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrF,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/D,UAAU,CAACQ,QAAQ,EAAC,GAAC;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1D1F,OAAA;gBAAKoF,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1F,OAAA;QAAKoF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCrF,OAAA;UAAKoF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrF,OAAA;YAAAqF,QAAA,EAAI;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B1F,OAAA;YACEoF,SAAS,EAAC,WAAW;YACrBW,OAAO,EAAEhD,kBAAmB;YAC5BiD,QAAQ,EAAE,CAAC5F,OAAO,IAAI2B,YAAY,IAAIkB,MAAM,CAACgD,MAAM,CAACzF,SAAS,CAAC,CAAC0F,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAAE;YAAAd,QAAA,EAE7EtD,YAAY,gBACX/B,OAAA,CAAAE,SAAA;cAAAmF,QAAA,gBACErF,OAAA,CAACT,QAAQ;gBAAC+F,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAC7B,EAACZ,IAAI,CAACC,KAAK,CAAC9C,oBAAoB,CAAC,EAAC,GAClD;YAAA,eAAE,CAAC,gBAEHjC,OAAA,CAAAE,SAAA;cAAAmF,QAAA,gBACErF,OAAA,CAACP,IAAI;gBAAC6F,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEpB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1F,OAAA;UAAKoF,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBlD,gBAAgB,CAACmB,GAAG,CAAEY,KAAK,iBAC1BlE,OAAA;YAAoBoF,SAAS,EAAE,cAAc5E,SAAS,CAAC0D,KAAK,CAAC9B,EAAE,CAAC,GAAG,SAAS,GAAG,EAAE,EAAG;YAAAiD,QAAA,gBAClFrF,OAAA;cAAKoF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrF,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAAqF,QAAA,EAAKnB,KAAK,CAAC7B;gBAAK;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtB1F,OAAA;kBAAKoF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BrF,OAAA;oBACEoF,SAAS,EAAC,cAAc;oBACxBgB,KAAK,EAAE;sBAAEC,eAAe,EAAElB,cAAc,CAACjB,KAAK,CAAC3B,MAAM;oBAAE,CAAE;oBAAA8C,QAAA,GAExDnB,KAAK,CAAC3B,MAAM,EAAC,SAChB;kBAAA;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1F,OAAA;oBACEoF,SAAS,EAAC,YAAY;oBACtBgB,KAAK,EAAE;sBAAEC,eAAe,EAAEpB,YAAY,CAACf,KAAK,CAACzB,SAAS;oBAAE,CAAE;oBAAA4C,QAAA,GAEzDnB,KAAK,CAACzB,SAAS,EAAC,OACnB;kBAAA;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1F,OAAA;gBAAOoF,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACvBrF,OAAA;kBACE4E,IAAI,EAAC,UAAU;kBACf0B,OAAO,EAAE9F,SAAS,CAAC0D,KAAK,CAAC9B,EAAE,CAAE;kBAC7BmE,QAAQ,EAAEA,CAAA,KAAM3D,iBAAiB,CAACsB,KAAK,CAAC9B,EAAE,CAAE;kBAC5C4D,QAAQ,EAAE,CAAC5F;gBAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACF1F,OAAA;kBAAMoF,SAAS,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN1F,OAAA;cAAGoF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEnB,KAAK,CAAC5B;YAAW;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExD1F,OAAA;cAAKoF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BrF,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAMoF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/C1F,OAAA;kBAAMoF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEnB,KAAK,CAAC1B;gBAAQ;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACN1F,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAMoF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvD1F,OAAA;kBAAMoF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAEnB,KAAK,CAACxB;gBAAa;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN1F,OAAA;gBAAKoF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrF,OAAA;kBAAMoF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChD1F,OAAA;kBAAMoF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEnB,KAAK,CAACvB;gBAAO;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA9CExB,KAAK,CAAC9B,EAAE;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+Cb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACtF,OAAO,iBACPJ,OAAA;MAAKoF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrF,OAAA,CAACN,aAAa;QAAC4F,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3B1F,OAAA;QAAAqF,QAAA,EAAM;MAAuD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpF,EAAA,CArYIH,eAAe;AAAAqG,EAAA,GAAfrG,eAAe;AAuYrB,eAAeA,eAAe;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}