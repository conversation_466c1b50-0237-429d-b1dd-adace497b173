{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Croissant = createLucideIcon(\"Croissant\", [[\"path\", {\n  d: \"m4.6 13.11 5.79-3.21c1.89-1.05 4.79 1.78 3.71 3.71l-3.22 5.81C8.8 23.16.79 15.23 4.6 13.11Z\",\n  key: \"1ozxlb\"\n}], [\"path\", {\n  d: \"m10.5 9.5-1-2.29C9.2 6.48 8.8 6 8 6H4.5C2.79 6 2 6.5 2 8.5a7.71 7.71 0 0 0 2 4.83\",\n  key: \"ffuyb5\"\n}], [\"path\", {\n  d: \"M8 6c0-1.55.24-4-2-4-2 0-2.5 2.17-2.5 4\",\n  key: \"osnpzi\"\n}], [\"path\", {\n  d: \"m14.5 13.5 2.29 1c.73.3 1.21.7 1.21 1.5v3.5c0 1.71-.5 2.5-2.5 2.5a7.71 7.71 0 0 1-4.83-2\",\n  key: \"1vubaw\"\n}], [\"path\", {\n  d: \"M18 16c1.55 0 4-.24 4 2 0 2-2.17 2.5-4 2.5\",\n  key: \"wxr772\"\n}]]);\nexport { Croissant as default };", "map": {"version": 3, "names": ["Croissant", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\croissant.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Croissant\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNC42IDEzLjExIDUuNzktMy4yMWMxLjg5LTEuMDUgNC43OSAxLjc4IDMuNzEgMy43MWwtMy4yMiA1LjgxQzguOCAyMy4xNi43OSAxNS4yMyA0LjYgMTMuMTFaIiAvPgogIDxwYXRoIGQ9Im0xMC41IDkuNS0xLTIuMjlDOS4yIDYuNDggOC44IDYgOCA2SDQuNUMyLjc5IDYgMiA2LjUgMiA4LjVhNy43MSA3LjcxIDAgMCAwIDIgNC44MyIgLz4KICA8cGF0aCBkPSJNOCA2YzAtMS41NS4yNC00LTItNC0yIDAtMi41IDIuMTctMi41IDQiIC8+CiAgPHBhdGggZD0ibTE0LjUgMTMuNSAyLjI5IDFjLjczLjMgMS4yMS43IDEuMjEgMS41djMuNWMwIDEuNzEtLjUgMi41LTIuNSAyLjVhNy43MSA3LjcxIDAgMCAxLTQuODMtMiIgLz4KICA8cGF0aCBkPSJNMTggMTZjMS41NSAwIDQtLjI0IDQgMiAwIDItMi4xNyAyLjUtNCAyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/croissant\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Croissant = createLucideIcon('Croissant', [\n  [\n    'path',\n    {\n      d: 'm4.6 13.11 5.79-3.21c1.89-1.05 4.79 1.78 3.71 3.71l-3.22 5.81C8.8 23.16.79 15.23 4.6 13.11Z',\n      key: '1ozxlb',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm10.5 9.5-1-2.29C9.2 6.48 8.8 6 8 6H4.5C2.79 6 2 6.5 2 8.5a7.71 7.71 0 0 0 2 4.83',\n      key: 'ffuyb5',\n    },\n  ],\n  ['path', { d: 'M8 6c0-1.55.24-4-2-4-2 0-2.5 2.17-2.5 4', key: 'osnpzi' }],\n  [\n    'path',\n    {\n      d: 'm14.5 13.5 2.29 1c.73.3 1.21.7 1.21 1.5v3.5c0 1.71-.5 2.5-2.5 2.5a7.71 7.71 0 0 1-4.83-2',\n      key: '1vubaw',\n    },\n  ],\n  ['path', { d: 'M18 16c1.55 0 4-.24 4 2 0 2-2.17 2.5-4 2.5', key: 'wxr772' }],\n]);\n\nexport default Croissant;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}