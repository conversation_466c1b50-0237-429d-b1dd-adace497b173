import React from 'react';
import { Minus, Square, X, Shield } from 'lucide-react';
import './TitleBar.css';

const TitleBar = () => {
  const handleMinimize = () => {
    window.electronAPI.minimizeWindow();
  };

  const handleMaximize = () => {
    window.electronAPI.maximizeWindow();
  };

  const handleClose = () => {
    window.electronAPI.closeWindow();
  };

  return (
    <div className="title-bar">
      <div className="title-bar-left">
        <div className="app-icon">
          <Shield size={20} />
        </div>
        <div className="app-title">
          <span className="title-main">Rodey Premium</span>
          <span className="title-sub">Tweaker</span>
        </div>
      </div>

      <div className="title-bar-center">
        <div className="performance-indicator">
          <div className="perf-dot perf-active"></div>
          <span>System Optimized</span>
        </div>
      </div>

      <div className="title-bar-right">
        <button 
          className="title-bar-button minimize"
          onClick={handleMinimize}
          title="Minimize"
        >
          <Minus size={16} />
        </button>
        <button 
          className="title-bar-button maximize"
          onClick={handleMaximize}
          title="Maximize"
        >
          <Square size={14} />
        </button>
        <button 
          className="title-bar-button close"
          onClick={handleClose}
          title="Close"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default TitleBar;
