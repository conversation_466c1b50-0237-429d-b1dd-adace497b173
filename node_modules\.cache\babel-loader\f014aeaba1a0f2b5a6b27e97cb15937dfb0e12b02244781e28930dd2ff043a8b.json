{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Cctv = createLucideIcon(\"Cctv\", [[\"path\", {\n  d: \"M7 9h.01\",\n  key: \"19b3jx\"\n}], [\"path\", {\n  d: \"M16.75 12H22l-3.5 7-3.09-4.32\",\n  key: \"1h9vqe\"\n}], [\"path\", {\n  d: \"M18 9.5l-4 8-10.39-5.2a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3Z\",\n  key: \"q5d122\"\n}], [\"path\", {\n  d: \"M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15\",\n  key: \"19bib8\"\n}], [\"path\", {\n  d: \"M2 21v-4\",\n  key: \"l40lih\"\n}]]);\nexport { Cctv as default };", "map": {"version": 3, "names": ["Cctv", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\cctv.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Cctv\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyA5aC4wMSIgLz4KICA8cGF0aCBkPSJNMTYuNzUgMTJIMjJsLTMuNSA3LTMuMDktNC4zMiIgLz4KICA8cGF0aCBkPSJNMTggOS41bC00IDgtMTAuMzktNS4yYTIuOTIgMi45MiAwIDAgMS0xLjMtMy45MUwzLjY5IDUuNmEyLjkyIDIuOTIgMCAwIDEgMy45Mi0xLjNaIiAvPgogIDxwYXRoIGQ9Ik0yIDE5aDMuNzZhMiAyIDAgMCAwIDEuOC0xLjFMOSAxNSIgLz4KICA8cGF0aCBkPSJNMiAyMXYtNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/cctv\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cctv = createLucideIcon('Cctv', [\n  ['path', { d: 'M7 9h.01', key: '19b3jx' }],\n  ['path', { d: 'M16.75 12H22l-3.5 7-3.09-4.32', key: '1h9vqe' }],\n  [\n    'path',\n    {\n      d: 'M18 9.5l-4 8-10.39-5.2a2.92 2.92 0 0 1-1.3-3.91L3.69 5.6a2.92 2.92 0 0 1 3.92-1.3Z',\n      key: 'q5d122',\n    },\n  ],\n  ['path', { d: 'M2 19h3.76a2 2 0 0 0 1.8-1.1L9 15', key: '19bib8' }],\n  ['path', { d: 'M2 21v-4', key: 'l40lih' }],\n]);\n\nexport default Cctv;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}