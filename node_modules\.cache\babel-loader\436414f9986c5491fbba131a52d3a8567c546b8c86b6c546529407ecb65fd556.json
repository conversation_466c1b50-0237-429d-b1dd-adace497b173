{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Undo2 = createLucideIcon(\"Undo2\", [[\"path\", {\n  d: \"M9 14 4 9l5-5\",\n  key: \"102s5s\"\n}], [\"path\", {\n  d: \"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v0a5.5 5.5 0 0 1-5.5 5.5H11\",\n  key: \"llx8ln\"\n}]]);\nexport { Undo2 as default };", "map": {"version": 3, "names": ["Undo2", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\undo-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Undo2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAxNCA0IDlsNS01IiAvPgogIDxwYXRoIGQ9Ik00IDloMTAuNWE1LjUgNS41IDAgMCAxIDUuNSA1LjV2MGE1LjUgNS41IDAgMCAxLTUuNSA1LjVIMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/undo-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Undo2 = createLucideIcon('Undo2', [\n  ['path', { d: 'M9 14 4 9l5-5', key: '102s5s' }],\n  ['path', { d: 'M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v0a5.5 5.5 0 0 1-5.5 5.5H11', key: 'llx8ln' }],\n]);\n\nexport default Undo2;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,4DAA8D;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}