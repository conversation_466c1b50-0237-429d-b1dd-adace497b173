using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Timer = System.Windows.Forms.Timer;

namespace RodeyPremiumTweaker.Controls
{
    public class ModernProgressPanel : UserControl
    {
        private string _title = "";
        private string _status = "";
        private int _progress = 0;
        private bool _isComplete = false;
        private Timer _animationTimer;
        private float _animationOffset = 0;

        public string Title
        {
            get => _title;
            set { _title = value; Invalidate(); }
        }

        public string Status
        {
            get => _status;
            set { _status = value; Invalidate(); }
        }

        public int Progress
        {
            get => _progress;
            set
            {
                _progress = Math.Max(0, Math.Min(100, value));
                _isComplete = _progress >= 100;
                Invalidate();
            }
        }

        public bool IsComplete
        {
            get => _isComplete;
            set { _isComplete = value; Invalidate(); }
        }

        public ModernProgressPanel()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw |
                     ControlStyles.SupportsTransparentBackColor, true);

            BackColor = Color.Transparent;
            Size = new Size(800, 200);

            _animationTimer = new Timer { Interval = 50 };
            _animationTimer.Tick += (s, e) =>
            {
                _animationOffset += 2;
                if (_animationOffset > 40) _animationOffset = 0;
                if (!_isComplete) Invalidate();
            };
            _animationTimer.Start();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            // Header with gradient
            var headerRect = new Rectangle(0, 0, Width, 80);
            using (var headerBrush = new LinearGradientBrush(
                headerRect,
                Color.FromArgb(0, 120, 215),
                Color.FromArgb(0, 80, 150),
                LinearGradientMode.Horizontal))
            {
                using (var headerPath = CreateRoundedRectanglePath(headerRect, 8))
                {
                    g.FillPath(headerBrush, headerPath);
                }
            }

            // Title
            using (var titleBrush = new SolidBrush(Color.White))
            using (var titleFont = new Font("Segoe UI", 18, FontStyle.Bold))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };
                var titleRect = new Rectangle(20, 10, Width - 40, 35);
                g.DrawString(_title, titleFont, titleBrush, titleRect, titleFormat);
            }

            // Subtitle
            using (var subtitleBrush = new SolidBrush(Color.FromArgb(220, 220, 220)))
            using (var subtitleFont = new Font("Segoe UI", 11))
            {
                var subtitleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };
                var subtitleRect = new Rectangle(20, 45, Width - 40, 25);
                g.DrawString("Applying extreme performance tweaks...", subtitleFont, subtitleBrush, subtitleRect, subtitleFormat);
            }

            // Status panel
            var statusRect = new Rectangle(0, 90, Width, 50);
            using (var statusBrush = new SolidBrush(Color.FromArgb(40, 40, 43)))
            {
                using (var statusPath = CreateRoundedRectanglePath(statusRect, 6))
                {
                    g.FillPath(statusBrush, statusPath);

                    using (var borderPen = new Pen(Color.FromArgb(0, 120, 215), 2))
                    {
                        g.DrawPath(borderPen, statusPath);
                    }
                }
            }

            // Status text
            var statusColor = _isComplete ? Color.FromArgb(0, 255, 127) : Color.FromArgb(255, 193, 7);
            var statusIcon = _isComplete ? "✅" : "⚡";

            using (var statusTextBrush = new SolidBrush(statusColor))
            using (var statusFont = new Font("Segoe UI", 12, FontStyle.Bold))
            {
                var statusFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };
                var statusTextRect = new Rectangle(20, 90, Width - 40, 50);
                g.DrawString($"{statusIcon} {_status}", statusFont, statusTextBrush, statusTextRect, statusFormat);
            }

            // Progress bar background
            var progressBgRect = new Rectangle(20, 155, Width - 40, 25);
            using (var progressBgBrush = new SolidBrush(Color.FromArgb(30, 30, 33)))
            {
                using (var progressBgPath = CreateRoundedRectanglePath(progressBgRect, 12))
                {
                    g.FillPath(progressBgBrush, progressBgPath);
                }
            }

            // Progress bar fill
            if (_progress > 0)
            {
                var progressWidth = (int)((Width - 40) * (_progress / 100.0));
                var progressRect = new Rectangle(20, 155, progressWidth, 25);

                if (_isComplete)
                {
                    // Solid green for complete
                    using (var progressBrush = new SolidBrush(Color.FromArgb(0, 255, 127)))
                    {
                        using (var progressPath = CreateRoundedRectanglePath(progressRect, 12))
                        {
                            g.FillPath(progressBrush, progressPath);
                        }
                    }
                }
                else
                {
                    // Animated gradient for in progress
                    var gradientRect = new Rectangle(progressRect.X - 20, progressRect.Y, progressRect.Width + 40, progressRect.Height);
                    using (var progressBrush = new LinearGradientBrush(
                        gradientRect,
                        Color.FromArgb(0, 120, 215),
                        Color.FromArgb(0, 180, 255),
                        LinearGradientMode.Horizontal))
                    {
                        // Animate the gradient
                        var blend = new ColorBlend(3);
                        blend.Colors = new[] { Color.FromArgb(0, 120, 215), Color.FromArgb(0, 180, 255), Color.FromArgb(0, 120, 215) };
                        blend.Positions = new[] { 0f, 0.5f, 1f };
                        progressBrush.InterpolationColors = blend;

                        using (var progressPath = CreateRoundedRectanglePath(progressRect, 12))
                        {
                            g.FillPath(progressBrush, progressPath);
                        }
                    }
                }
            }

            // Progress percentage
            if (_progress > 0)
            {
                using (var percentBrush = new SolidBrush(Color.White))
                using (var percentFont = new Font("Segoe UI", 10, FontStyle.Bold))
                {
                    var percentFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    g.DrawString($"{_progress}%", percentFont, percentBrush, progressBgRect, percentFormat);
                }
            }
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Stop();
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
