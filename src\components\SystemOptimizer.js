import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  Play, 
  Pause, 
  RotateCcw, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Cpu,
  Monitor,
  MemoryStick,
  Wifi,
  HardDrive,
  Settings2
} from 'lucide-react';
import './SystemOptimizer.css';

const SystemOptimizer = ({ isAdmin }) => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [optimizationResults, setOptimizationResults] = useState(null);
  const [selectedProfile, setSelectedProfile] = useState('gaming');

  const optimizationProfiles = {
    gaming: {
      name: 'Gaming Performance',
      description: 'Maximum FPS and lowest latency for gaming',
      icon: Zap,
      color: '#00ff88',
      tweaks: [
        'High Performance Power Plan',
        'Disable Windows Game Bar',
        'Optimize GPU Settings',
        'Disable Fullscreen Optimizations',
        'Set CPU Priority to High',
        'Disable Windows Updates',
        'Clean Temporary Files',
        'Optimize Network Settings',
        'Disable Background Apps',
        'Registry Gaming Tweaks'
      ],
      estimatedGain: '+35-60% FPS boost'
    },
    extreme: {
      name: 'Extreme Performance',
      description: 'Maximum performance with all advanced tweaks',
      icon: TrendingUp,
      color: '#ff6b35',
      tweaks: [
        'All Gaming Tweaks',
        'Disable Security Mitigations',
        'Aggressive CPU Overclocking',
        'GPU Memory Overclock',
        'Disable C-States',
        'MSI Mode for GPU',
        'Disable HPET',
        'Registry Performance Tweaks',
        'Kernel Optimizations',
        'BIOS-level Tweaks'
      ],
      estimatedGain: '+60-100% FPS boost'
    },
    balanced: {
      name: 'Balanced Optimization',
      description: 'Good performance while maintaining stability',
      icon: Shield,
      color: '#74b9ff',
      tweaks: [
        'Balanced Power Plan',
        'Basic GPU Optimization',
        'Memory Cleanup',
        'Network Optimization',
        'Startup Optimization',
        'Visual Effects Tweaks',
        'Safe Registry Tweaks',
        'Service Optimization'
      ],
      estimatedGain: '+15-30% FPS boost'
    }
  };

  const optimizationSteps = [
    { name: 'Analyzing System', icon: Activity, duration: 2000 },
    { name: 'Optimizing CPU Settings', icon: Cpu, duration: 3000 },
    { name: 'Configuring GPU', icon: Monitor, duration: 2500 },
    { name: 'Optimizing Memory', icon: MemoryStick, duration: 2000 },
    { name: 'Network Optimization', icon: Wifi, duration: 1500 },
    { name: 'Registry Tweaks', icon: Settings2, duration: 4000 },
    { name: 'System Cleanup', icon: HardDrive, duration: 3000 },
    { name: 'Finalizing Changes', icon: CheckCircle, duration: 1000 }
  ];

  const startOptimization = async () => {
    if (!isAdmin) {
      await window.electronAPI.showMessageBox({
        type: 'warning',
        title: 'Administrator Required',
        message: 'Administrator privileges are required for system optimization.',
        buttons: ['OK']
      });
      return;
    }

    setIsOptimizing(true);
    setOptimizationProgress(0);
    setOptimizationResults(null);

    const profile = optimizationProfiles[selectedProfile];
    let totalProgress = 0;

    for (let i = 0; i < optimizationSteps.length; i++) {
      const step = optimizationSteps[i];
      setCurrentStep(step.name);

      // Simulate optimization step
      await new Promise(resolve => setTimeout(resolve, step.duration));

      totalProgress = ((i + 1) / optimizationSteps.length) * 100;
      setOptimizationProgress(totalProgress);
    }

    // Generate results
    const results = {
      profile: profile.name,
      tweaksApplied: profile.tweaks.length,
      estimatedFpsGain: profile.estimatedGain,
      optimizationTime: optimizationSteps.reduce((total, step) => total + step.duration, 0) / 1000,
      categories: {
        cpu: Math.floor(Math.random() * 20) + 10,
        gpu: Math.floor(Math.random() * 25) + 15,
        memory: Math.floor(Math.random() * 15) + 8,
        network: Math.floor(Math.random() * 10) + 5,
        system: Math.floor(Math.random() * 18) + 12
      }
    };

    setOptimizationResults(results);
    setIsOptimizing(false);
    setCurrentStep('');
  };

  const createRestorePoint = async () => {
    if (!isAdmin) return;

    try {
      await window.electronAPI.showMessageBox({
        type: 'info',
        title: 'Creating Restore Point',
        message: 'Creating system restore point before optimization...',
        buttons: ['OK']
      });

      // Here you would create actual restore point
      // await window.electronAPI.createRestorePoint('Rodey Premium Pre-Optimization');
    } catch (error) {
      console.error('Failed to create restore point:', error);
    }
  };

  return (
    <div className="system-optimizer">
      <div className="page-header">
        <div className="header-content">
          <div className="header-icon">
            <Zap size={32} />
          </div>
          <div className="header-text">
            <h1>System Optimizer</h1>
            <p>One-click optimization for maximum gaming performance</p>
          </div>
        </div>
      </div>

      <div className="optimizer-content">
        {/* Profile Selection */}
        <div className="profile-section">
          <h2>Optimization Profile</h2>
          <div className="profile-grid">
            {Object.entries(optimizationProfiles).map(([key, profile]) => {
              const ProfileIcon = profile.icon;
              return (
                <div 
                  key={key}
                  className={`profile-card ${selectedProfile === key ? 'selected' : ''}`}
                  onClick={() => setSelectedProfile(key)}
                  style={{ '--profile-color': profile.color }}
                >
                  <div className="profile-icon">
                    <ProfileIcon size={24} />
                  </div>
                  <div className="profile-content">
                    <h3>{profile.name}</h3>
                    <p>{profile.description}</p>
                    <div className="profile-gain">{profile.estimatedGain}</div>
                  </div>
                  <div className="profile-tweaks">
                    <span>{profile.tweaks.length} tweaks</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Optimization Controls */}
        <div className="controls-section">
          <div className="main-controls">
            <button 
              className="optimize-btn"
              onClick={startOptimization}
              disabled={isOptimizing || !isAdmin}
            >
              {isOptimizing ? (
                <>
                  <Pause size={20} />
                  Optimizing System...
                </>
              ) : (
                <>
                  <Play size={20} />
                  Start Optimization
                </>
              )}
            </button>

            <button 
              className="restore-btn"
              onClick={createRestorePoint}
              disabled={isOptimizing || !isAdmin}
            >
              <RotateCcw size={16} />
              Create Restore Point
            </button>
          </div>

          {isOptimizing && (
            <div className="progress-section">
              <div className="progress-header">
                <span className="current-step">{currentStep}</span>
                <span className="progress-percent">{Math.round(optimizationProgress)}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${optimizationProgress}%` }}
                ></div>
              </div>
              <div className="step-indicators">
                {optimizationSteps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isCompleted = (index + 1) / optimizationSteps.length * 100 <= optimizationProgress;
                  const isCurrent = step.name === currentStep;
                  
                  return (
                    <div 
                      key={index}
                      className={`step-indicator ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}`}
                    >
                      <StepIcon size={16} />
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        {optimizationResults && (
          <div className="results-section">
            <div className="results-header">
              <CheckCircle size={24} className="success-icon" />
              <h2>Optimization Complete!</h2>
            </div>
            
            <div className="results-grid">
              <div className="result-card">
                <h3>Profile Applied</h3>
                <div className="result-value">{optimizationResults.profile}</div>
              </div>
              
              <div className="result-card">
                <h3>Tweaks Applied</h3>
                <div className="result-value">{optimizationResults.tweaksApplied}</div>
              </div>
              
              <div className="result-card">
                <h3>Estimated FPS Gain</h3>
                <div className="result-value gain">{optimizationResults.estimatedFpsGain}</div>
              </div>
              
              <div className="result-card">
                <h3>Optimization Time</h3>
                <div className="result-value">{optimizationResults.optimizationTime}s</div>
              </div>
            </div>

            <div className="category-results">
              <h3>Performance Improvements by Category</h3>
              <div className="category-grid">
                {Object.entries(optimizationResults.categories).map(([category, improvement]) => (
                  <div key={category} className="category-item">
                    <span className="category-name">{category.toUpperCase()}</span>
                    <span className="category-improvement">+{improvement}%</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Selected Profile Details */}
        <div className="profile-details">
          <h2>Included Optimizations</h2>
          <div className="tweaks-list">
            {optimizationProfiles[selectedProfile].tweaks.map((tweak, index) => (
              <div key={index} className="tweak-item">
                <CheckCircle size={16} />
                <span>{tweak}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {!isAdmin && (
        <div className="admin-warning">
          <AlertTriangle size={20} />
          <div>
            <strong>Administrator Required</strong>
            <p>System optimization requires administrator privileges. Please restart the application as administrator.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemOptimizer;
