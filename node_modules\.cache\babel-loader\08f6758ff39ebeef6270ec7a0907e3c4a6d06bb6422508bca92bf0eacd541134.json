{"ast": null, "code": "import { AnimationFeature } from './animation/index.mjs';\nimport { ExitAnimationFeature } from './animation/exit.mjs';\nconst animations = {\n  animation: {\n    Feature: AnimationFeature\n  },\n  exit: {\n    Feature: ExitAnimationFeature\n  }\n};\nexport { animations };", "map": {"version": 3, "names": ["AnimationFeature", "ExitAnimationFeature", "animations", "animation", "Feature", "exit"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/motion/features/animations.mjs"], "sourcesContent": ["import { AnimationFeature } from './animation/index.mjs';\nimport { ExitAnimationFeature } from './animation/exit.mjs';\n\nconst animations = {\n    animation: {\n        Feature: AnimationFeature,\n    },\n    exit: {\n        Feature: ExitAnimationFeature,\n    },\n};\n\nexport { animations };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,oBAAoB,QAAQ,sBAAsB;AAE3D,MAAMC,UAAU,GAAG;EACfC,SAAS,EAAE;IACPC,OAAO,EAAEJ;EACb,CAAC;EACDK,IAAI,EAAE;IACFD,OAAO,EAAEH;EACb;AACJ,CAAC;AAED,SAASC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}