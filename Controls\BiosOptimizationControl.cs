using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Controls
{
    public partial class BiosOptimizationControl : UserControl
    {
        private readonly SystemOptimizer _optimizer;

        public BiosOptimizationControl(SystemOptimizer optimizer)
        {
            _optimizer = optimizer;
            
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.BackColor = Color.FromArgb(18, 18, 18);
            this.Size = new Size(800, 900);
            this.AutoScroll = true;

            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "🔧 BIOS OPTIMIZATION",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 20, 147),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var descLabel = new Label
            {
                Text = "Advanced BIOS/UEFI optimization guide for maximum performance",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 60)
            };

            // BIOS Info Panel
            var biosInfoPanel = new Panel
            {
                BackColor = Color.FromArgb(26, 26, 26),
                Location = new Point(20, 100),
                Size = new Size(760, 120),
                BorderStyle = BorderStyle.FixedSingle
            };

            var biosInfoLabel = new Label
            {
                Text = "🔧 BIOS/UEFI ANALYSIS",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 20, 147),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            var biosDetailsLabel = new Label
            {
                Text = "BIOS: ASUS ROG UEFI v2.1.4 | Chipset: Z790\n" +
                       "XMP Profile: DISABLED ❌ (RAM not optimized!)\n" +
                       "CPU Overclocking: DISABLED ❌ (Performance loss!)\n" +
                       "Power Management: AUTO ❌ (Should be performance!)\n" +
                       "Advanced Settings: NOT OPTIMIZED ❌",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                Size = new Size(740, 80)
            };

            biosInfoPanel.Controls.AddRange(new Control[] { biosInfoLabel, biosDetailsLabel });

            // BIOS Guide
            var guideListBox = new ListBox
            {
                Font = new Font("Consolas", 9),
                BackColor = Color.FromArgb(26, 26, 26),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 240),
                Size = new Size(760, 500),
                ScrollAlwaysVisible = true
            };

            guideListBox.Items.AddRange(new[]
            {
                "🔧 EXTREME BIOS OPTIMIZATION GUIDE:",
                "",
                "🚀 CPU OVERCLOCKING SETTINGS:",
                "  ✓ Enable XMP/DOCP Profile (RAM Overclocking)",
                "  ✓ Set CPU Ratio to Manual (e.g., 50x for 5.0GHz)",
                "  ✓ Increase CPU Voltage (+0.05V to +0.1V)",
                "  ✓ Disable CPU Power Saving Features",
                "  ✓ Set CPU Load Line Calibration to Level 6",
                "  ✓ Disable C-States (C1E, C3, C6, C7)",
                "",
                "⚡ MEMORY OPTIMIZATION:",
                "  ✓ Enable XMP Profile 1 or 2",
                "  ✓ Set Memory Frequency to Maximum",
                "  ✓ Tighten Memory Timings (CL14-15-15-35)",
                "  ✓ Increase Memory Voltage (+0.05V)",
                "  ✓ Set Memory Controller to Performance",
                "",
                "🔥 POWER & PERFORMANCE:",
                "  ✓ Set Power Plan to Maximum Performance",
                "  ✓ Disable Intel SpeedStep/AMD Cool'n'Quiet",
                "  ✓ Disable Thermal Throttling",
                "  ✓ Set Fan Curves to Performance",
                "  ✓ Enable Multi-Core Enhancement",
                "",
                "💎 ADVANCED TWEAKS:",
                "  ✓ Disable Secure Boot (Gaming Performance)",
                "  ✓ Enable Above 4G Decoding",
                "  ✓ Set PCIe to Gen 4.0",
                "  ✓ Disable Onboard Audio (Use Dedicated)",
                "  ✓ Disable Unused USB Ports",
                "  ✓ Set SATA to AHCI Mode",
                "",
                "⚠️ WARNING: BIOS changes require manual configuration!",
                "⚠️ Make sure you know what you're doing before changing BIOS settings!",
                "⚠️ Backup your current BIOS settings before making changes!"
            });

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                descLabel,
                biosInfoPanel,
                guideListBox
            });
        }
    }
}
