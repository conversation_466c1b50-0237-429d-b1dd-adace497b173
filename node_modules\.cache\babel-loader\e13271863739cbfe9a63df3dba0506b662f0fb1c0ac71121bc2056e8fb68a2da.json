{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\SecurityOptimization.js\";\nimport React from 'react';\nimport { Shield } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SecurityOptimization = ({\n  isAdmin\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: '#ff4757'\n      },\n      children: \"Security Optimization\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#888'\n      },\n      children: \"Security vs performance tweaks coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = SecurityOptimization;\nexport default SecurityOptimization;\nvar _c;\n$RefreshReg$(_c, \"SecurityOptimization\");", "map": {"version": 3, "names": ["React", "Shield", "jsxDEV", "_jsxDEV", "SecurityOptimization", "isAdmin", "style", "padding", "textAlign", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/SecurityOptimization.js"], "sourcesContent": ["import React from 'react';\nimport { Shield } from 'lucide-react';\n\nconst SecurityOptimization = ({ isAdmin }) => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1 style={{ color: '#ff4757' }}>Security Optimization</h1>\n      <p style={{ color: '#888' }}>Security vs performance tweaks coming soon...</p>\n    </div>\n  );\n};\n\nexport default SecurityOptimization;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC5C,oBACEF,OAAA;IAAKG,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDN,OAAA;MAAIG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAU,CAAE;MAAAD,QAAA,EAAC;IAAqB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3DX,OAAA;MAAGG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAA6C;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3E,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIX,oBAAoB;AAS1B,eAAeA,oBAAoB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}