using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace RodeyPremiumTweaker.Controls
{
    public class ModernCard : UserControl
    {
        private string _title = "";
        private string _description = "";
        private string _icon = "";
        private string _tweakCount = "";
        private Color _accentColor = Color.FromArgb(0, 120, 215);
        private bool _isHovered = false;
        private bool _isPressed = false;
        
        public string Title
        {
            get => _title;
            set { _title = value; Invalidate(); }
        }
        
        public string Description
        {
            get => _description;
            set { _description = value; Invalidate(); }
        }
        
        public string Icon
        {
            get => _icon;
            set { _icon = value; Invalidate(); }
        }
        
        public string TweakCount
        {
            get => _tweakCount;
            set { _tweakCount = value; Invalidate(); }
        }
        
        public Color AccentColor
        {
            get => _accentColor;
            set { _accentColor = value; Invalidate(); }
        }

        public ModernCard()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw |
                     ControlStyles.SupportsTransparentBackColor, true);
            
            BackColor = Color.Transparent;
            Size = new Size(220, 160);
            Cursor = Cursors.Hand;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            // Modern card background with rounded corners
            var cardRect = new Rectangle(5, 5, Width - 10, Height - 10);
            var cornerRadius = 12;
            
            using (var path = CreateRoundedRectanglePath(cardRect, cornerRadius))
            {
                // Drop shadow
                var shadowRect = new Rectangle(cardRect.X + 3, cardRect.Y + 3, cardRect.Width, cardRect.Height);
                using (var shadowPath = CreateRoundedRectanglePath(shadowRect, cornerRadius))
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    g.FillPath(shadowBrush, shadowPath);
                }

                // Card background gradient
                var startColor = _isPressed ? Color.FromArgb(35, 35, 38) : 
                                _isHovered ? Color.FromArgb(55, 55, 58) : Color.FromArgb(45, 45, 48);
                var endColor = _isPressed ? Color.FromArgb(25, 25, 28) : 
                              _isHovered ? Color.FromArgb(45, 45, 48) : Color.FromArgb(35, 35, 38);
                
                using (var bgBrush = new LinearGradientBrush(cardRect, startColor, endColor, LinearGradientMode.Vertical))
                {
                    g.FillPath(bgBrush, path);
                }

                // Accent border
                var borderColor = _isHovered ? 
                    Color.FromArgb(Math.Min(255, _accentColor.R + 40), 
                                  Math.Min(255, _accentColor.G + 40), 
                                  Math.Min(255, _accentColor.B + 40)) : _accentColor;
                
                using (var borderPen = new Pen(borderColor, 2))
                {
                    g.DrawPath(borderPen, path);
                }

                // Inner glow
                using (var glowPen = new Pen(Color.FromArgb(20, borderColor), 1))
                {
                    var innerRect = new Rectangle(cardRect.X + 2, cardRect.Y + 2, cardRect.Width - 4, cardRect.Height - 4);
                    using (var innerPath = CreateRoundedRectanglePath(innerRect, cornerRadius - 2))
                    {
                        g.DrawPath(glowPen, innerPath);
                    }
                }
            }

            // Icon
            var iconRect = new Rectangle(20, 20, 50, 50);
            using (var iconBrush = new SolidBrush(_isHovered ? 
                Color.FromArgb(Math.Min(255, _accentColor.R + 30), 
                              Math.Min(255, _accentColor.G + 30), 
                              Math.Min(255, _accentColor.B + 30)) : _accentColor))
            using (var iconFont = new Font("Segoe UI Emoji", 24, FontStyle.Bold))
            {
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(_icon, iconFont, iconBrush, iconRect, iconFormat);
            }

            // Title
            var titleRect = new Rectangle(20, 80, Width - 40, 25);
            using (var titleBrush = new SolidBrush(Color.White))
            using (var titleFont = new Font("Segoe UI", 14, FontStyle.Bold))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center,
                    Trimming = StringTrimming.EllipsisCharacter
                };
                g.DrawString(_title, titleFont, titleBrush, titleRect, titleFormat);
            }

            // Tweak count badge
            var badgeRect = new Rectangle(20, 110, 90, 22);
            using (var badgePath = CreateRoundedRectanglePath(badgeRect, 11))
            using (var badgeBrush = new SolidBrush(Color.FromArgb(40, _accentColor)))
            {
                g.FillPath(badgeBrush, badgePath);
                
                using (var badgeBorderPen = new Pen(_accentColor, 1))
                {
                    g.DrawPath(badgeBorderPen, badgePath);
                }
                
                using (var badgeTextBrush = new SolidBrush(_accentColor))
                using (var badgeFont = new Font("Segoe UI", 9, FontStyle.Bold))
                {
                    var badgeFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    g.DrawString($"{_tweakCount} tweaks", badgeFont, badgeTextBrush, badgeRect, badgeFormat);
                }
            }

            // Status indicator
            var statusRect = new Rectangle(Width - 80, 112, 60, 18);
            using (var statusBrush = new SolidBrush(Color.FromArgb(0, 255, 127)))
            using (var statusFont = new Font("Segoe UI", 8, FontStyle.Bold))
            {
                var statusFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString("READY", statusFont, statusBrush, statusRect, statusFormat);
            }
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;
            
            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();
            
            return path;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _isPressed = true;
            Invalidate();
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }
    }
}
