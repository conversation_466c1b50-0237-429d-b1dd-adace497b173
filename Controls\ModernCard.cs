using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace RodeyPremiumTweaker.Controls
{
    public class ModernCard : UserControl
    {
        private string _title = "";
        private string _description = "";
        private string _icon = "";
        private string _tweakCount = "";
        private Color _accentColor = Color.FromArgb(0, 120, 215);
        private bool _isHovered = false;
        private bool _isPressed = false;

        public string Title
        {
            get => _title;
            set { _title = value; Invalidate(); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; Invalidate(); }
        }

        public string Icon
        {
            get => _icon;
            set { _icon = value; Invalidate(); }
        }

        public string TweakCount
        {
            get => _tweakCount;
            set { _tweakCount = value; Invalidate(); }
        }

        public Color AccentColor
        {
            get => _accentColor;
            set { _accentColor = value; Invalidate(); }
        }

        public ModernCard()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw |
                     ControlStyles.SupportsTransparentBackColor, true);

            BackColor = Color.Transparent;
            Size = new Size(220, 160);
            Cursor = Cursors.Hand;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

            // Professional card with glass morphism effect
            var cardRect = new Rectangle(8, 8, Width - 16, Height - 16);
            var cornerRadius = 16;

            using (var path = CreateRoundedRectanglePath(cardRect, cornerRadius))
            {
                // Multiple shadow layers for depth
                for (int i = 0; i < 3; i++)
                {
                    var shadowOffset = 2 + i * 2;
                    var shadowOpacity = 15 - i * 3;
                    var shadowRect = new Rectangle(cardRect.X + shadowOffset, cardRect.Y + shadowOffset, cardRect.Width, cardRect.Height);
                    using (var shadowPath = CreateRoundedRectanglePath(shadowRect, cornerRadius))
                    using (var shadowBrush = new SolidBrush(Color.FromArgb(shadowOpacity, 0, 0, 0)))
                    {
                        g.FillPath(shadowBrush, shadowPath);
                    }
                }

                // Glass morphism background
                var baseColor = _isPressed ? Color.FromArgb(25, 25, 28) :
                               _isHovered ? Color.FromArgb(40, 42, 48) : Color.FromArgb(32, 34, 40);

                using (var bgBrush = new SolidBrush(baseColor))
                {
                    g.FillPath(bgBrush, path);
                }

                // Gradient overlay for premium look
                var overlayRect = new Rectangle(cardRect.X, cardRect.Y, cardRect.Width, cardRect.Height / 2);
                using (var overlayPath = CreateRoundedRectanglePath(overlayRect, cornerRadius))
                {
                    using (var overlayBrush = new LinearGradientBrush(
                        overlayRect,
                        Color.FromArgb(20, 255, 255, 255),
                        Color.FromArgb(5, 255, 255, 255),
                        LinearGradientMode.Vertical))
                    {
                        g.FillPath(overlayBrush, overlayPath);
                    }
                }

                // Premium border with glow effect
                var borderColor = _isHovered ?
                    Color.FromArgb(Math.Min(255, _accentColor.R + 60),
                                  Math.Min(255, _accentColor.G + 60),
                                  Math.Min(255, _accentColor.B + 60)) : _accentColor;

                // Outer glow
                using (var glowPen = new Pen(Color.FromArgb(40, borderColor), 3))
                {
                    g.DrawPath(glowPen, path);
                }

                // Main border
                using (var borderPen = new Pen(borderColor, 2))
                {
                    g.DrawPath(borderPen, path);
                }

                // Inner highlight
                var innerRect = new Rectangle(cardRect.X + 2, cardRect.Y + 2, cardRect.Width - 4, cardRect.Height - 4);
                using (var innerPath = CreateRoundedRectanglePath(innerRect, cornerRadius - 2))
                using (var innerPen = new Pen(Color.FromArgb(30, 255, 255, 255), 1))
                {
                    g.DrawPath(innerPen, innerPath);
                }
            }

            // Professional icon with background circle
            var iconCenterX = cardRect.X + 30;
            var iconCenterY = cardRect.Y + 35;
            var iconCircleRect = new Rectangle(iconCenterX - 25, iconCenterY - 25, 50, 50);

            // Icon background with gradient
            using (var iconBgBrush = new LinearGradientBrush(
                iconCircleRect,
                Color.FromArgb(60, _accentColor),
                Color.FromArgb(30, _accentColor),
                LinearGradientMode.ForwardDiagonal))
            {
                g.FillEllipse(iconBgBrush, iconCircleRect);
            }

            // Icon border
            using (var iconBorderPen = new Pen(_accentColor, 2))
            {
                g.DrawEllipse(iconBorderPen, iconCircleRect);
            }

            // Icon text
            using (var iconBrush = new SolidBrush(Color.White))
            using (var iconFont = new Font("Segoe UI Emoji", 20, FontStyle.Bold))
            {
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(_icon, iconFont, iconBrush, iconCircleRect, iconFormat);
            }

            // Professional title with shadow
            var titleRect = new Rectangle(cardRect.X + 15, cardRect.Y + 90, cardRect.Width - 30, 30);

            // Title shadow
            var shadowTitleRect = new Rectangle(titleRect.X + 1, titleRect.Y + 1, titleRect.Width, titleRect.Height);
            using (var shadowBrush = new SolidBrush(Color.FromArgb(80, 0, 0, 0)))
            using (var titleFont = new Font("Segoe UI", 16, FontStyle.Bold))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    Trimming = StringTrimming.EllipsisCharacter
                };
                g.DrawString(_title, titleFont, shadowBrush, shadowTitleRect, titleFormat);
            }

            // Main title
            using (var titleBrush = new SolidBrush(Color.White))
            using (var titleFont = new Font("Segoe UI", 16, FontStyle.Bold))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    Trimming = StringTrimming.EllipsisCharacter
                };
                g.DrawString(_title, titleFont, titleBrush, titleRect, titleFormat);
            }

            // Premium tweak count badge
            var badgeRect = new Rectangle(cardRect.X + 15, cardRect.Y + 125, 100, 24);
            using (var badgePath = CreateRoundedRectanglePath(badgeRect, 12))
            {
                // Badge gradient background
                using (var badgeBrush = new LinearGradientBrush(
                    badgeRect,
                    Color.FromArgb(80, _accentColor),
                    Color.FromArgb(40, _accentColor),
                    LinearGradientMode.Vertical))
                {
                    g.FillPath(badgeBrush, badgePath);
                }

                // Badge border
                using (var badgeBorderPen = new Pen(_accentColor, 1.5f))
                {
                    g.DrawPath(badgeBorderPen, badgePath);
                }

                // Badge text
                using (var badgeTextBrush = new SolidBrush(Color.White))
                using (var badgeFont = new Font("Segoe UI", 10, FontStyle.Bold))
                {
                    var badgeFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    g.DrawString($"{_tweakCount} tweaks", badgeFont, badgeTextBrush, badgeRect, badgeFormat);
                }
            }

            // Professional status indicator
            var statusRect = new Rectangle(cardRect.Right - 70, cardRect.Y + 127, 55, 20);
            using (var statusBrush = new SolidBrush(Color.FromArgb(0, 255, 127)))
            using (var statusFont = new Font("Segoe UI", 9, FontStyle.Bold))
            {
                var statusFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString("● READY", statusFont, statusBrush, statusRect, statusFormat);
            }
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            _isPressed = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _isPressed = true;
            Invalidate();
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }
    }
}
