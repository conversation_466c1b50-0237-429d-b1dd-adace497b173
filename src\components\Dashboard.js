import React, { useState, useEffect } from 'react';
import { 
  Cpu, 
  HardDrive, 
  Zap, 
  Activity, 
  Shield, 
  Gauge,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import './Dashboard.css';

const Dashboard = ({ systemInfo, isAdmin }) => {
  const [performanceScore, setPerformanceScore] = useState(0);
  const [optimizationStatus, setOptimizationStatus] = useState('checking');
  const [quickStats, setQuickStats] = useState({
    fpsBoost: 0,
    ramFreed: 0,
    processesOptimized: 0,
    tweaksApplied: 0
  });

  useEffect(() => {
    // Simulate performance calculation
    setTimeout(() => {
      setPerformanceScore(87);
      setOptimizationStatus('optimized');
      setQuickStats({
        fpsBoost: 247,
        ramFreed: 2.4,
        processesOptimized: 43,
        tweaksApplied: 156
      });
    }, 1500);
  }, []);

  const getPerformanceColor = (score) => {
    if (score >= 80) return '#00ff88';
    if (score >= 60) return '#ffa502';
    return '#ff4757';
  };

  const getOptimizationStatusInfo = () => {
    switch (optimizationStatus) {
      case 'checking':
        return { icon: Activity, color: '#74b9ff', text: 'Analyzing System...' };
      case 'optimized':
        return { icon: CheckCircle, color: '#00ff88', text: 'System Optimized' };
      case 'needs-optimization':
        return { icon: AlertTriangle, color: '#ffa502', text: 'Optimization Recommended' };
      default:
        return { icon: Info, color: '#888', text: 'Unknown Status' };
    }
  };

  const statusInfo = getOptimizationStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>System Dashboard</h1>
        <p>Real-time overview of your system's performance and optimization status</p>
      </div>

      <div className="dashboard-grid">
        {/* Performance Score */}
        <div className="card performance-card">
          <div className="card-header">
            <h3>Performance Score</h3>
            <Gauge size={20} />
          </div>
          <div className="performance-score">
            <div className="score-circle">
              <svg viewBox="0 0 100 100" className="score-svg">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="rgba(255,255,255,0.1)"
                  strokeWidth="8"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke={getPerformanceColor(performanceScore)}
                  strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray={`${performanceScore * 2.83} 283`}
                  transform="rotate(-90 50 50)"
                  className="score-progress"
                />
              </svg>
              <div className="score-text">
                <span className="score-number">{performanceScore}</span>
                <span className="score-label">/ 100</span>
              </div>
            </div>
            <div className="score-status">
              <StatusIcon size={16} style={{ color: statusInfo.color }} />
              <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="card system-info-card">
          <div className="card-header">
            <h3>System Information</h3>
            <Cpu size={20} />
          </div>
          <div className="system-info">
            {systemInfo ? (
              <>
                <div className="info-item">
                  <span className="info-label">CPU:</span>
                  <span className="info-value">{systemInfo.cpu.brand}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Cores:</span>
                  <span className="info-value">{systemInfo.cpu.cores} ({systemInfo.cpu.physicalCores} physical)</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Memory:</span>
                  <span className="info-value">{(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB</span>
                </div>
                <div className="info-item">
                  <span className="info-label">GPU:</span>
                  <span className="info-value">{systemInfo.graphics[0]?.model || 'Unknown'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">OS:</span>
                  <span className="info-value">{systemInfo.os.distro}</span>
                </div>
              </>
            ) : (
              <div className="loading-info">Loading system information...</div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="card stats-card">
          <div className="card-header">
            <h3>Optimization Results</h3>
            <TrendingUp size={20} />
          </div>
          <div className="quick-stats">
            <div className="stat-item">
              <div className="stat-icon fps">
                <Zap size={16} />
              </div>
              <div className="stat-content">
                <div className="stat-value">+{quickStats.fpsBoost}%</div>
                <div className="stat-label">FPS Boost</div>
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-icon ram">
                <HardDrive size={16} />
              </div>
              <div className="stat-content">
                <div className="stat-value">{quickStats.ramFreed} GB</div>
                <div className="stat-label">RAM Freed</div>
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-icon processes">
                <Activity size={16} />
              </div>
              <div className="stat-content">
                <div className="stat-value">{quickStats.processesOptimized}</div>
                <div className="stat-label">Processes Optimized</div>
              </div>
            </div>
            <div className="stat-item">
              <div className="stat-icon tweaks">
                <Shield size={16} />
              </div>
              <div className="stat-content">
                <div className="stat-value">{quickStats.tweaksApplied}</div>
                <div className="stat-label">Tweaks Applied</div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card actions-card">
          <div className="card-header">
            <h3>Quick Actions</h3>
            <Zap size={20} />
          </div>
          <div className="quick-actions">
            <button className="action-btn primary" disabled={!isAdmin}>
              <Zap size={16} />
              <span>Quick Optimize</span>
            </button>
            <button className="action-btn secondary">
              <Activity size={16} />
              <span>Performance Monitor</span>
            </button>
            <button className="action-btn secondary" disabled={!isAdmin}>
              <Shield size={16} />
              <span>Advanced Tweaks</span>
            </button>
            <button className="action-btn danger" disabled={!isAdmin}>
              <HardDrive size={16} />
              <span>System Cleanup</span>
            </button>
          </div>
        </div>
      </div>

      {!isAdmin && (
        <div className="admin-notice">
          <AlertTriangle size={20} />
          <div>
            <strong>Limited Mode Active</strong>
            <p>Some features require administrator privileges. Restart the application as administrator to unlock all optimization features.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
