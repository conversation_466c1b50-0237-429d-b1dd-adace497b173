{"ast": null, "code": "import { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\nconst frameloopDriver = update => {\n  const passTimestamp = ({\n    timestamp\n  }) => update(timestamp);\n  return {\n    start: () => frame.update(passTimestamp, true),\n    stop: () => cancelFrame(passTimestamp),\n    /**\n     * If we're processing this frame we can use the\n     * framelocked timestamp to keep things in sync.\n     */\n    now: () => frameData.isProcessing ? frameData.timestamp : performance.now()\n  };\n};\nexport { frameloopDriver };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "frameloopDriver", "update", "passTimestamp", "timestamp", "start", "stop", "now", "isProcessing", "performance"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from '../../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: () => frame.update(passTimestamp, true),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => frameData.isProcessing ? frameData.timestamp : performance.now(),\n    };\n};\n\nexport { frameloopDriver };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,8BAA8B;AAE5E,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAChC,MAAMC,aAAa,GAAGA,CAAC;IAAEC;EAAU,CAAC,KAAKF,MAAM,CAACE,SAAS,CAAC;EAC1D,OAAO;IACHC,KAAK,EAAEA,CAAA,KAAMP,KAAK,CAACI,MAAM,CAACC,aAAa,EAAE,IAAI,CAAC;IAC9CG,IAAI,EAAEA,CAAA,KAAMP,WAAW,CAACI,aAAa,CAAC;IACtC;AACR;AACA;AACA;IACQI,GAAG,EAAEA,CAAA,KAAMP,SAAS,CAACQ,YAAY,GAAGR,SAAS,CAACI,SAAS,GAAGK,WAAW,CAACF,GAAG,CAAC;EAC9E,CAAC;AACL,CAAC;AAED,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}