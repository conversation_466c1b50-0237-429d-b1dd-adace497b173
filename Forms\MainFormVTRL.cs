using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainFormVTRL : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebar;
        private Panel _mainContent;
        private string _currentPage = "HOME";

        public MainFormVTRL()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupVTRLDesign();
            CreateLayout();

            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.SupportsTransparentBackColor, true);

            this.AutoScaleDimensions = new SizeF(96F, 96F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainFormVTRL";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

            this.ResumeLayout(false);
        }

        private void SetupVTRLDesign()
        {
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create title bar
            CreateTitleBar();

            // Create sidebar like VTRL
            CreateSidebar();

            // Create main content area
            CreateMainContent();
        }

        private void CreateTitleBar()
        {
            var titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.FromArgb(25, 25, 28)
            };

            var closeButton = new Button
            {
                Text = "×",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 50, 5)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 100, 100);
            closeButton.Click += (s, e) => this.Close();

            var minimizeButton = new Button
            {
                Text = "−",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 90, 5)
            };
            minimizeButton.FlatAppearance.BorderSize = 0;
            minimizeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(60, 60, 65);
            minimizeButton.Click += (s, e) => this.WindowState = FormWindowState.Minimized;

            titleBar.Controls.AddRange(new Control[] { closeButton, minimizeButton });
            this.Controls.Add(titleBar);
        }

        private void CreateSidebar()
        {
            _sidebar = new Panel
            {
                Size = new Size(200, this.Height - 40),
                Location = new Point(0, 40),
                BackColor = Color.FromArgb(25, 25, 28)
            };

            // Logo section
            var logoPanel = new Panel
            {
                Size = new Size(200, 80),
                Location = new Point(0, 0),
                BackColor = Color.Transparent
            };

            var logoLabel = new Label
            {
                Text = "R",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(138, 43, 226),
                Location = new Point(20, 20),
                Size = new Size(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            logoLabel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                var rect = new Rectangle(0, 0, logoLabel.Width - 1, logoLabel.Height - 1);
                using (var brush = new SolidBrush(Color.FromArgb(138, 43, 226)))
                {
                    g.FillRectangle(brush, rect);
                }
            };

            var brandLabel = new Label
            {
                Text = "RODEY PREMIUM",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(70, 25),
                AutoSize = true
            };

            logoPanel.Controls.AddRange(new Control[] { logoLabel, brandLabel });

            // Menu sections
            var menuY = 100;
            var menuItems = new[]
            {
                new { Text = "🏠 Home", Category = "HOME", IsActive = true },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Tweaks", Category = "HEADER", IsActive = false },
                new { Text = "🔧 Tweaks", Category = "TWEAKS", IsActive = false },
                new { Text = "⚡ Powerplan", Category = "POWER", IsActive = false },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Network", Category = "HEADER", IsActive = false },
                new { Text = "🌐 Network Tweaks", Category = "NETWORK", IsActive = false },
                new { Text = "📡 Network Scripts", Category = "SCRIPTS", IsActive = false },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Windows", Category = "HEADER", IsActive = false },
                new { Text = "🧹 Cleaner", Category = "CLEANER", IsActive = false },
                new { Text = "🗑️ Debloat", Category = "DEBLOAT", IsActive = false },
                new { Text = "🚀 Startup", Category = "STARTUP", IsActive = false }
            };

            foreach (var item in menuItems)
            {
                if (item.Category == "SEPARATOR")
                {
                    menuY += 10;
                    continue;
                }

                if (item.Category == "HEADER")
                {
                    var headerLabel = new Label
                    {
                        Text = item.Text,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular),
                        ForeColor = Color.FromArgb(120, 120, 120),
                        Location = new Point(20, menuY),
                        AutoSize = true
                    };
                    _sidebar.Controls.Add(headerLabel);
                    menuY += 25;
                }
                else
                {
                    var isActive = item.Category == "HOME";
                    var menuButton = new Button
                    {
                        Text = item.Text,
                        Font = new Font("Segoe UI", 10, FontStyle.Regular),
                        ForeColor = isActive ? Color.FromArgb(138, 43, 226) : Color.FromArgb(180, 180, 180),
                        BackColor = isActive ? Color.FromArgb(40, 40, 45) : Color.Transparent,
                        FlatStyle = FlatStyle.Flat,
                        Size = new Size(180, 35),
                        Location = new Point(10, menuY),
                        TextAlign = ContentAlignment.MiddleLeft,
                        Padding = new Padding(10, 0, 0, 0),
                        Tag = item.Category
                    };
                    menuButton.FlatAppearance.BorderSize = 0;
                    menuButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(40, 40, 45);

                    if (isActive)
                    {
                        menuButton.FlatAppearance.BorderColor = Color.FromArgb(138, 43, 226);
                        menuButton.FlatAppearance.BorderSize = 1;
                    }

                    menuButton.Click += (s, e) => SwitchPage(item.Category);
                    _sidebar.Controls.Add(menuButton);
                    menuY += 40;
                }
            }

            _sidebar.Controls.Add(logoPanel);
            this.Controls.Add(_sidebar);
        }

        private void SwitchPage(string page)
        {
            _currentPage = page;

            // Update sidebar button states
            foreach (Control control in _sidebar.Controls)
            {
                if (control is Button btn && btn.Tag != null)
                {
                    var isActive = btn.Tag.ToString() == page;
                    btn.ForeColor = isActive ? Color.FromArgb(138, 43, 226) : Color.FromArgb(180, 180, 180);
                    btn.BackColor = isActive ? Color.FromArgb(40, 40, 45) : Color.Transparent;
                    btn.FlatAppearance.BorderSize = isActive ? 1 : 0;
                }
            }

            // Update main content based on page
            CreateMainContent();
        }

        private void CreateMainContent()
        {
            if (_mainContent != null)
            {
                this.Controls.Remove(_mainContent);
                _mainContent.Dispose();
            }

            _mainContent = new Panel
            {
                Size = new Size(this.Width - 200, this.Height - 40),
                Location = new Point(200, 40),
                BackColor = Color.FromArgb(32, 32, 36)
            };

            if (_currentPage == "HOME")
            {
                CreateHomePage();
            }
            else if (_currentPage == "TWEAKS")
            {
                CreateTweaksPage();
            }
            else
            {
                CreatePlaceholderPage(_currentPage);
            }

            this.Controls.Add(_mainContent);
        }

        private void CreateHomePage()
        {
            // Header greeting
            var greetingLabel = new Label
            {
                Text = "🌙 Good Evening, Guest",
                Font = new Font("Segoe UI", 20, FontStyle.Regular),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            // Quick Info section
            var quickInfoLabel = new Label
            {
                Text = "Quick Info",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 90),
                AutoSize = true
            };

            _mainContent.Controls.AddRange(new Control[] { greetingLabel, quickInfoLabel });
        }

        private void CreateTweaksPage()
        {
            var titleLabel = new Label
            {
                Text = "System Tweaks",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Apply powerful system optimizations for maximum performance",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            // Create tweak cards
            var tweakCards = new[]
            {
                new { Title = "🚀 GAMING BEAST", Description = "Ultimate gaming optimizations", Tweaks = "2,847", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "⚡ CPU OPTIMIZER", Description = "Maximum CPU performance", Tweaks = "1,234", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🎮 GPU OPTIMIZER", Description = "Graphics card optimization", Tweaks = "1,567", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🧠 MEMORY BOOST", Description = "RAM optimization tweaks", Tweaks = "892", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "🌐 NETWORK BOOST", Description = "Network performance", Tweaks = "456", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "💾 STORAGE BOOST", Description = "Disk optimization", Tweaks = "678", Color = Color.FromArgb(50, 205, 50) },
                new { Title = "⚡ POWER BOOST", Description = "Power management", Tweaks = "234", Color = Color.FromArgb(255, 215, 0) },
                new { Title = "⚙️ REGISTRY CLEAN", Description = "Registry optimization", Tweaks = "1,123", Color = Color.FromArgb(220, 20, 60) },
                new { Title = "🔒 PRIVACY BOOST", Description = "Privacy optimization", Tweaks = "789", Color = Color.FromArgb(75, 0, 130) },
                new { Title = "🧹 SYSTEM DEBLOAT", Description = "Remove bloatware", Tweaks = "3,456", Color = Color.FromArgb(34, 139, 34) },
                new { Title = "⚡ LATENCY BOOST", Description = "Reduce input lag", Tweaks = "567", Color = Color.FromArgb(255, 140, 0) },
                new { Title = "🔥 ADVANCED GPU", Description = "Advanced GPU tweaks", Tweaks = "890", Color = Color.FromArgb(199, 21, 133) },
                new { Title = "⚙️ BIOS OPTIMIZER", Description = "BIOS-level tweaks", Tweaks = "2,156", Color = Color.FromArgb(255, 20, 147) },
                new { Title = "🎯 EXTREME MODE", Description = "Maximum performance", Tweaks = "4,567", Color = Color.FromArgb(255, 0, 0) }
            };

            var cardY = 120;
            var cardX = 40;
            var cardWidth = 280;
            var cardHeight = 100;
            var cardsPerRow = 4;

            for (int i = 0; i < tweakCards.Length; i++)
            {
                var card = tweakCards[i];
                var x = cardX + (i % cardsPerRow) * (cardWidth + 20);
                var y = cardY + (i / cardsPerRow) * (cardHeight + 20);

                var cardPanel = new Panel
                {
                    Size = new Size(cardWidth, cardHeight),
                    Location = new Point(x, y),
                    BackColor = Color.FromArgb(40, 40, 45),
                    Cursor = Cursors.Hand
                };

                cardPanel.Paint += (s, e) =>
                {
                    var g = e.Graphics;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    var rect = new Rectangle(0, 0, cardPanel.Width - 1, cardPanel.Height - 1);
                    using (var path = CreateRoundedRectanglePath(rect, 8))
                    {
                        using (var brush = new SolidBrush(Color.FromArgb(40, 40, 45)))
                        {
                            g.FillPath(brush, path);
                        }
                        using (var pen = new Pen(card.Color, 2))
                        {
                            g.DrawPath(pen, path);
                        }
                    }
                };

                var titleLabel2 = new Label
                {
                    Text = card.Title,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(15, 15),
                    Size = new Size(250, 20),
                    BackColor = Color.Transparent
                };

                var descLabel = new Label
                {
                    Text = card.Description,
                    Font = new Font("Segoe UI", 9, FontStyle.Regular),
                    ForeColor = Color.FromArgb(180, 180, 180),
                    Location = new Point(15, 35),
                    Size = new Size(250, 15),
                    BackColor = Color.Transparent
                };

                var tweaksLabel = new Label
                {
                    Text = $"{card.Tweaks} tweaks",
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    ForeColor = card.Color,
                    Location = new Point(15, 60),
                    Size = new Size(100, 20),
                    BackColor = Color.Transparent
                };

                var applyButton = new Button
                {
                    Text = "APPLY",
                    Font = new Font("Segoe UI", 9, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = card.Color,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(80, 25),
                    Location = new Point(180, 60)
                };
                applyButton.FlatAppearance.BorderSize = 0;

                cardPanel.Controls.AddRange(new Control[] { titleLabel2, descLabel, tweaksLabel, applyButton });
                _mainContent.Controls.Add(cardPanel);
            }

            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreatePlaceholderPage(string pageName)
        {
            switch (pageName)
            {
                case "POWER":
                    CreatePowerPage();
                    break;
                case "NETWORK":
                    CreateNetworkPage();
                    break;
                case "SCRIPTS":
                    CreateScriptsPage();
                    break;
                case "CLEANER":
                    CreateCleanerPage();
                    break;
                case "DEBLOAT":
                    CreateDebloatPage();
                    break;
                case "STARTUP":
                    CreateStartupPage();
                    break;
                default:
                    CreateDefaultPage(pageName);
                    break;
            }
        }

        private void CreatePowerPage()
        {
            var titleLabel = new Label
            {
                Text = "⚡ Power Management",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Optimize power settings for maximum performance",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            // Power plan cards
            var powerPlans = new[]
            {
                new { Name = "🔥 ULTIMATE PERFORMANCE", Description = "Maximum CPU/GPU performance", Status = "RECOMMENDED", Color = Color.FromArgb(255, 0, 0) },
                new { Name = "⚡ HIGH PERFORMANCE", Description = "High performance mode", Status = "ACTIVE", Color = Color.FromArgb(255, 140, 0) },
                new { Name = "⚖️ BALANCED", Description = "Balance performance and power", Status = "DEFAULT", Color = Color.FromArgb(30, 144, 255) },
                new { Name = "🔋 POWER SAVER", Description = "Save battery power", Status = "DISABLED", Color = Color.FromArgb(50, 205, 50) }
            };

            var cardY = 120;
            for (int i = 0; i < powerPlans.Length; i++)
            {
                var plan = powerPlans[i];
                var cardPanel = new Panel
                {
                    Size = new Size(600, 80),
                    Location = new Point(40, cardY + i * 90),
                    BackColor = Color.FromArgb(40, 40, 45)
                };

                var nameLabel = new Label
                {
                    Text = plan.Name,
                    Font = new Font("Segoe UI", 14, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(20, 15),
                    AutoSize = true
                };

                var descLabel = new Label
                {
                    Text = plan.Description,
                    Font = new Font("Segoe UI", 10, FontStyle.Regular),
                    ForeColor = Color.FromArgb(180, 180, 180),
                    Location = new Point(20, 40),
                    AutoSize = true
                };

                var statusLabel = new Label
                {
                    Text = plan.Status,
                    Font = new Font("Segoe UI", 9, FontStyle.Bold),
                    ForeColor = plan.Color,
                    Location = new Point(450, 20),
                    AutoSize = true
                };

                var applyButton = new Button
                {
                    Text = "APPLY",
                    Font = new Font("Segoe UI", 9, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = plan.Color,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(80, 30),
                    Location = new Point(500, 40)
                };
                applyButton.FlatAppearance.BorderSize = 0;

                cardPanel.Controls.AddRange(new Control[] { nameLabel, descLabel, statusLabel, applyButton });
                _mainContent.Controls.Add(cardPanel);
            }

            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateNetworkPage()
        {
            var titleLabel = new Label
            {
                Text = "🌐 Network Optimization",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Optimize network settings for gaming and streaming",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            // Network optimization cards
            var networkTweaks = new[]
            {
                new { Title = "🎮 GAMING NETWORK", Description = "Optimize for gaming", Tweaks = "234", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "📺 STREAMING BOOST", Description = "Optimize for streaming", Tweaks = "156", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "⚡ LATENCY REDUCER", Description = "Reduce network latency", Tweaks = "89", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🌐 DNS OPTIMIZER", Description = "Optimize DNS settings", Tweaks = "45", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "🔒 FIREWALL BOOST", Description = "Optimize Windows Firewall", Tweaks = "67", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "📡 WIFI OPTIMIZER", Description = "Optimize wireless settings", Tweaks = "123", Color = Color.FromArgb(50, 205, 50) }
            };

            CreateTweakGrid(networkTweaks, 120);
            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateScriptsPage()
        {
            var titleLabel = new Label
            {
                Text = "📡 Network Scripts",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Advanced network optimization scripts",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            var scripts = new[]
            {
                new { Title = "🚀 TCP OPTIMIZER", Description = "Advanced TCP optimization", Tweaks = "456", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "⚡ BANDWIDTH BOOST", Description = "Maximize bandwidth usage", Tweaks = "234", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🎯 PING REDUCER", Description = "Minimize ping and jitter", Tweaks = "123", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🌐 QOS OPTIMIZER", Description = "Quality of Service tweaks", Tweaks = "89", Color = Color.FromArgb(0, 191, 255) }
            };

            CreateTweakGrid(scripts, 120);
            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateCleanerPage()
        {
            var titleLabel = new Label
            {
                Text = "🧹 System Cleaner",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Clean temporary files and optimize system storage",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            var cleanerTweaks = new[]
            {
                new { Title = "🗑️ TEMP CLEANER", Description = "Clean temporary files", Tweaks = "567", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "📁 CACHE CLEANER", Description = "Clear system cache", Tweaks = "234", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🔄 LOG CLEANER", Description = "Clean system logs", Tweaks = "123", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🗂️ REGISTRY CLEANER", Description = "Clean registry entries", Tweaks = "456", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "💾 DISK CLEANER", Description = "Deep disk cleanup", Tweaks = "789", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "🔍 DUPLICATE FINDER", Description = "Find duplicate files", Tweaks = "345", Color = Color.FromArgb(50, 205, 50) }
            };

            CreateTweakGrid(cleanerTweaks, 120);
            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateDebloatPage()
        {
            var titleLabel = new Label
            {
                Text = "🗑️ System Debloat",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Remove bloatware and unnecessary Windows components",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            var debloatTweaks = new[]
            {
                new { Title = "🗑️ WINDOWS BLOAT", Description = "Remove Windows bloatware", Tweaks = "1,234", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "📱 UWP APPS", Description = "Remove UWP applications", Tweaks = "567", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🌐 EDGE REMOVAL", Description = "Remove Microsoft Edge", Tweaks = "89", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🔍 CORTANA DISABLE", Description = "Disable Cortana completely", Tweaks = "45", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "📊 TELEMETRY BLOCK", Description = "Block Windows telemetry", Tweaks = "234", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "🎮 XBOX REMOVAL", Description = "Remove Xbox components", Tweaks = "123", Color = Color.FromArgb(50, 205, 50) }
            };

            CreateTweakGrid(debloatTweaks, 120);
            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateStartupPage()
        {
            var titleLabel = new Label
            {
                Text = "🚀 Startup Optimizer",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var subtitleLabel = new Label
            {
                Text = "Optimize system startup and boot performance",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            var startupTweaks = new[]
            {
                new { Title = "⚡ BOOT OPTIMIZER", Description = "Optimize boot sequence", Tweaks = "345", Color = Color.FromArgb(255, 0, 255) },
                new { Title = "🚀 STARTUP APPS", Description = "Manage startup programs", Tweaks = "234", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "⏱️ BOOT TIME", Description = "Reduce boot time", Tweaks = "123", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🔧 SERVICES OPTIMIZER", Description = "Optimize Windows services", Tweaks = "456", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "💾 FAST STARTUP", Description = "Enable fast startup", Tweaks = "67", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "🔄 AUTO UPDATES", Description = "Manage Windows updates", Tweaks = "89", Color = Color.FromArgb(50, 205, 50) }
            };

            CreateTweakGrid(startupTweaks, 120);
            _mainContent.Controls.AddRange(new Control[] { titleLabel, subtitleLabel });
        }

        private void CreateDefaultPage(string pageName)
        {
            var titleLabel = new Label
            {
                Text = $"{pageName} Page",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var descLabel = new Label
            {
                Text = "This page is under development.",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            _mainContent.Controls.AddRange(new Control[] { titleLabel, descLabel });
        }

        private void CreateTweakGrid(dynamic[] tweaks, int startY)
        {
            var cardWidth = 280;
            var cardHeight = 100;
            var cardsPerRow = 4;
            var cardX = 40;

            for (int i = 0; i < tweaks.Length; i++)
            {
                var tweak = tweaks[i];
                var x = cardX + (i % cardsPerRow) * (cardWidth + 20);
                var y = startY + (i / cardsPerRow) * (cardHeight + 20);

                var cardPanel = new Panel
                {
                    Size = new Size(cardWidth, cardHeight),
                    Location = new Point(x, y),
                    BackColor = Color.FromArgb(40, 40, 45),
                    Cursor = Cursors.Hand
                };

                cardPanel.Paint += (s, e) =>
                {
                    var g = e.Graphics;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    var rect = new Rectangle(0, 0, cardPanel.Width - 1, cardPanel.Height - 1);
                    using (var path = CreateRoundedRectanglePath(rect, 8))
                    {
                        using (var brush = new SolidBrush(Color.FromArgb(40, 40, 45)))
                        {
                            g.FillPath(brush, path);
                        }
                        using (var pen = new Pen(tweak.Color, 2))
                        {
                            g.DrawPath(pen, path);
                        }
                    }
                };

                var titleLabel = new Label
                {
                    Text = tweak.Title,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(15, 15),
                    Size = new Size(250, 20),
                    BackColor = Color.Transparent
                };

                var descLabel = new Label
                {
                    Text = tweak.Description,
                    Font = new Font("Segoe UI", 9, FontStyle.Regular),
                    ForeColor = Color.FromArgb(180, 180, 180),
                    Location = new Point(15, 35),
                    Size = new Size(250, 15),
                    BackColor = Color.Transparent
                };

                var tweaksLabel = new Label
                {
                    Text = $"{tweak.Tweaks} tweaks",
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    ForeColor = tweak.Color,
                    Location = new Point(15, 60),
                    Size = new Size(100, 20),
                    BackColor = Color.Transparent
                };

                var applyButton = new Button
                {
                    Text = "APPLY",
                    Font = new Font("Segoe UI", 9, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = tweak.Color,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(80, 25),
                    Location = new Point(180, 60)
                };
                applyButton.FlatAppearance.BorderSize = 0;

                cardPanel.Controls.AddRange(new Control[] { titleLabel, descLabel, tweaksLabel, applyButton });
                _mainContent.Controls.Add(cardPanel);
            }
        }

        private System.Drawing.Drawing2D.GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
