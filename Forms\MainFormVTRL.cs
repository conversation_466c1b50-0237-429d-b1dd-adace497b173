using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainFormVTRL : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebar;
        private Panel _mainContent;
        private string _currentPage = "HOME";

        public MainFormVTRL()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupVTRLDesign();
            CreateLayout();

            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.SupportsTransparentBackColor, true);

            this.AutoScaleDimensions = new SizeF(96F, 96F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainFormVTRL";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

            this.ResumeLayout(false);
        }

        private void SetupVTRLDesign()
        {
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create title bar
            CreateTitleBar();

            // Create sidebar like VTRL
            CreateSidebar();

            // Create main content area
            CreateMainContent();
        }

        private void CreateTitleBar()
        {
            var titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.FromArgb(25, 25, 28)
            };

            var closeButton = new Button
            {
                Text = "×",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 50, 5)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 100, 100);
            closeButton.Click += (s, e) => this.Close();

            var minimizeButton = new Button
            {
                Text = "−",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 90, 5)
            };
            minimizeButton.FlatAppearance.BorderSize = 0;
            minimizeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(60, 60, 65);
            minimizeButton.Click += (s, e) => this.WindowState = FormWindowState.Minimized;

            titleBar.Controls.AddRange(new Control[] { closeButton, minimizeButton });
            this.Controls.Add(titleBar);
        }

        private void CreateSidebar()
        {
            _sidebar = new Panel
            {
                Size = new Size(200, this.Height - 40),
                Location = new Point(0, 40),
                BackColor = Color.FromArgb(25, 25, 28)
            };

            // Logo section
            var logoPanel = new Panel
            {
                Size = new Size(200, 80),
                Location = new Point(0, 0),
                BackColor = Color.Transparent
            };

            var logoLabel = new Label
            {
                Text = "R",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(138, 43, 226),
                Location = new Point(20, 20),
                Size = new Size(40, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            logoLabel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                var rect = new Rectangle(0, 0, logoLabel.Width - 1, logoLabel.Height - 1);
                using (var brush = new SolidBrush(Color.FromArgb(138, 43, 226)))
                {
                    g.FillRectangle(brush, rect);
                }
            };

            var brandLabel = new Label
            {
                Text = "RODEY PREMIUM",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(70, 25),
                AutoSize = true
            };

            logoPanel.Controls.AddRange(new Control[] { logoLabel, brandLabel });

            // Menu sections
            var menuY = 100;
            var menuItems = new[]
            {
                new { Text = "🏠 Home", Category = "HOME", IsActive = true },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Tweaks", Category = "HEADER", IsActive = false },
                new { Text = "🔧 Tweaks", Category = "TWEAKS", IsActive = false },
                new { Text = "⚡ Powerplan", Category = "POWER", IsActive = false },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Network", Category = "HEADER", IsActive = false },
                new { Text = "🌐 Network Tweaks", Category = "NETWORK", IsActive = false },
                new { Text = "📡 Network Scripts", Category = "SCRIPTS", IsActive = false },
                new { Text = "", Category = "SEPARATOR", IsActive = false },
                new { Text = "Windows", Category = "HEADER", IsActive = false },
                new { Text = "🧹 Cleaner", Category = "CLEANER", IsActive = false },
                new { Text = "🗑️ Debloat", Category = "DEBLOAT", IsActive = false },
                new { Text = "🚀 Startup", Category = "STARTUP", IsActive = false }
            };

            foreach (var item in menuItems)
            {
                if (item.Category == "SEPARATOR")
                {
                    menuY += 10;
                    continue;
                }

                if (item.Category == "HEADER")
                {
                    var headerLabel = new Label
                    {
                        Text = item.Text,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular),
                        ForeColor = Color.FromArgb(120, 120, 120),
                        Location = new Point(20, menuY),
                        AutoSize = true
                    };
                    _sidebar.Controls.Add(headerLabel);
                    menuY += 25;
                }
                else
                {
                    var isActive = item.Category == "HOME";
                    var menuButton = new Button
                    {
                        Text = item.Text,
                        Font = new Font("Segoe UI", 10, FontStyle.Regular),
                        ForeColor = isActive ? Color.FromArgb(138, 43, 226) : Color.FromArgb(180, 180, 180),
                        BackColor = isActive ? Color.FromArgb(40, 40, 45) : Color.Transparent,
                        FlatStyle = FlatStyle.Flat,
                        Size = new Size(180, 35),
                        Location = new Point(10, menuY),
                        TextAlign = ContentAlignment.MiddleLeft,
                        Padding = new Padding(10, 0, 0, 0),
                        Tag = item.Category
                    };
                    menuButton.FlatAppearance.BorderSize = 0;
                    menuButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(40, 40, 45);

                    if (isActive)
                    {
                        menuButton.FlatAppearance.BorderColor = Color.FromArgb(138, 43, 226);
                        menuButton.FlatAppearance.BorderSize = 1;
                    }

                    menuButton.Click += (s, e) => SwitchPage(item.Category);
                    _sidebar.Controls.Add(menuButton);
                    menuY += 40;
                }
            }

            _sidebar.Controls.Add(logoPanel);
            this.Controls.Add(_sidebar);
        }

        private void SwitchPage(string page)
        {
            _currentPage = page;

            // Update sidebar button states
            foreach (Control control in _sidebar.Controls)
            {
                if (control is Button btn && btn.Tag != null)
                {
                    var isActive = btn.Tag.ToString() == page;
                    btn.ForeColor = isActive ? Color.FromArgb(138, 43, 226) : Color.FromArgb(180, 180, 180);
                    btn.BackColor = isActive ? Color.FromArgb(40, 40, 45) : Color.Transparent;
                    btn.FlatAppearance.BorderSize = isActive ? 1 : 0;
                }
            }

            // Update main content based on page
            CreateMainContent();
        }

        private void CreateMainContent()
        {
            if (_mainContent != null)
            {
                this.Controls.Remove(_mainContent);
                _mainContent.Dispose();
            }

            _mainContent = new Panel
            {
                Size = new Size(this.Width - 200, this.Height - 40),
                Location = new Point(200, 40),
                BackColor = Color.FromArgb(32, 32, 36)
            };

            if (_currentPage == "HOME")
            {
                CreateHomePage();
            }
            else if (_currentPage == "TWEAKS")
            {
                CreateTweaksPage();
            }
            else
            {
                CreatePlaceholderPage(_currentPage);
            }

            this.Controls.Add(_mainContent);
        }

        private void CreateHomePage()
        {
            // Header greeting
            var greetingLabel = new Label
            {
                Text = "🌙 Good Evening, Guest",
                Font = new Font("Segoe UI", 20, FontStyle.Regular),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            // Quick Info section
            var quickInfoLabel = new Label
            {
                Text = "Quick Info",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 90),
                AutoSize = true
            };

            _mainContent.Controls.AddRange(new Control[] { greetingLabel, quickInfoLabel });
        }

        private void CreateTweaksPage()
        {
            var titleLabel = new Label
            {
                Text = "System Tweaks",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            _mainContent.Controls.Add(titleLabel);
        }

        private void CreatePlaceholderPage(string pageName)
        {
            var titleLabel = new Label
            {
                Text = $"{pageName} Page",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var descLabel = new Label
            {
                Text = "This page is under development.",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            _mainContent.Controls.AddRange(new Control[] { titleLabel, descLabel });
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
