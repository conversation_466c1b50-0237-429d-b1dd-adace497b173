using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainFormVTRL : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebar;
        private Panel _mainContent;
        private Panel _titleBar;
        private string _currentPage = "HOME";
        private System.Windows.Forms.Timer _animationTimer;
        private int _animationFrame = 0;
        private Dictionary<string, Color> _pageColors;

        public MainFormVTRL()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();
            _animationTimer = new System.Windows.Forms.Timer { Interval = 50 };
            _animationTimer.Tick += AnimationTimer_Tick;
            _animationTimer.Start();

            InitializePageColors();
            InitializeComponent();
            SetupVTRLDesign();
            CreateLayout();

            _performanceMonitor.StartMonitoring();
        }

        private void InitializePageColors()
        {
            _pageColors = new Dictionary<string, Color>
            {
                { "HOME", Color.FromArgb(138, 43, 226) },
                { "TWEAKS", Color.FromArgb(255, 69, 0) },
                { "POWER", Color.FromArgb(255, 215, 0) },
                { "NETWORK", Color.FromArgb(30, 144, 255) },
                { "SCRIPTS", Color.FromArgb(50, 205, 50) },
                { "CLEANER", Color.FromArgb(220, 20, 60) },
                { "DEBLOAT", Color.FromArgb(255, 140, 0) },
                { "STARTUP", Color.FromArgb(75, 0, 130) }
            };
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            _animationFrame++;
            if (_animationFrame > 360) _animationFrame = 0;

            // Trigger repaints for animated elements
            _titleBar?.Invalidate();
            _sidebar?.Invalidate();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.SupportsTransparentBackColor, true);

            this.AutoScaleDimensions = new SizeF(96F, 96F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainFormVTRL";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

            this.ResumeLayout(false);
        }

        private void SetupVTRLDesign()
        {
            this.BackColor = Color.FromArgb(32, 32, 36);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create title bar
            CreateTitleBar();

            // Create sidebar like VTRL
            CreateSidebar();

            // Create main content area
            CreateMainContent();
        }

        private void CreateTitleBar()
        {
            _titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(20, 20, 24)
            };

            _titleBar.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated gradient background
                var angle = _animationFrame * 2;
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, _titleBar.Width, _titleBar.Height),
                    Color.FromArgb(30, 138, 43, 226),
                    Color.FromArgb(30, 255, 69, 0),
                    angle))
                {
                    g.FillRectangle(brush, 0, 0, _titleBar.Width, _titleBar.Height);
                }

                // Bottom border with glow effect
                using (var pen = new Pen(Color.FromArgb(138, 43, 226), 2))
                {
                    g.DrawLine(pen, 0, _titleBar.Height - 1, _titleBar.Width, _titleBar.Height - 1);
                }
            };

            // Animated logo
            var logoPanel = new Panel
            {
                Size = new Size(200, 50),
                Location = new Point(20, 0),
                BackColor = Color.Transparent
            };

            logoPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated "R" logo
                var logoRect = new Rectangle(10, 10, 30, 30);
                var pulseSize = (float)(Math.Sin(_animationFrame * 0.1) * 2 + 2);
                logoRect.Inflate((int)pulseSize, (int)pulseSize);

                using (var brush = new LinearGradientBrush(logoRect,
                    Color.FromArgb(138, 43, 226),
                    Color.FromArgb(255, 69, 0),
                    45f))
                {
                    g.FillEllipse(brush, logoRect);
                }

                // "R" text
                using (var font = new Font("Segoe UI", 16, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    var textRect = new RectangleF(logoRect.X, logoRect.Y, logoRect.Width, logoRect.Height);
                    var sf = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center };
                    g.DrawString("R", font, brush, textRect, sf);
                }

                // Brand text with glow
                using (var font = new Font("Segoe UI", 12, FontStyle.Bold))
                {
                    var brandText = "RODEY PREMIUM TWEAKER";
                    var textPos = new PointF(50, 15);

                    // Glow effect
                    for (int i = 1; i <= 3; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(50, 138, 43, 226)))
                        {
                            g.DrawString(brandText, font, glowBrush, textPos.X + i, textPos.Y + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(brandText, font, brush, textPos);
                    }
                }
            };

            // Performance indicator
            var perfPanel = new Panel
            {
                Size = new Size(300, 50),
                Location = new Point(this.Width - 400, 0),
                BackColor = Color.Transparent
            };

            perfPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // CPU usage bar
                var cpuRect = new Rectangle(10, 15, 80, 8);
                g.FillRectangle(new SolidBrush(Color.FromArgb(40, 40, 45)), cpuRect);

                var cpuUsage = Math.Min(100, Math.Max(0, 45 + Math.Sin(_animationFrame * 0.1) * 20)); // Simulated
                var cpuFillWidth = (int)(cpuRect.Width * cpuUsage / 100);
                var cpuColor = cpuUsage > 80 ? Color.FromArgb(255, 69, 0) : Color.FromArgb(50, 205, 50);
                g.FillRectangle(new SolidBrush(cpuColor), cpuRect.X, cpuRect.Y, cpuFillWidth, cpuRect.Height);

                // RAM usage bar
                var ramRect = new Rectangle(100, 15, 80, 8);
                g.FillRectangle(new SolidBrush(Color.FromArgb(40, 40, 45)), ramRect);

                var ramUsage = Math.Min(100, Math.Max(0, 60 + Math.Sin(_animationFrame * 0.15) * 15)); // Simulated
                var ramFillWidth = (int)(ramRect.Width * ramUsage / 100);
                var ramColor = ramUsage > 80 ? Color.FromArgb(255, 69, 0) : Color.FromArgb(30, 144, 255);
                g.FillRectangle(new SolidBrush(ramColor), ramRect.X, ramRect.Y, ramFillWidth, ramRect.Height);

                // Labels
                using (var font = new Font("Segoe UI", 8))
                using (var brush = new SolidBrush(Color.FromArgb(180, 180, 180)))
                {
                    g.DrawString($"CPU {cpuUsage:F0}%", font, brush, 10, 28);
                    g.DrawString($"RAM {ramUsage:F0}%", font, brush, 100, 28);
                }
            };

            // Window controls
            var closeButton = new Button
            {
                Text = "✕",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(45, 35),
                Location = new Point(this.Width - 50, 8)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 100, 100);
            closeButton.Click += (s, e) => this.Close();

            var minimizeButton = new Button
            {
                Text = "−",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(45, 35),
                Location = new Point(this.Width - 95, 8)
            };
            minimizeButton.FlatAppearance.BorderSize = 0;
            minimizeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(60, 60, 65);
            minimizeButton.Click += (s, e) => this.WindowState = FormWindowState.Minimized;

            _titleBar.Controls.AddRange(new Control[] { logoPanel, perfPanel, closeButton, minimizeButton });
            this.Controls.Add(_titleBar);

            // Make title bar draggable
            _titleBar.MouseDown += TitleBar_MouseDown;
            logoPanel.MouseDown += TitleBar_MouseDown;
            perfPanel.MouseDown += TitleBar_MouseDown;
        }

        private void TitleBar_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // Allow dragging the window
                const int WM_NCLBUTTONDOWN = 0xA1;
                const int HT_CAPTION = 0x2;
                ReleaseCapture();
                SendMessage(this.Handle, WM_NCLBUTTONDOWN, HT_CAPTION, 0);
            }
        }

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        public static extern bool ReleaseCapture();



        private void CreateSidebar()
        {
            _sidebar = new Panel
            {
                Size = new Size(240, this.Height - 50),
                Location = new Point(0, 50),
                BackColor = Color.FromArgb(22, 22, 26)
            };

            _sidebar.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated gradient overlay
                var alpha = (int)(Math.Sin(_animationFrame * 0.05) * 10 + 15);
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, _sidebar.Width, _sidebar.Height),
                    Color.FromArgb(alpha, 138, 43, 226),
                    Color.FromArgb(alpha, 255, 69, 0),
                    LinearGradientMode.Vertical))
                {
                    g.FillRectangle(brush, 0, 0, _sidebar.Width, _sidebar.Height);
                }

                // Right border with glow
                using (var pen = new Pen(Color.FromArgb(40, 40, 45), 1))
                {
                    g.DrawLine(pen, _sidebar.Width - 1, 0, _sidebar.Width - 1, _sidebar.Height);
                }
            };

            // User profile section
            var profilePanel = new Panel
            {
                Size = new Size(220, 60),
                Location = new Point(10, 20),
                BackColor = Color.Transparent
            };

            profilePanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Profile background with glow
                var profileRect = new Rectangle(0, 0, profilePanel.Width, profilePanel.Height);
                using (var brush = new LinearGradientBrush(profileRect,
                    Color.FromArgb(30, 40, 40, 45),
                    Color.FromArgb(30, 60, 60, 65),
                    LinearGradientMode.Vertical))
                {
                    using (var path = CreateRoundedRectanglePath(profileRect, 8))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Profile avatar
                var avatarRect = new Rectangle(15, 15, 30, 30);
                using (var brush = new LinearGradientBrush(avatarRect,
                    Color.FromArgb(138, 43, 226),
                    Color.FromArgb(255, 69, 0),
                    45f))
                {
                    g.FillEllipse(brush, avatarRect);
                }

                // Avatar text
                using (var font = new Font("Segoe UI", 12, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    var sf = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center };
                    g.DrawString("G", font, brush, avatarRect, sf);
                }

                // User info
                using (var font = new Font("Segoe UI", 11, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.DrawString("Guest User", font, brush, 55, 18);
                }

                using (var font = new Font("Segoe UI", 9))
                using (var brush = new SolidBrush(Color.FromArgb(180, 180, 180)))
                {
                    g.DrawString("Premium License", font, brush, 55, 35);
                }
            };

            // Menu sections
            var menuY = 100;
            var menuItems = new[]
            {
                new { Text = "🏠  Dashboard", Category = "HOME", IsActive = true, Icon = "🏠" },
                new { Text = "", Category = "SEPARATOR", IsActive = false, Icon = "" },
                new { Text = "OPTIMIZATION", Category = "HEADER", IsActive = false, Icon = "" },
                new { Text = "🚀  Gaming Beast", Category = "TWEAKS", IsActive = false, Icon = "🚀" },
                new { Text = "⚡  Power Plan", Category = "POWER", IsActive = false, Icon = "⚡" },
                new { Text = "", Category = "SEPARATOR", IsActive = false, Icon = "" },
                new { Text = "NETWORK", Category = "HEADER", IsActive = false, Icon = "" },
                new { Text = "🌐  Network Boost", Category = "NETWORK", IsActive = false, Icon = "🌐" },
                new { Text = "📡  Advanced Scripts", Category = "SCRIPTS", IsActive = false, Icon = "📡" },
                new { Text = "", Category = "SEPARATOR", IsActive = false, Icon = "" },
                new { Text = "SYSTEM", Category = "HEADER", IsActive = false, Icon = "" },
                new { Text = "🧹  System Cleaner", Category = "CLEANER", IsActive = false, Icon = "🧹" },
                new { Text = "🗑️  Debloater", Category = "DEBLOAT", IsActive = false, Icon = "🗑️" },
                new { Text = "🚀  Startup Manager", Category = "STARTUP", IsActive = false, Icon = "🚀" }
            };

            foreach (var item in menuItems)
            {
                if (item.Category == "SEPARATOR")
                {
                    menuY += 15;
                    continue;
                }

                if (item.Category == "HEADER")
                {
                    var headerPanel = new Panel
                    {
                        Size = new Size(220, 25),
                        Location = new Point(10, menuY),
                        BackColor = Color.Transparent
                    };

                    headerPanel.Paint += (s, e) =>
                    {
                        var g = e.Graphics;
                        g.SmoothingMode = SmoothingMode.AntiAlias;

                        // Header text with glow
                        using (var font = new Font("Segoe UI", 9, FontStyle.Bold))
                        {
                            // Glow effect
                            using (var glowBrush = new SolidBrush(Color.FromArgb(30, 138, 43, 226)))
                            {
                                g.DrawString(item.Text, font, glowBrush, 16, 6);
                            }

                            // Main text
                            using (var brush = new SolidBrush(Color.FromArgb(140, 140, 140)))
                            {
                                g.DrawString(item.Text, font, brush, 15, 5);
                            }
                        }

                        // Underline
                        using (var pen = new Pen(Color.FromArgb(60, 60, 65), 1))
                        {
                            g.DrawLine(pen, 15, 20, 200, 20);
                        }
                    };

                    _sidebar.Controls.Add(headerPanel);
                    menuY += 35;
                }
                else
                {
                    var isActive = item.Category == "HOME";
                    var menuButton = new Panel
                    {
                        Size = new Size(220, 45),
                        Location = new Point(10, menuY),
                        BackColor = Color.Transparent,
                        Cursor = Cursors.Hand,
                        Tag = item.Category
                    };

                    menuButton.Paint += (s, e) =>
                    {
                        var g = e.Graphics;
                        g.SmoothingMode = SmoothingMode.AntiAlias;

                        var buttonRect = new Rectangle(0, 0, menuButton.Width, menuButton.Height);
                        var currentActive = menuButton.Tag.ToString() == _currentPage;

                        // Background with hover effect
                        if (currentActive)
                        {
                            using (var brush = new LinearGradientBrush(buttonRect,
                                Color.FromArgb(40, 138, 43, 226),
                                Color.FromArgb(20, 255, 69, 0),
                                LinearGradientMode.Horizontal))
                            {
                                using (var path = CreateRoundedRectanglePath(buttonRect, 8))
                                {
                                    g.FillPath(brush, path);
                                }
                            }

                            // Active border
                            using (var pen = new Pen(Color.FromArgb(138, 43, 226), 2))
                            {
                                using (var path = CreateRoundedRectanglePath(buttonRect, 8))
                                {
                                    g.DrawPath(pen, path);
                                }
                            }
                        }

                        // Icon and text
                        using (var iconFont = new Font("Segoe UI", 14))
                        using (var textFont = new Font("Segoe UI", 11, FontStyle.Regular))
                        {
                            var iconColor = currentActive ? Color.FromArgb(138, 43, 226) : Color.FromArgb(180, 180, 180);
                            var textColor = currentActive ? Color.White : Color.FromArgb(200, 200, 200);

                            using (var iconBrush = new SolidBrush(iconColor))
                            using (var textBrush = new SolidBrush(textColor))
                            {
                                g.DrawString(item.Icon, iconFont, iconBrush, 15, 12);
                                g.DrawString(item.Text.Substring(item.Icon.Length + 2), textFont, textBrush, 45, 15);
                            }
                        }

                        // Hover glow effect
                        if (currentActive)
                        {
                            var glowAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 20 + 30);
                            using (var glowBrush = new SolidBrush(Color.FromArgb(glowAlpha, 138, 43, 226)))
                            {
                                using (var path = CreateRoundedRectanglePath(buttonRect, 8))
                                {
                                    g.FillPath(glowBrush, path);
                                }
                            }
                        }
                    };

                    menuButton.Click += (s, e) => SwitchPage(item.Category);
                    menuButton.MouseEnter += (s, e) => menuButton.Invalidate();
                    menuButton.MouseLeave += (s, e) => menuButton.Invalidate();

                    _sidebar.Controls.Add(menuButton);
                    menuY += 50;
                }
            }

            _sidebar.Controls.Add(profilePanel);
            this.Controls.Add(_sidebar);
        }

        private void SwitchPage(string page)
        {
            _currentPage = page;

            // Update sidebar button states - trigger repaint for all panels
            foreach (Control control in _sidebar.Controls)
            {
                if (control is Panel panel && panel.Tag != null)
                {
                    panel.Invalidate();
                }
            }

            // Update main content based on page
            CreateMainContent();
        }

        private void CreateMainContent()
        {
            if (_mainContent != null)
            {
                this.Controls.Remove(_mainContent);
                _mainContent.Dispose();
            }

            _mainContent = new Panel
            {
                Size = new Size(this.Width - 240, this.Height - 50),
                Location = new Point(240, 50),
                BackColor = Color.FromArgb(28, 28, 32)
            };

            _mainContent.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated background gradient
                var alpha = (int)(Math.Sin(_animationFrame * 0.03) * 5 + 10);
                using (var brush = new LinearGradientBrush(
                    new Rectangle(0, 0, _mainContent.Width, _mainContent.Height),
                    Color.FromArgb(alpha, 138, 43, 226),
                    Color.FromArgb(alpha, 255, 69, 0),
                    LinearGradientMode.ForwardDiagonal))
                {
                    g.FillRectangle(brush, 0, 0, _mainContent.Width, _mainContent.Height);
                }
            };

            if (_currentPage == "HOME")
            {
                CreateHomePage();
            }
            else if (_currentPage == "TWEAKS")
            {
                CreateTweaksPage();
            }
            else
            {
                CreatePlaceholderPage(_currentPage);
            }

            this.Controls.Add(_mainContent);
        }

        private void CreateHomePage()
        {
            // Welcome header with animation
            var welcomePanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 120),
                Location = new Point(40, 30),
                BackColor = Color.Transparent
            };

            welcomePanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated welcome background
                var welcomeRect = new Rectangle(0, 0, welcomePanel.Width, welcomePanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.08) * 20 + 40);

                using (var brush = new LinearGradientBrush(welcomeRect,
                    Color.FromArgb(pulseAlpha, 138, 43, 226),
                    Color.FromArgb(pulseAlpha, 255, 69, 0),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(welcomeRect, 15))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Border glow
                using (var pen = new Pen(Color.FromArgb(100, 138, 43, 226), 2))
                {
                    using (var path = CreateRoundedRectanglePath(welcomeRect, 15))
                    {
                        g.DrawPath(pen, path);
                    }
                }

                // Welcome text with glow
                var timeOfDay = DateTime.Now.Hour < 12 ? "Morning" : DateTime.Now.Hour < 18 ? "Afternoon" : "Evening";
                var welcomeText = $"🌟 Good {timeOfDay}, Guest!";

                using (var font = new Font("Segoe UI", 24, FontStyle.Bold))
                {
                    // Text glow
                    for (int i = 1; i <= 3; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(50, 138, 43, 226)))
                        {
                            g.DrawString(welcomeText, font, glowBrush, 30 + i, 25 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(welcomeText, font, brush, 30, 25);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Welcome to Rodey Premium Tweaker - Your Ultimate Performance Solution", font, brush, 30, 65);
                }
            };

            // System stats cards
            var statsY = 170;
            var statsCards = new[]
            {
                new { Title = "🖥️ System Performance", Value = "Excellent", SubValue = "98% Optimized", Color = Color.FromArgb(50, 205, 50) },
                new { Title = "🎮 Gaming Ready", Value = "Beast Mode", SubValue = "+150 FPS Boost", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🚀 Boot Time", Value = "8.2s", SubValue = "75% Faster", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "🧠 Memory Usage", Value = "45%", SubValue = "Optimized", Color = Color.FromArgb(30, 144, 255) }
            };

            for (int i = 0; i < statsCards.Length; i++)
            {
                var card = statsCards[i];
                var cardWidth = (_mainContent.Width - 120) / 4;
                var cardPanel = new Panel
                {
                    Size = new Size(cardWidth, 140),
                    Location = new Point(40 + i * (cardWidth + 20), statsY),
                    BackColor = Color.Transparent
                };

                cardPanel.Paint += (s, e) =>
                {
                    var g = e.Graphics;
                    g.SmoothingMode = SmoothingMode.AntiAlias;

                    var cardRect = new Rectangle(0, 0, cardPanel.Width, cardPanel.Height);
                    var hoverAlpha = (int)(Math.Sin(_animationFrame * 0.1 + i) * 10 + 30);

                    // Card background
                    using (var brush = new LinearGradientBrush(cardRect,
                        Color.FromArgb(40, 40, 40, 45),
                        Color.FromArgb(60, 50, 50, 55),
                        LinearGradientMode.Vertical))
                    {
                        using (var path = CreateRoundedRectanglePath(cardRect, 12))
                        {
                            g.FillPath(brush, path);
                        }
                    }

                    // Animated border
                    using (var pen = new Pen(Color.FromArgb(hoverAlpha, card.Color), 2))
                    {
                        using (var path = CreateRoundedRectanglePath(cardRect, 12))
                        {
                            g.DrawPath(pen, path);
                        }
                    }

                    // Card content
                    using (var titleFont = new Font("Segoe UI", 11, FontStyle.Bold))
                    using (var valueFont = new Font("Segoe UI", 16, FontStyle.Bold))
                    using (var subFont = new Font("Segoe UI", 9, FontStyle.Regular))
                    {
                        using (var titleBrush = new SolidBrush(Color.FromArgb(200, 200, 200)))
                        using (var valueBrush = new SolidBrush(Color.White))
                        using (var subBrush = new SolidBrush(card.Color))
                        {
                            g.DrawString(card.Title, titleFont, titleBrush, 15, 15);
                            g.DrawString(card.Value, valueFont, valueBrush, 15, 45);
                            g.DrawString(card.SubValue, subFont, subBrush, 15, 75);
                        }
                    }

                    // Progress indicator
                    var progressRect = new Rectangle(15, 100, cardPanel.Width - 30, 8);
                    g.FillRectangle(new SolidBrush(Color.FromArgb(40, 40, 45)), progressRect);

                    var progressValue = 75 + Math.Sin(_animationFrame * 0.05 + i) * 20;
                    var progressWidth = (int)(progressRect.Width * progressValue / 100);
                    g.FillRectangle(new SolidBrush(card.Color), progressRect.X, progressRect.Y, progressWidth, progressRect.Height);
                };

                _mainContent.Controls.Add(cardPanel);
            }

            // Quick actions section
            var actionsY = 340;
            var actionsTitle = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 40),
                Location = new Point(40, actionsY),
                BackColor = Color.Transparent
            };

            actionsTitle.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                using (var font = new Font("Segoe UI", 18, FontStyle.Bold))
                {
                    // Glow effect
                    using (var glowBrush = new SolidBrush(Color.FromArgb(50, 138, 43, 226)))
                    {
                        g.DrawString("⚡ Quick Actions", font, glowBrush, 1, 11);
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString("⚡ Quick Actions", font, brush, 0, 10);
                    }
                }
            };

            _mainContent.Controls.AddRange(new Control[] { welcomePanel, actionsTitle });
        }

        private void CreateTweaksPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Animated header background
                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 255, 69, 0),
                    Color.FromArgb(pulseAlpha, 138, 43, 226),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "🚀 GAMING BEAST OPTIMIZATIONS";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 255, 69, 0)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Unleash maximum performance with our advanced optimization suite", font, brush, 25, 55);
                }
            };

            // Create MASSIVE amount of tweak cards
            var tweakCards = new[]
            {
                // ULTIMATE PERFORMANCE SECTION
                new { Title = "🔥 EXTREME GAMING MODE", Description = "Ultimate gaming performance", Tweaks = "4,567", Color = Color.FromArgb(255, 0, 0), Category = "ULTIMATE" },
                new { Title = "🚀 GAMING BEAST", Description = "Maximum FPS optimization", Tweaks = "3,847", Color = Color.FromArgb(255, 0, 255), Category = "ULTIMATE" },
                new { Title = "⚡ TURBO BOOST", Description = "Extreme CPU/GPU boost", Tweaks = "2,934", Color = Color.FromArgb(255, 140, 0), Category = "ULTIMATE" },
                new { Title = "🎯 PRECISION MODE", Description = "Ultra-low latency gaming", Tweaks = "1,876", Color = Color.FromArgb(255, 20, 147), Category = "ULTIMATE" },

                // CPU OPTIMIZATION SECTION
                new { Title = "⚡ CPU BEAST MODE", Description = "Maximum CPU performance", Tweaks = "2,234", Color = Color.FromArgb(255, 69, 0), Category = "CPU" },
                new { Title = "🔥 CPU OVERCLOCK", Description = "Safe CPU overclocking", Tweaks = "1,567", Color = Color.FromArgb(255, 100, 0), Category = "CPU" },
                new { Title = "🧠 CPU SCHEDULER", Description = "Advanced CPU scheduling", Tweaks = "1,123", Color = Color.FromArgb(255, 130, 0), Category = "CPU" },
                new { Title = "⚙️ CPU CACHE OPT", Description = "CPU cache optimization", Tweaks = "892", Color = Color.FromArgb(255, 160, 0), Category = "CPU" },
                new { Title = "🔧 CPU POWER OPT", Description = "CPU power optimization", Tweaks = "756", Color = Color.FromArgb(255, 190, 0), Category = "CPU" },
                new { Title = "⚡ CPU TURBO MAX", Description = "Maximum turbo boost", Tweaks = "634", Color = Color.FromArgb(255, 220, 0), Category = "CPU" },

                // GPU OPTIMIZATION SECTION
                new { Title = "🎮 GPU BEAST MODE", Description = "Ultimate GPU performance", Tweaks = "2,567", Color = Color.FromArgb(138, 43, 226), Category = "GPU" },
                new { Title = "🔥 GPU OVERCLOCK", Description = "Advanced GPU overclocking", Tweaks = "1,890", Color = Color.FromArgb(168, 73, 255), Category = "GPU" },
                new { Title = "🌟 RTX OPTIMIZER", Description = "NVIDIA RTX optimization", Tweaks = "1,456", Color = Color.FromArgb(76, 175, 80), Category = "GPU" },
                new { Title = "🔴 AMD OPTIMIZER", Description = "AMD GPU optimization", Tweaks = "1,234", Color = Color.FromArgb(244, 67, 54), Category = "GPU" },
                new { Title = "⚡ GPU MEMORY OPT", Description = "VRAM optimization", Tweaks = "987", Color = Color.FromArgb(156, 39, 176), Category = "GPU" },
                new { Title = "🎯 GPU PRECISION", Description = "GPU precision mode", Tweaks = "765", Color = Color.FromArgb(103, 58, 183), Category = "GPU" },

                // MEMORY OPTIMIZATION SECTION
                new { Title = "🧠 MEMORY BEAST", Description = "Ultimate RAM optimization", Tweaks = "1,892", Color = Color.FromArgb(0, 191, 255), Category = "MEMORY" },
                new { Title = "⚡ RAM OVERCLOCK", Description = "Safe RAM overclocking", Tweaks = "1,345", Color = Color.FromArgb(30, 221, 255), Category = "MEMORY" },
                new { Title = "🔥 MEMORY BOOST", Description = "Memory performance boost", Tweaks = "1,123", Color = Color.FromArgb(60, 251, 255), Category = "MEMORY" },
                new { Title = "🚀 CACHE OPTIMIZER", Description = "System cache optimization", Tweaks = "876", Color = Color.FromArgb(0, 150, 200), Category = "MEMORY" },
                new { Title = "⚙️ VIRTUAL MEMORY", Description = "Virtual memory tweaks", Tweaks = "654", Color = Color.FromArgb(0, 180, 230), Category = "MEMORY" },

                // NETWORK OPTIMIZATION SECTION
                new { Title = "🌐 NETWORK BEAST", Description = "Ultimate network performance", Tweaks = "1,456", Color = Color.FromArgb(30, 144, 255), Category = "NETWORK" },
                new { Title = "🎮 GAMING NETWORK", Description = "Gaming network optimization", Tweaks = "1,234", Color = Color.FromArgb(60, 174, 255), Category = "NETWORK" },
                new { Title = "📡 WIFI OPTIMIZER", Description = "WiFi performance boost", Tweaks = "987", Color = Color.FromArgb(90, 204, 255), Category = "NETWORK" },
                new { Title = "⚡ LATENCY KILLER", Description = "Ultra-low latency mode", Tweaks = "765", Color = Color.FromArgb(120, 234, 255), Category = "NETWORK" },
                new { Title = "🔥 BANDWIDTH MAX", Description = "Maximum bandwidth usage", Tweaks = "543", Color = Color.FromArgb(150, 255, 255), Category = "NETWORK" },

                // STORAGE OPTIMIZATION SECTION
                new { Title = "💾 STORAGE BEAST", Description = "Ultimate storage performance", Tweaks = "1,678", Color = Color.FromArgb(50, 205, 50), Category = "STORAGE" },
                new { Title = "🚀 SSD OPTIMIZER", Description = "SSD performance boost", Tweaks = "1,234", Color = Color.FromArgb(80, 235, 80), Category = "STORAGE" },
                new { Title = "⚡ NVME BOOST", Description = "NVMe optimization", Tweaks = "987", Color = Color.FromArgb(110, 255, 110), Category = "STORAGE" },
                new { Title = "🔥 DISK DEFRAG", Description = "Advanced disk optimization", Tweaks = "765", Color = Color.FromArgb(140, 255, 140), Category = "STORAGE" },
                new { Title = "⚙️ FILE SYSTEM", Description = "File system optimization", Tweaks = "543", Color = Color.FromArgb(170, 255, 170), Category = "STORAGE" },

                // SYSTEM OPTIMIZATION SECTION
                new { Title = "🧹 SYSTEM CLEANER", Description = "Deep system cleaning", Tweaks = "2,456", Color = Color.FromArgb(220, 20, 60), Category = "SYSTEM" },
                new { Title = "🗑️ BLOAT REMOVER", Description = "Remove all bloatware", Tweaks = "1,890", Color = Color.FromArgb(250, 50, 90), Category = "SYSTEM" },
                new { Title = "⚙️ REGISTRY BEAST", Description = "Ultimate registry optimization", Tweaks = "1,567", Color = Color.FromArgb(255, 80, 120), Category = "SYSTEM" },
                new { Title = "🔒 PRIVACY SHIELD", Description = "Maximum privacy protection", Tweaks = "1,234", Color = Color.FromArgb(75, 0, 130), Category = "SYSTEM" },
                new { Title = "🚀 STARTUP BOOST", Description = "Ultra-fast boot times", Tweaks = "987", Color = Color.FromArgb(105, 30, 160), Category = "SYSTEM" },

                // ADVANCED TWEAKS SECTION
                new { Title = "⚙️ BIOS OPTIMIZER", Description = "BIOS-level optimizations", Tweaks = "2,156", Color = Color.FromArgb(255, 215, 0), Category = "ADVANCED" },
                new { Title = "🔥 KERNEL TWEAKS", Description = "Windows kernel optimization", Tweaks = "1,789", Color = Color.FromArgb(255, 245, 30), Category = "ADVANCED" },
                new { Title = "⚡ DRIVER OPTIMIZER", Description = "Driver performance boost", Tweaks = "1,456", Color = Color.FromArgb(255, 255, 60), Category = "ADVANCED" },
                new { Title = "🎯 INTERRUPT OPT", Description = "Interrupt optimization", Tweaks = "1,123", Color = Color.FromArgb(255, 255, 90), Category = "ADVANCED" },
                new { Title = "🚀 SCHEDULER OPT", Description = "Advanced task scheduling", Tweaks = "890", Color = Color.FromArgb(255, 255, 120), Category = "ADVANCED" }
            };

            // Create scrollable panel for all the cards
            var scrollPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, _mainContent.Height - 140),
                Location = new Point(40, 130),
                BackColor = Color.Transparent,
                AutoScroll = true
            };

            var cardY = 20;
            var cardX = 20;
            var cardWidth = 320;
            var cardHeight = 120;
            var cardsPerRow = Math.Max(1, (scrollPanel.Width - 40) / (cardWidth + 20));

            // Group cards by category
            var categories = tweakCards.GroupBy(c => c.Category).ToList();

            foreach (var categoryGroup in categories)
            {
                // Category header
                var categoryHeader = new Panel
                {
                    Size = new Size(scrollPanel.Width - 40, 50),
                    Location = new Point(cardX, cardY),
                    BackColor = Color.Transparent
                };

                categoryHeader.Paint += (s, e) =>
                {
                    var g = e.Graphics;
                    g.SmoothingMode = SmoothingMode.AntiAlias;

                    var headerRect = new Rectangle(0, 0, categoryHeader.Width, categoryHeader.Height);
                    var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.08) * 10 + 20);

                    using (var brush = new LinearGradientBrush(headerRect,
                        Color.FromArgb(pulseAlpha, 138, 43, 226),
                        Color.FromArgb(pulseAlpha, 255, 69, 0),
                        LinearGradientMode.Horizontal))
                    {
                        using (var path = CreateRoundedRectanglePath(headerRect, 8))
                        {
                            g.FillPath(brush, path);
                        }
                    }

                    // Category title
                    using (var font = new Font("Segoe UI", 16, FontStyle.Bold))
                    {
                        var categoryTitle = $"🔥 {categoryGroup.Key} OPTIMIZATIONS";

                        // Glow effect
                        for (int i = 1; i <= 2; i++)
                        {
                            using (var glowBrush = new SolidBrush(Color.FromArgb(30, 255, 255, 255)))
                            {
                                g.DrawString(categoryTitle, font, glowBrush, 20 + i, 15 + i);
                            }
                        }

                        // Main text
                        using (var brush = new SolidBrush(Color.White))
                        {
                            g.DrawString(categoryTitle, font, brush, 20, 15);
                        }
                    }
                };

                scrollPanel.Controls.Add(categoryHeader);
                cardY += 70;

                // Cards for this category
                var cardsInCategory = categoryGroup.ToList();
                for (int i = 0; i < cardsInCategory.Count; i++)
                {
                    var card = cardsInCategory[i];
                    var x = cardX + (i % cardsPerRow) * (cardWidth + 20);
                    var y = cardY + (i / cardsPerRow) * (cardHeight + 20);

                    var cardPanel = new Panel
                    {
                        Size = new Size(cardWidth, cardHeight),
                        Location = new Point(x, y),
                        BackColor = Color.Transparent,
                        Cursor = Cursors.Hand,
                        Tag = card.Title
                    };

                    cardPanel.Paint += (s, e) =>
                    {
                        var g = e.Graphics;
                        g.SmoothingMode = SmoothingMode.AntiAlias;

                        var cardRect = new Rectangle(0, 0, cardPanel.Width, cardPanel.Height);
                        var hoverAlpha = (int)(Math.Sin(_animationFrame * 0.1 + i) * 15 + 35);

                        // Card background with gradient
                        using (var brush = new LinearGradientBrush(cardRect,
                            Color.FromArgb(50, 45, 45, 50),
                            Color.FromArgb(70, 55, 55, 60),
                            LinearGradientMode.Vertical))
                        {
                            using (var path = CreateRoundedRectanglePath(cardRect, 12))
                            {
                                g.FillPath(brush, path);
                            }
                        }

                        // Animated border
                        using (var pen = new Pen(Color.FromArgb(hoverAlpha, card.Color), 3))
                        {
                            using (var path = CreateRoundedRectanglePath(cardRect, 12))
                            {
                                g.DrawPath(pen, path);
                            }
                        }

                        // Glow effect
                        var glowAlpha = (int)(Math.Sin(_animationFrame * 0.12 + i) * 20 + 30);
                        using (var glowBrush = new SolidBrush(Color.FromArgb(glowAlpha, card.Color)))
                        {
                            var glowRect = new Rectangle(2, 2, cardRect.Width - 4, cardRect.Height - 4);
                            using (var path = CreateRoundedRectanglePath(glowRect, 10))
                            {
                                g.FillPath(glowBrush, path);
                            }
                        }

                        // Card content
                        using (var titleFont = new Font("Segoe UI", 13, FontStyle.Bold))
                        using (var descFont = new Font("Segoe UI", 10, FontStyle.Regular))
                        using (var tweaksFont = new Font("Segoe UI", 11, FontStyle.Bold))
                        {
                            // Title with glow
                            for (int j = 1; j <= 2; j++)
                            {
                                using (var glowBrush = new SolidBrush(Color.FromArgb(40, card.Color)))
                                {
                                    g.DrawString(card.Title, titleFont, glowBrush, 20 + j, 20 + j);
                                }
                            }

                            using (var titleBrush = new SolidBrush(Color.White))
                            using (var descBrush = new SolidBrush(Color.FromArgb(200, 200, 200)))
                            using (var tweaksBrush = new SolidBrush(card.Color))
                            {
                                g.DrawString(card.Title, titleFont, titleBrush, 20, 20);
                                g.DrawString(card.Description, descFont, descBrush, 20, 45);
                                g.DrawString($"{card.Tweaks} tweaks", tweaksFont, tweaksBrush, 20, 70);
                            }
                        }

                        // Apply button
                        var buttonRect = new Rectangle(cardWidth - 100, cardHeight - 35, 80, 25);
                        using (var buttonBrush = new LinearGradientBrush(buttonRect,
                            card.Color,
                            Color.FromArgb(Math.Min(255, card.Color.R + 50),
                                          Math.Min(255, card.Color.G + 50),
                                          Math.Min(255, card.Color.B + 50)),
                            LinearGradientMode.Vertical))
                        {
                            using (var path = CreateRoundedRectanglePath(buttonRect, 6))
                            {
                                g.FillPath(buttonBrush, path);
                            }
                        }

                        using (var buttonFont = new Font("Segoe UI", 9, FontStyle.Bold))
                        using (var buttonTextBrush = new SolidBrush(Color.White))
                        {
                            var sf = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center };
                            g.DrawString("APPLY NOW", buttonFont, buttonTextBrush, buttonRect, sf);
                        }
                    };

                    cardPanel.Click += async (s, e) => await ApplyOptimization(card.Title);
                    cardPanel.MouseEnter += (s, e) => cardPanel.Invalidate();
                    cardPanel.MouseLeave += (s, e) => cardPanel.Invalidate();

                    scrollPanel.Controls.Add(cardPanel);
                }

                cardY += ((cardsInCategory.Count - 1) / cardsPerRow + 1) * (cardHeight + 20) + 30;
            }

            _mainContent.Controls.AddRange(new Control[] { headerPanel, scrollPanel });
        }

        private void CreatePlaceholderPage(string pageName)
        {
            switch (pageName)
            {
                case "POWER":
                    CreatePowerPage();
                    break;
                case "NETWORK":
                    CreateNetworkPage();
                    break;
                case "SCRIPTS":
                    CreateScriptsPage();
                    break;
                case "CLEANER":
                    CreateCleanerPage();
                    break;
                case "DEBLOAT":
                    CreateDebloatPage();
                    break;
                case "STARTUP":
                    CreateStartupPage();
                    break;
                default:
                    CreateDefaultPage(pageName);
                    break;
            }
        }

        private void CreatePowerPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 255, 215, 0),
                    Color.FromArgb(pulseAlpha, 255, 140, 0),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "⚡ POWER OPTIMIZATION";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 255, 215, 0)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Optimize Windows power settings for maximum performance and efficiency", font, brush, 25, 55);
                }
            };

            // Power optimization tools with massive performance gains
            var powerTweaks = new[]
            {
                new { Title = "⚡ ULTIMATE PERFORMANCE", Description = "Maximum power mode", Tweaks = "2,456", Color = Color.FromArgb(255, 215, 0) },
                new { Title = "🔥 TURBO BOOST", Description = "CPU turbo optimization", Tweaks = "1,876", Color = Color.FromArgb(255, 140, 0) },
                new { Title = "🚀 POWER BEAST", Description = "Ultimate power settings", Tweaks = "1,567", Color = Color.FromArgb(255, 165, 0) },
                new { Title = "⚙️ CPU GOVERNOR", Description = "Advanced CPU scaling", Tweaks = "1,234", Color = Color.FromArgb(255, 228, 181) },
                new { Title = "🎯 PERFORMANCE MODE", Description = "High performance profile", Tweaks = "987", Color = Color.FromArgb(255, 218, 185) },
                new { Title = "🔋 POWER EFFICIENCY", Description = "Balanced performance", Tweaks = "765", Color = Color.FromArgb(255, 192, 203) }
            };

            CreateTweakGrid(powerTweaks, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateNetworkPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 30, 144, 255),
                    Color.FromArgb(pulseAlpha, 0, 191, 255),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "🌐 NETWORK OPTIMIZATION";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 30, 144, 255)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Optimize your network for gaming and streaming performance", font, brush, 25, 55);
                }
            };

            // Network optimization cards with more tweaks
            var networkTweaks = new[]
            {
                new { Title = "🎮 GAMING NETWORK", Description = "Ultra-low latency gaming", Tweaks = "1,234", Color = Color.FromArgb(30, 144, 255) },
                new { Title = "📺 STREAMING BOOST", Description = "Streaming optimization", Tweaks = "987", Color = Color.FromArgb(0, 191, 255) },
                new { Title = "⚡ LATENCY REDUCER", Description = "Minimize network lag", Tweaks = "765", Color = Color.FromArgb(135, 206, 250) },
                new { Title = "🌐 DNS OPTIMIZER", Description = "Fastest DNS settings", Tweaks = "543", Color = Color.FromArgb(70, 130, 180) },
                new { Title = "🔒 FIREWALL BOOST", Description = "Optimized firewall", Tweaks = "432", Color = Color.FromArgb(100, 149, 237) },
                new { Title = "📡 WIFI OPTIMIZER", Description = "WiFi performance boost", Tweaks = "321", Color = Color.FromArgb(176, 196, 222) },
                new { Title = "🚀 TCP OPTIMIZER", Description = "Advanced TCP tweaks", Tweaks = "654", Color = Color.FromArgb(65, 105, 225) },
                new { Title = "⚡ BANDWIDTH MAX", Description = "Maximum bandwidth usage", Tweaks = "876", Color = Color.FromArgb(72, 61, 139) },
                new { Title = "🎯 PING DESTROYER", Description = "Eliminate ping spikes", Tweaks = "432", Color = Color.FromArgb(123, 104, 238) }
            };

            CreateTweakGrid(networkTweaks, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateScriptsPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 50, 205, 50),
                    Color.FromArgb(pulseAlpha, 34, 139, 34),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "📡 ADVANCED SCRIPTS";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 50, 205, 50)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Professional-grade optimization scripts for maximum performance", font, brush, 25, 55);
                }
            };

            // Advanced scripts with massive tweak counts
            var scripts = new[]
            {
                new { Title = "🚀 TCP BEAST MODE", Description = "Ultimate TCP optimization", Tweaks = "2,456", Color = Color.FromArgb(50, 205, 50) },
                new { Title = "⚡ BANDWIDTH DESTROYER", Description = "Maximum bandwidth usage", Tweaks = "1,876", Color = Color.FromArgb(34, 139, 34) },
                new { Title = "🎯 PING ANNIHILATOR", Description = "Eliminate all ping issues", Tweaks = "1,543", Color = Color.FromArgb(0, 255, 0) },
                new { Title = "🌐 QOS DOMINATOR", Description = "Quality of Service mastery", Tweaks = "1,234", Color = Color.FromArgb(124, 252, 0) },
                new { Title = "🔥 PACKET OPTIMIZER", Description = "Advanced packet routing", Tweaks = "987", Color = Color.FromArgb(127, 255, 0) },
                new { Title = "⚡ BUFFER ELIMINATOR", Description = "Remove all buffering", Tweaks = "765", Color = Color.FromArgb(173, 255, 47) },
                new { Title = "🚀 SPEED MAXIMIZER", Description = "Maximum connection speed", Tweaks = "654", Color = Color.FromArgb(154, 205, 50) },
                new { Title = "🎮 GAMING PROTOCOL", Description = "Gaming-specific protocols", Tweaks = "543", Color = Color.FromArgb(107, 142, 35) },
                new { Title = "🌟 ULTRA OPTIMIZER", Description = "Ultimate network performance", Tweaks = "432", Color = Color.FromArgb(85, 107, 47) }
            };

            CreateTweakGrid(scripts, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateCleanerPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 220, 20, 60),
                    Color.FromArgb(pulseAlpha, 255, 69, 0),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "🧹 SYSTEM CLEANER";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 220, 20, 60)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Deep clean your system for maximum performance and storage", font, brush, 25, 55);
                }
            };

            // System cleaner tools with massive capabilities
            var cleanerTweaks = new[]
            {
                new { Title = "🗑️ TEMP DESTROYER", Description = "Eliminate all temp files", Tweaks = "3,456", Color = Color.FromArgb(220, 20, 60) },
                new { Title = "📁 CACHE ANNIHILATOR", Description = "Clear all system cache", Tweaks = "2,789", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🔄 LOG ELIMINATOR", Description = "Remove all system logs", Tweaks = "2,123", Color = Color.FromArgb(255, 99, 71) },
                new { Title = "🗂️ REGISTRY BEAST", Description = "Deep registry cleaning", Tweaks = "1,876", Color = Color.FromArgb(255, 140, 0) },
                new { Title = "💾 DISK OPTIMIZER", Description = "Ultimate disk cleanup", Tweaks = "1,567", Color = Color.FromArgb(255, 165, 0) },
                new { Title = "🔍 DUPLICATE HUNTER", Description = "Find and remove duplicates", Tweaks = "1,234", Color = Color.FromArgb(255, 215, 0) },
                new { Title = "🧽 MEMORY CLEANER", Description = "Clean RAM and virtual memory", Tweaks = "987", Color = Color.FromArgb(255, 228, 181) },
                new { Title = "🗄️ JUNK DESTROYER", Description = "Remove all junk files", Tweaks = "765", Color = Color.FromArgb(255, 218, 185) },
                new { Title = "⚡ SPEED CLEANER", Description = "Performance-focused cleaning", Tweaks = "654", Color = Color.FromArgb(255, 192, 203) }
            };

            CreateTweakGrid(cleanerTweaks, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateDebloatPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 255, 140, 0),
                    Color.FromArgb(pulseAlpha, 255, 69, 0),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "🗑️ SYSTEM DEBLOATER";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 255, 140, 0)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Remove bloatware and unnecessary Windows components for maximum performance", font, brush, 25, 55);
                }
            };

            var debloatTweaks = new[]
            {
                new { Title = "🗑️ WINDOWS DESTROYER", Description = "Remove all Windows bloatware", Tweaks = "4,567", Color = Color.FromArgb(255, 140, 0) },
                new { Title = "📱 UWP ANNIHILATOR", Description = "Remove all UWP applications", Tweaks = "3,456", Color = Color.FromArgb(255, 69, 0) },
                new { Title = "🌐 EDGE ELIMINATOR", Description = "Completely remove Microsoft Edge", Tweaks = "2,789", Color = Color.FromArgb(255, 99, 71) },
                new { Title = "🔍 CORTANA KILLER", Description = "Permanently disable Cortana", Tweaks = "2,123", Color = Color.FromArgb(255, 165, 0) },
                new { Title = "📊 TELEMETRY BLOCKER", Description = "Block all telemetry and tracking", Tweaks = "1,876", Color = Color.FromArgb(255, 215, 0) },
                new { Title = "🎮 XBOX DESTROYER", Description = "Remove all Xbox components", Tweaks = "1,567", Color = Color.FromArgb(255, 228, 181) },
                new { Title = "🛡️ DEFENDER REMOVER", Description = "Remove Windows Defender", Tweaks = "1,234", Color = Color.FromArgb(255, 218, 185) },
                new { Title = "📧 MAIL ELIMINATOR", Description = "Remove Windows Mail apps", Tweaks = "987", Color = Color.FromArgb(255, 192, 203) },
                new { Title = "🎵 MEDIA CLEANER", Description = "Remove unnecessary media apps", Tweaks = "765", Color = Color.FromArgb(255, 182, 193) }
            };

            CreateTweakGrid(debloatTweaks, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateStartupPage()
        {
            // Page header with animation
            var headerPanel = new Panel
            {
                Size = new Size(_mainContent.Width - 80, 100),
                Location = new Point(40, 20),
                BackColor = Color.Transparent
            };

            headerPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                var headerRect = new Rectangle(0, 0, headerPanel.Width, headerPanel.Height);
                var pulseAlpha = (int)(Math.Sin(_animationFrame * 0.1) * 15 + 25);

                using (var brush = new LinearGradientBrush(headerRect,
                    Color.FromArgb(pulseAlpha, 75, 0, 130),
                    Color.FromArgb(pulseAlpha, 138, 43, 226),
                    LinearGradientMode.Horizontal))
                {
                    using (var path = CreateRoundedRectanglePath(headerRect, 12))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // Title with glow
                using (var font = new Font("Segoe UI", 28, FontStyle.Bold))
                {
                    var titleText = "🚀 STARTUP MANAGER";

                    // Glow effect
                    for (int i = 1; i <= 4; i++)
                    {
                        using (var glowBrush = new SolidBrush(Color.FromArgb(40, 75, 0, 130)))
                        {
                            g.DrawString(titleText, font, glowBrush, 25 + i, 15 + i);
                        }
                    }

                    // Main text
                    using (var brush = new SolidBrush(Color.White))
                    {
                        g.DrawString(titleText, font, brush, 25, 15);
                    }
                }

                // Subtitle
                using (var font = new Font("Segoe UI", 14, FontStyle.Regular))
                using (var brush = new SolidBrush(Color.FromArgb(220, 220, 220)))
                {
                    g.DrawString("Optimize Windows startup and boot performance for lightning-fast speeds", font, brush, 25, 55);
                }
            };

            // Startup optimization tools with massive performance gains
            var startupTweaks = new[]
            {
                new { Title = "⚡ BOOT DESTROYER", Description = "Ultimate boot optimization", Tweaks = "3,456", Color = Color.FromArgb(75, 0, 130) },
                new { Title = "🚀 STARTUP KILLER", Description = "Eliminate startup delays", Tweaks = "2,789", Color = Color.FromArgb(138, 43, 226) },
                new { Title = "⏱️ SPEED DEMON", Description = "Maximum boot speed", Tweaks = "2,123", Color = Color.FromArgb(148, 0, 211) },
                new { Title = "🔧 SERVICE BEAST", Description = "Optimize all Windows services", Tweaks = "1,876", Color = Color.FromArgb(186, 85, 211) },
                new { Title = "💾 LIGHTNING BOOT", Description = "Ultra-fast startup mode", Tweaks = "1,567", Color = Color.FromArgb(221, 160, 221) },
                new { Title = "🔄 UPDATE CONTROLLER", Description = "Manage automatic updates", Tweaks = "1,234", Color = Color.FromArgb(238, 130, 238) },
                new { Title = "⚙️ PROCESS OPTIMIZER", Description = "Optimize startup processes", Tweaks = "987", Color = Color.FromArgb(255, 182, 193) },
                new { Title = "🎯 DELAY ELIMINATOR", Description = "Remove all startup delays", Tweaks = "765", Color = Color.FromArgb(255, 192, 203) },
                new { Title = "🔥 TURBO STARTUP", Description = "Maximum startup performance", Tweaks = "654", Color = Color.FromArgb(255, 218, 185) }
            };

            CreateTweakGrid(startupTweaks, 140);
            _mainContent.Controls.Add(headerPanel);
        }

        private void CreateDefaultPage(string pageName)
        {
            var titleLabel = new Label
            {
                Text = $"{pageName} Page",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(40, 30),
                AutoSize = true
            };

            var descLabel = new Label
            {
                Text = "This page is under development.",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(180, 180, 180),
                Location = new Point(40, 70),
                AutoSize = true
            };

            _mainContent.Controls.AddRange(new Control[] { titleLabel, descLabel });
        }

        private void CreateTweakGrid(dynamic[] tweaks, int startY)
        {
            var cardWidth = 280;
            var cardHeight = 100;
            var cardsPerRow = 4;
            var cardX = 40;

            for (int i = 0; i < tweaks.Length; i++)
            {
                var tweak = tweaks[i];
                var x = cardX + (i % cardsPerRow) * (cardWidth + 20);
                var y = startY + (i / cardsPerRow) * (cardHeight + 20);

                var cardPanel = new Panel
                {
                    Size = new Size(cardWidth, cardHeight),
                    Location = new Point(x, y),
                    BackColor = Color.FromArgb(40, 40, 45),
                    Cursor = Cursors.Hand
                };

                cardPanel.Paint += (s, e) =>
                {
                    var g = e.Graphics;
                    g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                    var rect = new Rectangle(0, 0, cardPanel.Width - 1, cardPanel.Height - 1);
                    using (var path = CreateRoundedRectanglePath(rect, 8))
                    {
                        using (var brush = new SolidBrush(Color.FromArgb(40, 40, 45)))
                        {
                            g.FillPath(brush, path);
                        }
                        using (var pen = new Pen(tweak.Color, 2))
                        {
                            g.DrawPath(pen, path);
                        }
                    }
                };

                var titleLabel = new Label
                {
                    Text = tweak.Title,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.White,
                    Location = new Point(15, 15),
                    Size = new Size(250, 20),
                    BackColor = Color.Transparent
                };

                var descLabel = new Label
                {
                    Text = tweak.Description,
                    Font = new Font("Segoe UI", 9, FontStyle.Regular),
                    ForeColor = Color.FromArgb(180, 180, 180),
                    Location = new Point(15, 35),
                    Size = new Size(250, 15),
                    BackColor = Color.Transparent
                };

                var tweaksLabel = new Label
                {
                    Text = $"{tweak.Tweaks} tweaks",
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    ForeColor = tweak.Color,
                    Location = new Point(15, 60),
                    Size = new Size(100, 20),
                    BackColor = Color.Transparent
                };

                var applyButton = new Button
                {
                    Text = "APPLY",
                    Font = new Font("Segoe UI", 9, FontStyle.Bold),
                    ForeColor = Color.White,
                    BackColor = tweak.Color,
                    FlatStyle = FlatStyle.Flat,
                    Size = new Size(80, 25),
                    Location = new Point(180, 60),
                    Tag = tweak.Title
                };
                applyButton.FlatAppearance.BorderSize = 0;
                applyButton.Click += async (s, e) => await ApplyOptimization(tweak.Title);

                cardPanel.Controls.AddRange(new Control[] { titleLabel, descLabel, tweaksLabel, applyButton });
                _mainContent.Controls.Add(cardPanel);
            }
        }

        private System.Drawing.Drawing2D.GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private async Task ApplyOptimization(string optimizationType)
        {
            try
            {
                // Show progress dialog
                var progressForm = new Form
                {
                    Text = "Applying Optimizations",
                    Size = new Size(400, 150),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false,
                    BackColor = Color.FromArgb(32, 32, 36)
                };

                var statusLabel = new Label
                {
                    Text = "Initializing...",
                    ForeColor = Color.White,
                    Location = new Point(20, 20),
                    Size = new Size(350, 20)
                };

                var progressBar = new ProgressBar
                {
                    Location = new Point(20, 50),
                    Size = new Size(350, 20),
                    Style = ProgressBarStyle.Continuous
                };

                progressForm.Controls.AddRange(new Control[] { statusLabel, progressBar });
                progressForm.Show();

                // Subscribe to events
                _optimizer.StatusChanged += (s, status) => statusLabel.Text = status;
                _optimizer.ProgressChanged += (s, progress) => progressBar.Value = Math.Min(progress, 100);

                OptimizationResult result = null;

                switch (optimizationType)
                {
                    // ULTIMATE PERFORMANCE SECTION
                    case "🔥 EXTREME GAMING MODE":
                        result = await _optimizer.OptimizeGamingBeast();
                        break;
                    case "🚀 GAMING BEAST":
                        result = await _optimizer.OptimizeGamingBeast();
                        break;
                    case "⚡ TURBO BOOST":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🎯 PRECISION MODE":
                        result = await _optimizer.OptimizeLatency();
                        break;

                    // CPU OPTIMIZATION SECTION
                    case "⚡ CPU BEAST MODE":
                    case "⚡ CPU OPTIMIZER":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🔥 CPU OVERCLOCK":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🧠 CPU SCHEDULER":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "⚙️ CPU CACHE OPT":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🔧 CPU POWER OPT":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "⚡ CPU TURBO MAX":
                        result = await _optimizer.OptimizeCpu();
                        break;

                    // GPU OPTIMIZATION SECTION
                    case "🎮 GPU BEAST MODE":
                    case "🎮 GPU OPTIMIZER":
                        result = await _optimizer.OptimizeGpu();
                        break;
                    case "🔥 GPU OVERCLOCK":
                        result = await _optimizer.OptimizeAdvancedGpu();
                        break;
                    case "🌟 RTX OPTIMIZER":
                        result = await _optimizer.OptimizeAdvancedGpu();
                        break;
                    case "🔴 AMD OPTIMIZER":
                        result = await _optimizer.OptimizeAdvancedGpu();
                        break;
                    case "⚡ GPU MEMORY OPT":
                        result = await _optimizer.OptimizeGpu();
                        break;
                    case "🎯 GPU PRECISION":
                        result = await _optimizer.OptimizeAdvancedGpu();
                        break;

                    // MEMORY OPTIMIZATION SECTION
                    case "🧠 MEMORY BEAST":
                    case "🧠 MEMORY BOOST":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "⚡ RAM OVERCLOCK":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "🔥 MEMORY BOOST":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "🚀 CACHE OPTIMIZER":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "⚙️ VIRTUAL MEMORY":
                        result = await _optimizer.OptimizeMemory();
                        break;

                    // NETWORK OPTIMIZATION SECTION
                    case "🌐 NETWORK BEAST":
                    case "🌐 NETWORK BOOST":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🎮 GAMING NETWORK":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "📡 WIFI OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "⚡ LATENCY KILLER":
                    case "⚡ LATENCY BOOST":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🔥 BANDWIDTH MAX":
                        result = await _optimizer.OptimizeNetwork();
                        break;

                    // STORAGE OPTIMIZATION SECTION
                    case "💾 STORAGE BEAST":
                    case "💾 STORAGE BOOST":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "🚀 SSD OPTIMIZER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "⚡ NVME BOOST":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "🔥 DISK DEFRAG":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "⚙️ FILE SYSTEM":
                        result = await _optimizer.OptimizeStorage();
                        break;

                    // SYSTEM OPTIMIZATION SECTION
                    case "🧹 SYSTEM CLEANER":
                    case "🧹 SYSTEM DEBLOAT":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🗑️ BLOAT REMOVER":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "⚙️ REGISTRY BEAST":
                    case "⚙️ REGISTRY CLEAN":
                        result = await _optimizer.OptimizeRegistry();
                        break;
                    case "🔒 PRIVACY SHIELD":
                    case "🔒 PRIVACY BOOST":
                        result = await _optimizer.OptimizePrivacy();
                        break;
                    case "🚀 STARTUP BOOST":
                        result = await _optimizer.OptimizeBios();
                        break;

                    // ADVANCED TWEAKS SECTION
                    case "⚙️ BIOS OPTIMIZER":
                        result = await _optimizer.OptimizeBios();
                        break;
                    case "🔥 KERNEL TWEAKS":
                        result = await _optimizer.OptimizeExtremeMode();
                        break;
                    case "⚡ DRIVER OPTIMIZER":
                        result = await _optimizer.OptimizeExtremeMode();
                        break;
                    case "🎯 INTERRUPT OPT":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🚀 SCHEDULER OPT":
                        result = await _optimizer.OptimizeExtremeMode();
                        break;
                    case "🔥 ADVANCED GPU":
                        result = await _optimizer.OptimizeAdvancedGpu();
                        break;
                    case "🎯 EXTREME MODE":
                        result = await _optimizer.OptimizeExtremeMode();
                        break;
                    case "⚡ POWER BOOST":
                        result = await _optimizer.OptimizePowerPlan();
                        break;

                    // Network Page specific tweaks
                    case "📺 STREAMING BOOST":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "⚡ LATENCY REDUCER":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🌐 DNS OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🔒 FIREWALL BOOST":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🎯 PING DESTROYER":
                        result = await _optimizer.OptimizeLatency();
                        break;

                    // Cleaner Page
                    case "🗑️ TEMP DESTROYER":
                    case "🗑️ TEMP CLEANER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "📁 CACHE ANNIHILATOR":
                    case "📁 CACHE CLEANER":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "🔄 LOG ELIMINATOR":
                    case "🔄 LOG CLEANER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "🗂️ REGISTRY BEAST":
                    case "🗂️ REGISTRY CLEANER":
                        result = await _optimizer.OptimizeRegistry();
                        break;
                    case "💾 DISK OPTIMIZER":
                    case "💾 DISK CLEANER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "🔍 DUPLICATE HUNTER":
                    case "🔍 DUPLICATE FINDER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "🧽 MEMORY CLEANER":
                        result = await _optimizer.OptimizeMemory();
                        break;
                    case "🗄️ JUNK DESTROYER":
                        result = await _optimizer.OptimizeStorage();
                        break;
                    case "⚡ SPEED CLEANER":
                        result = await _optimizer.OptimizeStorage();
                        break;

                    // Debloat Page
                    case "🗑️ WINDOWS DESTROYER":
                    case "🗑️ WINDOWS BLOAT":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "📱 UWP ANNIHILATOR":
                    case "📱 UWP APPS":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🌐 EDGE ELIMINATOR":
                    case "🌐 EDGE REMOVAL":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🔍 CORTANA KILLER":
                    case "🔍 CORTANA DISABLE":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "📊 TELEMETRY BLOCKER":
                    case "📊 TELEMETRY BLOCK":
                        result = await _optimizer.OptimizePrivacy();
                        break;
                    case "🎮 XBOX DESTROYER":
                    case "🎮 XBOX REMOVAL":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🛡️ DEFENDER REMOVER":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "📧 MAIL ELIMINATOR":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🎵 MEDIA CLEANER":
                        result = await _optimizer.OptimizeDebloat();
                        break;

                    // Startup Page
                    case "⚡ BOOT DESTROYER":
                    case "⚡ BOOT OPTIMIZER":
                        result = await _optimizer.OptimizeBios();
                        break;
                    case "🚀 STARTUP KILLER":
                    case "🚀 STARTUP APPS":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "⏱️ SPEED DEMON":
                    case "⏱️ BOOT TIME":
                        result = await _optimizer.OptimizeBios();
                        break;
                    case "🔧 SERVICE BEAST":
                    case "🔧 SERVICES OPTIMIZER":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "💾 LIGHTNING BOOT":
                    case "💾 FAST STARTUP":
                        result = await _optimizer.OptimizeBios();
                        break;
                    case "🔄 UPDATE CONTROLLER":
                    case "🔄 AUTO UPDATES":
                        result = await _optimizer.OptimizeRegistry();
                        break;
                    case "⚙️ PROCESS OPTIMIZER":
                        result = await _optimizer.OptimizeDebloat();
                        break;
                    case "🎯 DELAY ELIMINATOR":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🔥 TURBO STARTUP":
                        result = await _optimizer.OptimizeBios();
                        break;

                    // Power Page
                    case "⚡ ULTIMATE PERFORMANCE":
                        result = await _optimizer.OptimizePowerPlan();
                        break;
                    case "🔥 TURBO BOOST":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🚀 POWER BEAST":
                        result = await _optimizer.OptimizePowerPlan();
                        break;
                    case "⚙️ CPU GOVERNOR":
                        result = await _optimizer.OptimizeCpu();
                        break;
                    case "🎯 PERFORMANCE MODE":
                        result = await _optimizer.OptimizePowerPlan();
                        break;
                    case "🔋 POWER EFFICIENCY":
                        result = await _optimizer.OptimizePowerPlan();
                        break;

                    // Advanced Scripts Page
                    case "🚀 TCP BEAST MODE":
                    case "🚀 TCP OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "⚡ BANDWIDTH DESTROYER":
                    case "⚡ BANDWIDTH BOOST":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🎯 PING ANNIHILATOR":
                    case "🎯 PING REDUCER":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🌐 QOS DOMINATOR":
                    case "🌐 QOS OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🔥 PACKET OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "⚡ BUFFER ELIMINATOR":
                        result = await _optimizer.OptimizeLatency();
                        break;
                    case "🚀 SPEED MAXIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🎮 GAMING PROTOCOL":
                        result = await _optimizer.OptimizeNetwork();
                        break;
                    case "🌟 ULTRA OPTIMIZER":
                        result = await _optimizer.OptimizeNetwork();
                        break;

                    default:
                        // Fallback to CPU optimization for unknown types
                        result = await _optimizer.OptimizeCpu();
                        break;
                }

                progressForm.Close();

                // Show result
                var resultMessage = result?.Success == true
                    ? $"✅ {result.Message}\n\n🎮 Estimated FPS Gain: {result.EstimatedFpsGain}"
                    : $"❌ {result?.Message ?? "Unknown error occurred"}";

                MessageBox.Show(resultMessage, "Optimization Complete", MessageBoxButtons.OK,
                    result?.Success == true ? MessageBoxIcon.Information : MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ Error applying optimization: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
