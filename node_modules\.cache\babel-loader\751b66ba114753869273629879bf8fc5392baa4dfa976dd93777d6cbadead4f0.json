{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ShoppingCart = createLucideIcon(\"ShoppingCart\", [[\"circle\", {\n  cx: \"8\",\n  cy: \"21\",\n  r: \"1\",\n  key: \"jimo8o\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"21\",\n  r: \"1\",\n  key: \"13723u\"\n}], [\"path\", {\n  d: \"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\",\n  key: \"9zh506\"\n}]]);\nexport { ShoppingCart as default };", "map": {"version": 3, "names": ["ShoppingCart", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\shopping-cart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ShoppingCart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI4IiBjeT0iMjEiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSIyMSIgcj0iMSIgLz4KICA8cGF0aCBkPSJNMi4wNSAyLjA1aDJsMi42NiAxMi40MmEyIDIgMCAwIDAgMiAxLjU4aDkuNzhhMiAyIDAgMCAwIDEuOTUtMS41N2wxLjY1LTcuNDNINS4xMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shopping-cart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShoppingCart = createLucideIcon('ShoppingCart', [\n  ['circle', { cx: '8', cy: '21', r: '1', key: 'jimo8o' }],\n  ['circle', { cx: '19', cy: '21', r: '1', key: '13723u' }],\n  [\n    'path',\n    {\n      d: 'M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12',\n      key: '9zh506',\n    },\n  ],\n]);\n\nexport default ShoppingCart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}