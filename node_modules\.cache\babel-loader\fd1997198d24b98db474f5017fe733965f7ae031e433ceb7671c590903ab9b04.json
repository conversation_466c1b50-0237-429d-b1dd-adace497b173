{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\BackupRestore.js\";\nimport React from 'react';\nimport { HardDrive } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BackupRestore = ({\n  isAdmin\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: '#00ff88'\n      },\n      children: \"Backup & Restore\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#888'\n      },\n      children: \"System backup and restore tools coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = BackupRestore;\nexport default BackupRestore;\nvar _c;\n$RefreshReg$(_c, \"BackupRestore\");", "map": {"version": 3, "names": ["React", "HardDrive", "jsxDEV", "_jsxDEV", "BackupRestore", "isAdmin", "style", "padding", "textAlign", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/BackupRestore.js"], "sourcesContent": ["import React from 'react';\nimport { HardDrive } from 'lucide-react';\n\nconst BackupRestore = ({ isAdmin }) => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1 style={{ color: '#00ff88' }}>Backup & Restore</h1>\n      <p style={{ color: '#888' }}>System backup and restore tools coming soon...</p>\n    </div>\n  );\n};\n\nexport default BackupRestore;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACrC,oBACEF,OAAA;IAAKG,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDN,OAAA;MAAIG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAU,CAAE;MAAAD,QAAA,EAAC;IAAgB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtDX,OAAA;MAAGG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAA8C;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5E,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIX,aAAa;AASnB,eAAeA,aAAa;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}