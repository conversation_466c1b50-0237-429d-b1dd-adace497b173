{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\PerformanceMonitor.js\";\nimport React from 'react';\nimport { BarChart3 } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PerformanceMonitor = ({\n  systemInfo\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: '#00ff88'\n      },\n      children: \"Performance Monitor\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#888'\n      },\n      children: \"Real-time FPS and system metrics coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = PerformanceMonitor;\nexport default PerformanceMonitor;\nvar _c;\n$RefreshReg$(_c, \"PerformanceMonitor\");", "map": {"version": 3, "names": ["React", "BarChart3", "jsxDEV", "_jsxDEV", "PerformanceMonitor", "systemInfo", "style", "padding", "textAlign", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/PerformanceMonitor.js"], "sourcesContent": ["import React from 'react';\nimport { BarChart3 } from 'lucide-react';\n\nconst PerformanceMonitor = ({ systemInfo }) => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1 style={{ color: '#00ff88' }}>Performance Monitor</h1>\n      <p style={{ color: '#888' }}>Real-time FPS and system metrics coming soon...</p>\n    </div>\n  );\n};\n\nexport default PerformanceMonitor;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAC7C,oBACEF,OAAA;IAAKG,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDN,OAAA;MAAIG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAU,CAAE;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzDX,OAAA;MAAGG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAA+C;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7E,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIX,kBAAkB;AASxB,eAAeA,kBAAkB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}