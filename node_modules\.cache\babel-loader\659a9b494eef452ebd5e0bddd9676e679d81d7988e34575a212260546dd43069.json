{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PanelTopInactive = createLucideIcon(\"PanelTopInactive\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M14 9h1\",\n  key: \"l0svgy\"\n}], [\"path\", {\n  d: \"M19 9h2\",\n  key: \"te2zfg\"\n}], [\"path\", {\n  d: \"M3 9h2\",\n  key: \"1h4ldw\"\n}], [\"path\", {\n  d: \"M9 9h1\",\n  key: \"15jzuz\"\n}]]);\nexport { PanelTopInactive as default };", "map": {"version": 3, "names": ["PanelTopInactive", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\panel-top-inactive.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PanelTopInactive\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xNCA5aDEiIC8+CiAgPHBhdGggZD0iTTE5IDloMiIgLz4KICA8cGF0aCBkPSJNMyA5aDIiIC8+CiAgPHBhdGggZD0iTTkgOWgxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/panel-top-inactive\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PanelTopInactive = createLucideIcon('PanelTopInactive', [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M14 9h1', key: 'l0svgy' }],\n  ['path', { d: 'M19 9h2', key: 'te2zfg' }],\n  ['path', { d: 'M3 9h2', key: '1h4ldw' }],\n  ['path', { d: 'M9 9h1', key: '15jzuz' }],\n]);\n\nexport default PanelTopInactive;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAD,GAAA,EAAK;AAAA,CAAU,EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}