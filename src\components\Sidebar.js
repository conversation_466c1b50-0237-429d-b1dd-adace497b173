import React from 'react';
import {
  Home,
  Zap,
  Settings2,
  Trash2,
  Cpu,
  Activity,
  Settings,
  Shield,
  Gauge,
  HardDrive,
  Monitor,
  Wifi,
  MemoryStick,
  Wrench,
  BarChart3,
  TrendingUp
} from 'lucide-react';
import './Sidebar.css';

const Sidebar = ({ currentPage, setCurrentPage, isAdmin }) => {
  const menuCategories = [
    {
      title: 'GENERAL',
      items: [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: Home,
          description: 'System overview & quick stats'
        },
        {
          id: 'optimizer',
          label: 'Quick Optimizer',
          icon: Zap,
          description: 'One-click optimization'
        }
      ]
    },
    {
      title: 'HARDWARE',
      items: [
        {
          id: 'cpu',
          label: 'CPU',
          icon: Cpu,
          description: 'Processor tweaks & overclocking',
          requiresAdmin: true
        },
        {
          id: 'gpu',
          label: 'GPU',
          icon: Monitor,
          description: 'Graphics optimization',
          requiresAdmin: true
        },
        {
          id: 'memory',
          label: 'Memory',
          icon: MemoryStick,
          description: 'RAM optimization',
          requiresAdmin: true
        },
        {
          id: 'bios',
          label: 'BIOS',
          icon: Settings2,
          description: 'Hardware-level tweaks',
          requiresAdmin: true,
          advanced: true
        }
      ]
    },
    {
      title: 'SYSTEM',
      items: [
        {
          id: 'network',
          label: 'Network',
          icon: Wifi,
          description: 'Internet & latency optimization',
          requiresAdmin: true
        },
        {
          id: 'debloater',
          label: 'Debloater',
          icon: Trash2,
          description: 'Remove bloatware',
          requiresAdmin: true
        },
        {
          id: 'services',
          label: 'Services',
          icon: Settings,
          description: 'Windows services optimization',
          requiresAdmin: true
        },
        {
          id: 'startup',
          label: 'Startup',
          icon: Activity,
          description: 'Boot optimization',
          requiresAdmin: true
        }
      ]
    },
    {
      title: 'ADVANCED',
      items: [
        {
          id: 'registry',
          label: 'Registry',
          icon: Wrench,
          description: '2000+ registry tweaks',
          requiresAdmin: true,
          advanced: true
        },
        {
          id: 'kernel',
          label: 'Kernel',
          icon: Shield,
          description: 'Kernel-level optimizations',
          requiresAdmin: true,
          advanced: true
        },
        {
          id: 'security',
          label: 'Security',
          icon: Shield,
          description: 'Security vs performance',
          requiresAdmin: true,
          advanced: true
        }
      ]
    },
    {
      title: 'MONITORING',
      items: [
        {
          id: 'monitor',
          label: 'Performance',
          icon: BarChart3,
          description: 'Real-time FPS & metrics'
        },
        {
          id: 'benchmarks',
          label: 'Benchmarks',
          icon: TrendingUp,
          description: 'System benchmarking'
        }
      ]
    },
    {
      title: 'TOOLS',
      items: [
        {
          id: 'backup',
          label: 'Backup',
          icon: HardDrive,
          description: 'System backup & restore'
        },
        {
          id: 'settings',
          label: 'Settings',
          icon: Settings,
          description: 'App configuration'
        }
      ]
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <Shield size={24} />
          <div className="logo-text">
            <div className="logo-main">RODEY</div>
            <div className="logo-sub">PREMIUM</div>
          </div>
        </div>

        <div className="admin-status">
          <div className={`admin-indicator ${isAdmin ? 'admin-active' : 'admin-inactive'}`}>
            <Shield size={12} />
            <span>{isAdmin ? 'Admin' : 'Limited'}</span>
          </div>
        </div>
      </div>

      <nav className="sidebar-nav">
        {menuCategories.map((category, categoryIndex) => (
          <div key={categoryIndex} className="nav-category">
            <div className="category-header">
              <span className="category-title">{category.title}</span>
            </div>

            <div className="category-items">
              {category.items.map((item) => {
                const Icon = item.icon;
                const isDisabled = item.requiresAdmin && !isAdmin;
                const isActive = currentPage === item.id;

                return (
                  <button
                    key={item.id}
                    className={`nav-item ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''} ${item.advanced ? 'advanced' : ''}`}
                    onClick={() => !isDisabled && setCurrentPage(item.id)}
                    disabled={isDisabled}
                    title={isDisabled ? 'Requires administrator privileges' : item.description}
                  >
                    <div className="nav-icon">
                      <Icon size={18} />
                      {item.advanced && (
                        <div className="advanced-badge">PRO</div>
                      )}
                    </div>
                    <div className="nav-content">
                      <div className="nav-label">{item.label}</div>
                      <div className="nav-description">{item.description}</div>
                    </div>
                    {isDisabled && (
                      <div className="lock-icon">
                        <Shield size={12} />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        ))}
      </nav>

      <div className="sidebar-footer">
        <div className="performance-stats">
          <div className="stat-item">
            <Gauge size={16} />
            <div className="stat-content">
              <div className="stat-label">FPS Boost</div>
              <div className="stat-value">+247%</div>
            </div>
          </div>
          <div className="stat-item">
            <HardDrive size={16} />
            <div className="stat-content">
              <div className="stat-label">RAM Freed</div>
              <div className="stat-value">2.4 GB</div>
            </div>
          </div>
        </div>

        <div className="version-info">
          <div className="version">v1.0.0 Premium</div>
          <div className="build">Build 2024.1</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
