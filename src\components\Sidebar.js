import React from 'react';
import {
  Home,
  Zap,
  Settings2,
  Trash2,
  Cpu,
  Activity,
  Settings,
  Shield,
  Gauge,
  HardDrive
} from 'lucide-react';
import './Sidebar.css';

const Sidebar = ({ currentPage, setCurrentPage, isAdmin }) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      description: 'System overview'
    },
    {
      id: 'optimizer',
      label: 'Quick Optimizer',
      icon: Zap,
      description: 'One-click optimization'
    },
    {
      id: 'cpu',
      label: 'CPU Optimization',
      icon: Cpu,
      description: 'Processor tweaks & overclocking',
      requiresAdmin: true
    },
    {
      id: 'gpu',
      label: 'GPU Optimization',
      icon: HardDrive,
      description: 'Graphics card optimization',
      requiresAdmin: true
    },
    {
      id: 'memory',
      label: 'Memory & RAM',
      icon: Activity,
      description: 'Memory optimization',
      requiresAdmin: true
    },
    {
      id: 'bios',
      label: 'BIOS/UEFI Tweaks',
      icon: Settings2,
      description: 'Hardware-level optimization',
      requiresAdmin: true,
      advanced: true
    },
    {
      id: 'network',
      label: 'Network Optimization',
      icon: Gauge,
      description: 'Internet & latency tweaks',
      requiresAdmin: true
    },
    {
      id: 'debloater',
      label: 'System Debloater',
      icon: Trash2,
      description: 'Remove bloatware & services',
      requiresAdmin: true
    },
    {
      id: 'advanced',
      label: 'Advanced Tweaks',
      icon: Shield,
      description: '2000+ registry tweaks',
      requiresAdmin: true,
      advanced: true
    },
    {
      id: 'monitor',
      label: 'Performance Monitor',
      icon: Activity,
      description: 'Real-time FPS & metrics'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'App configuration'
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <Shield size={24} />
          <div className="logo-text">
            <div className="logo-main">RODEY</div>
            <div className="logo-sub">PREMIUM</div>
          </div>
        </div>

        <div className="admin-status">
          <div className={`admin-indicator ${isAdmin ? 'admin-active' : 'admin-inactive'}`}>
            <Shield size={12} />
            <span>{isAdmin ? 'Admin' : 'Limited'}</span>
          </div>
        </div>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isDisabled = item.requiresAdmin && !isAdmin;
          const isActive = currentPage === item.id;

          return (
            <button
              key={item.id}
              className={`nav-item ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''} ${item.advanced ? 'advanced' : ''}`}
              onClick={() => !isDisabled && setCurrentPage(item.id)}
              disabled={isDisabled}
              title={isDisabled ? 'Requires administrator privileges' : item.description}
            >
              <div className="nav-icon">
                <Icon size={20} />
                {item.advanced && (
                  <div className="advanced-badge">PRO</div>
                )}
              </div>
              <div className="nav-content">
                <div className="nav-label">{item.label}</div>
                <div className="nav-description">{item.description}</div>
              </div>
              {isDisabled && (
                <div className="lock-icon">
                  <Shield size={14} />
                </div>
              )}
            </button>
          );
        })}
      </nav>

      <div className="sidebar-footer">
        <div className="performance-stats">
          <div className="stat-item">
            <Gauge size={16} />
            <div className="stat-content">
              <div className="stat-label">FPS Boost</div>
              <div className="stat-value">+247%</div>
            </div>
          </div>
          <div className="stat-item">
            <HardDrive size={16} />
            <div className="stat-content">
              <div className="stat-label">RAM Freed</div>
              <div className="stat-value">2.4 GB</div>
            </div>
          </div>
        </div>

        <div className="version-info">
          <div className="version">v1.0.0 Premium</div>
          <div className="build">Build 2024.1</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
