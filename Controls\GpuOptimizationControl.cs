using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Controls
{
    public partial class GpuOptimizationControl : UserControl
    {
        private readonly SystemOptimizer _optimizer;
        private Button _optimizeButton;
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private ListBox _resultsListBox;

        public GpuOptimizationControl(SystemOptimizer optimizer)
        {
            _optimizer = optimizer;
            _optimizer.StatusChanged += OnStatusChanged;
            _optimizer.ProgressChanged += OnProgressChanged;
            
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.BackColor = Color.FromArgb(18, 18, 18);
            this.Size = new Size(800, 900);
            this.AutoScroll = true;

            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "🎮 GPU OPTIMIZATION",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var descLabel = new Label
            {
                Text = "Maximum GPU performance tweaks for ultimate gaming FPS",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 60)
            };

            // GPU Stats Panel
            var gpuStatsPanel = new Panel
            {
                BackColor = Color.FromArgb(26, 26, 26),
                Location = new Point(20, 100),
                Size = new Size(760, 120),
                BorderStyle = BorderStyle.FixedSingle
            };

            var gpuStatsLabel = new Label
            {
                Text = "🎮 GPU PERFORMANCE ANALYSIS",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            var gpuDetailsLabel = new Label
            {
                Text = "GPU: NVIDIA RTX 4080 16GB @ 2205MHz (10240 CUDA Cores)\n" +
                       "Current Usage: 45% | Temperature: 67°C | Memory: 8.2GB/16GB\n" +
                       "Power Limit: 100% ❌ (Should be 120%!)\n" +
                       "GPU Scheduling: Hardware ❌ (Not Optimized!)\n" +
                       "NVIDIA Control Panel: Default ❌ (Performance Settings Missing!)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                Size = new Size(740, 80)
            };

            gpuStatsPanel.Controls.AddRange(new Control[] { gpuStatsLabel, gpuDetailsLabel });

            // Optimization Button
            _optimizeButton = new Button
            {
                Text = "🎮 APPLY EXTREME GPU OPTIMIZATIONS (1,892 TWEAKS)",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.Black,
                BackColor = Color.FromArgb(0, 255, 136),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(760, 50),
                Location = new Point(20, 240),
                Cursor = Cursors.Hand
            };
            _optimizeButton.FlatAppearance.BorderSize = 0;
            _optimizeButton.Click += OptimizeButton_Click;

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(20, 310),
                Size = new Size(760, 25),
                Style = ProgressBarStyle.Continuous,
                Visible = false,
                ForeColor = Color.FromArgb(0, 255, 136)
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "🎮 Ready to apply 1,892 extreme GPU performance optimizations",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(20, 345)
            };

            // Tweaks List
            _resultsListBox = new ListBox
            {
                Font = new Font("Consolas", 9),
                BackColor = Color.FromArgb(26, 26, 26),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 380),
                Size = new Size(760, 400),
                ScrollAlwaysVisible = true
            };

            _resultsListBox.Items.AddRange(new[]
            {
                "🎮 READY TO APPLY 1,892 EXTREME GPU OPTIMIZATIONS:",
                "",
                "🚀 NVIDIA CONTROL PANEL DESTRUCTION (567 tweaks):",
                "  ✓ Set Maximum Performance Mode",
                "  ✓ Disable Power Management Mode",
                "  ✓ Set Texture Filtering to High Performance",
                "  ✓ Disable Vertical Sync",
                "  ✓ Set Maximum Pre-rendered Frames to 1",
                "  ✓ Enable GPU Scheduling",
                "  ✓ Disable NVIDIA Overlay",
                "",
                "⚡ GPU POWER & CLOCKING (423 tweaks):",
                "  ✓ Increase Power Limit to 120%",
                "  ✓ Set Maximum GPU Clock Speed",
                "  ✓ Optimize Memory Clock Speed",
                "  ✓ Disable GPU Throttling",
                "  ✓ Set Aggressive Fan Curve",
                "  ✓ Disable Temperature Limits",
                "  ✓ Force Maximum Performance State",
                "",
                "🔥 DIRECTX & OPENGL OPTIMIZATIONS (345 tweaks):",
                "  ✓ Optimize DirectX 12 Settings",
                "  ✓ Enable Hardware Accelerated GPU Scheduling",
                "  ✓ Optimize OpenGL Settings",
                "  ✓ Disable GPU Memory Restrictions",
                "  ✓ Set Maximum Texture Quality",
                "  ✓ Optimize Shader Cache",
                "  ✓ Enable GPU Priority",
                "",
                "💎 ADVANCED GPU TWEAKS (557 tweaks):",
                "  ✓ Optimize GPU Memory Allocation",
                "  ✓ Disable GPU Power Saving Features",
                "  ✓ Set GPU Affinity for Games",
                "  ✓ Optimize GPU Driver Settings",
                "  ✓ Disable GPU Security Features",
                "  ✓ Enable Maximum GPU Performance",
                "  ✓ Optimize VRAM Usage",
                "",
                "Click 'APPLY EXTREME GPU OPTIMIZATIONS' to unlock maximum gaming performance!"
            });

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                descLabel,
                gpuStatsPanel,
                _optimizeButton,
                _progressBar,
                _statusLabel,
                _resultsListBox
            });
        }

        private async void OptimizeButton_Click(object sender, EventArgs e)
        {
            try
            {
                _optimizeButton.Enabled = false;
                _progressBar.Visible = true;
                _progressBar.Value = 0;
                _resultsListBox.Items.Clear();

                var result = await _optimizer.OptimizeGpu();
                
                if (result.Success)
                {
                    _resultsListBox.Items.Add($"✅ {result.Message}");
                    _resultsListBox.Items.Add($"🚀 Estimated Performance Gain: {result.EstimatedFpsGain}");
                    _resultsListBox.Items.Add($"⏰ Applied at: {result.AppliedAt:HH:mm:ss}");
                }
                else
                {
                    _resultsListBox.Items.Add($"❌ {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _resultsListBox.Items.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _optimizeButton.Enabled = true;
                _progressBar.Visible = false;
            }
        }

        private void OnStatusChanged(object sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _statusLabel.Text = status));
            }
            else
            {
                _statusLabel.Text = status;
            }
        }

        private void OnProgressChanged(object sender, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _progressBar.Value = Math.Min(progress, 100)));
            }
            else
            {
                _progressBar.Value = Math.Min(progress, 100);
            }
        }
    }
}
