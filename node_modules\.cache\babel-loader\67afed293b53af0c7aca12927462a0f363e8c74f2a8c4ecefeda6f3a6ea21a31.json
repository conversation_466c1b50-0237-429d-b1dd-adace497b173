{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst AlignEndVertical = createLucideIcon(\"AlignEndVertical\", [[\"rect\", {\n  width: \"16\",\n  height: \"6\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"10wcwx\"\n}], [\"rect\", {\n  width: \"9\",\n  height: \"6\",\n  x: \"9\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"4p5bwg\"\n}], [\"path\", {\n  d: \"M22 22V2\",\n  key: \"12ipfv\"\n}]]);\nexport { AlignEndVertical as default };", "map": {"version": 3, "names": ["AlignEndVertical", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\align-end-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignEndVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iNiIgeD0iMiIgeT0iNCIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjkiIGhlaWdodD0iNiIgeD0iOSIgeT0iMTQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yMiAyMlYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-end-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignEndVertical = createLucideIcon('AlignEndVertical', [\n  ['rect', { width: '16', height: '6', x: '2', y: '4', rx: '2', key: '10wcwx' }],\n  ['rect', { width: '9', height: '6', x: '9', y: '14', rx: '2', key: '4p5bwg' }],\n  ['path', { d: 'M22 22V2', key: '12ipfv' }],\n]);\n\nexport default AlignEndVertical;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}