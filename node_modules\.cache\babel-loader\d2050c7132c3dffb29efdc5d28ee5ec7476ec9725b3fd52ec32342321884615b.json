{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst DoorClosed = createLucideIcon(\"DoorClosed\", [[\"path\", {\n  d: \"M18 20V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14\",\n  key: \"36qu9e\"\n}], [\"path\", {\n  d: \"M2 20h20\",\n  key: \"owomy5\"\n}], [\"path\", {\n  d: \"M14 12v.01\",\n  key: \"xfcn54\"\n}]]);\nexport { DoorClosed as default };", "map": {"version": 3, "names": ["DoorClosed", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\door-closed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name DoorClosed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMjBWNmEyIDIgMCAwIDAtMi0ySDhhMiAyIDAgMCAwLTIgMnYxNCIgLz4KICA8cGF0aCBkPSJNMiAyMGgyMCIgLz4KICA8cGF0aCBkPSJNMTQgMTJ2LjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/door-closed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DoorClosed = createLucideIcon('DoorClosed', [\n  ['path', { d: 'M18 20V6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14', key: '36qu9e' }],\n  ['path', { d: 'M2 20h20', key: 'owomy5' }],\n  ['path', { d: 'M14 12v.01', key: 'xfcn54' }],\n]);\n\nexport default DoorClosed;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}