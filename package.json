{"name": "rodey-premium-tweaker", "version": "1.0.0", "description": "Professional Windows Tweaking Software - Ultimate Performance Optimizer", "main": "src/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev-react\" \"wait-on http://localhost:3000 && electron .\"", "dev-react": "react-scripts start", "build": "react-scripts build", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir", "test": "react-scripts test"}, "keywords": ["windows", "tweaking", "optimization", "performance", "fps", "gaming", "debloat"], "author": "Rodey Premium", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "concurrently": "^8.0.0", "electron": "^27.0.0", "electron-builder": "^24.0.0", "typescript": "^5.0.0", "wait-on": "^7.0.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.0", "framer-motion": "^10.0.0", "lucide-react": "^0.300.0", "recharts": "^2.8.0", "electron-store": "^8.1.0", "node-powershell": "^5.0.0", "winreg": "^1.2.4", "systeminformation": "^5.21.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "build": {"appId": "com.rodeypremium.tweaker", "productName": "Rodey Premium Tweaker", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main.js", "src/preload.js", "src/tweaks/**/*", "src/utils/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "requireAdministrator"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}