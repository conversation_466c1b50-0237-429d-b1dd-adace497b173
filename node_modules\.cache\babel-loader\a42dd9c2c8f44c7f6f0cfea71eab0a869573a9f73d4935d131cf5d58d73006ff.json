{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FilePieChart = createLucideIcon(\"File<PERSON><PERSON><PERSON><PERSON>\", [[\"path\", {\n  d: \"M16 22h2a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3\",\n  key: \"zhyrez\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M4.04 11.71a5.84 5.84 0 1 0 8.2 8.29\",\n  key: \"f1t5jc\"\n}], [\"path\", {\n  d: \"M13.83 16A5.83 5.83 0 0 0 8 10.17V16h5.83Z\",\n  key: \"7q54ec\"\n}]]);\nexport { FilePieChart as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "points"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\file-pie-chart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FilePieChart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjJoMmEyIDIgMCAwIDAgMi0yVjcuNUwxNC41IDJINmEyIDIgMCAwIDAtMiAydjMiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0iTTQuMDQgMTEuNzFhNS44NCA1Ljg0IDAgMSAwIDguMiA4LjI5IiAvPgogIDxwYXRoIGQ9Ik0xMy44MyAxNkE1LjgzIDUuODMgMCAwIDAgOCAxMC4xN1YxNmg1LjgzWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-pie-chart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FilePieChart = createLucideIcon('FilePieChart', [\n  ['path', { d: 'M16 22h2a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3', key: 'zhyrez' }],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'M4.04 11.71a5.84 5.84 0 1 0 8.2 8.29', key: 'f1t5jc' }],\n  ['path', { d: 'M13.83 16A5.83 5.83 0 0 0 8 10.17V16h5.83Z', key: '7q54ec' }],\n]);\n\nexport default FilePieChart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}