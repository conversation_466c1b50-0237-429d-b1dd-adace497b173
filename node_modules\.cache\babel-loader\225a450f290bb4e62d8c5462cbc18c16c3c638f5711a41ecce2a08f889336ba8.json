{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CalendarSearch = createLucideIcon(\"CalendarSearch\", [[\"path\", {\n  d: \"M21 12V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h7.5\",\n  key: \"18ncp8\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M3 10h18\",\n  key: \"8toen8\"\n}], [\"path\", {\n  d: \"M18 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6v0Z\",\n  key: \"mgbru4\"\n}], [\"path\", {\n  d: \"m22 22-1.5-1.5\",\n  key: \"1x83k4\"\n}]]);\nexport { CalendarSearch as default };", "map": {"version": 3, "names": ["CalendarSearch", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\calendar-search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CalendarSearch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJWNmEyIDIgMCAwIDAtMi0ySDVhMiAyIDAgMCAwLTIgMnYxNGMwIDEuMS45IDIgMiAyaDcuNSIgLz4KICA8cGF0aCBkPSJNMTYgMnY0IiAvPgogIDxwYXRoIGQ9Ik04IDJ2NCIgLz4KICA8cGF0aCBkPSJNMyAxMGgxOCIgLz4KICA8cGF0aCBkPSJNMTggMjFhMyAzIDAgMSAwIDAtNiAzIDMgMCAwIDAgMCA2djBaIiAvPgogIDxwYXRoIGQ9Im0yMiAyMi0xLjUtMS41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar-search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CalendarSearch = createLucideIcon('CalendarSearch', [\n  ['path', { d: 'M21 12V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h7.5', key: '18ncp8' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n  ['path', { d: 'M18 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6v0Z', key: 'mgbru4' }],\n  ['path', { d: 'm22 22-1.5-1.5', key: '1x83k4' }],\n]);\n\nexport default CalendarSearch;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,6DAA+D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}