{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Pizza = createLucideIcon(\"Pizza\", [[\"path\", {\n  d: \"M15 11h.01\",\n  key: \"rns66s\"\n}], [\"path\", {\n  d: \"M11 15h.01\",\n  key: \"k85uqc\"\n}], [\"path\", {\n  d: \"M16 16h.01\",\n  key: \"1f9h7w\"\n}], [\"path\", {\n  d: \"m2 16 20 6-6-20A20 20 0 0 0 2 16\",\n  key: \"e4slt2\"\n}], [\"path\", {\n  d: \"M5.71 17.11a17.04 17.04 0 0 1 11.4-11.4\",\n  key: \"rerf8f\"\n}]]);\nexport { Pizza as default };", "map": {"version": 3, "names": ["Pizza", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\pizza.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pizza\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMTFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMSAxNWguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDE2aC4wMSIgLz4KICA8cGF0aCBkPSJtMiAxNiAyMCA2LTYtMjBBMjAgMjAgMCAwIDAgMiAxNiIgLz4KICA8cGF0aCBkPSJNNS43MSAxNy4xMWExNy4wNCAxNy4wNCAwIDAgMSAxMS40LTExLjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pizza\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pizza = createLucideIcon('Pizza', [\n  ['path', { d: 'M15 11h.01', key: 'rns66s' }],\n  ['path', { d: 'M11 15h.01', key: 'k85uqc' }],\n  ['path', { d: 'M16 16h.01', key: '1f9h7w' }],\n  ['path', { d: 'm2 16 20 6-6-20A20 20 0 0 0 2 16', key: 'e4slt2' }],\n  ['path', { d: 'M5.71 17.11a17.04 17.04 0 0 1 11.4-11.4', key: 'rerf8f' }],\n]);\n\nexport default Pizza;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}