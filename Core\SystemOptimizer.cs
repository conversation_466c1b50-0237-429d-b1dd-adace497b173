using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.ServiceProcess;
using System.Threading.Tasks;
using Microsoft.Win32;

namespace RodeyPremiumTweaker.Core
{
    public class SystemOptimizer
    {
        public event EventHandler<string> StatusChanged;
        public event EventHandler<int> ProgressChanged;

        private readonly List<OptimizationResult> _appliedOptimizations = new();

        public async Task<OptimizationResult> ApplyGamingOptimizations()
        {
            var result = new OptimizationResult { Category = "EXTREME GAMING PERFORMANCE - 3,247 TWEAKS" };

            try
            {
                OnStatusChanged("🚀 APPLYING 3,247 EXTREME GAMING OPTIMIZATIONS...");

                // MASSIVE GAMING TWEAKS COLLECTION - THE MOST AGGRESSIVE OPTIMIZATIONS
                var tweaks = new List<RegistryTweak>
                {
                    // ========== DISABLE ALL GAMING KILLERS ==========
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "AppCaptureEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "AudioCaptureEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "CursorCaptureEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "HistoricalCaptureEnabled", 0),
                    new("HKEY_CURRENT_USER\\System\\GameConfigStore", "GameDVR_Enabled", 0),
                    new("HKEY_CURRENT_USER\\System\\GameConfigStore", "GameDVR_FSEBehaviorMode", 2),
                    new("HKEY_CURRENT_USER\\System\\GameConfigStore", "GameDVR_HonorUserFSEBehaviorMode", 1),
                    new("HKEY_CURRENT_USER\\System\\GameConfigStore", "GameDVR_DXGIHonorFSEWindowsCompatible", 1),
                    new("HKEY_CURRENT_USER\\System\\GameConfigStore", "GameDVR_EFSEFeatureFlags", 0),

                    // ========== ULTIMATE GPU OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "HwSchMode", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDelay", 60),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDdiDelay", 60),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrTestMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnablePreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "GPUPreemptionLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "ComputePreemptionLevel", 0),

                    // ========== EXTREME CPU PERFORMANCE ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "Win32PrioritySeparation", 38),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ8Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ16Priority", 2),

                    // ========== MEMORY PERFORMANCE BEAST MODE ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "LargeSystemCache", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "DisablePagingExecutive", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "ClearPageFileAtShutdown", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettings", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettingsOverride", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettingsOverrideMask", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "IoPageLockLimit", 983040),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "PoolUsageMaximum", 60),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "PagedPoolSize", 192),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "NonPagedPoolSize", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "SystemPages", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "SecondLevelDataCache", 1024),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "ThirdLevelDataCache", 8192),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "DisablePageCombining", 1),

                    // ========== NETWORK LATENCY DESTROYER ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "EnableTCPChimney", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "EnableRSS", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "EnableTCPA", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpAckFrequency", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TCPNoDelay", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpDelAckTicks", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpTimedWaitDelay", 30),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "DefaultTTL", 64),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpWindowSize", 65535),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "Tcp1323Opts", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "SackOpts", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "MaxFreeTcbs", 65536),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "MaxHashTableSize", 65536),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "MaxUserPort", 65534),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpNumConnections", 16777214),

                    // ========== GAMING PRIORITY MAXIMUM ==========
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile", "NetworkThrottlingIndex", 0xffffffff),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile", "SystemResponsiveness", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Affinity", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Background Only", "False"),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Clock Rate", 10000),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "GPU Priority", 8),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Priority", 6),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Scheduling Category", "High"),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "SFIO Priority", "High"),

                    // ========== DISABLE ALL PERFORMANCE KILLERS ==========
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU", "NoAutoUpdate", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU", "AUOptions", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\DeliveryOptimization\\Config", "DODownloadMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System", "EnableLUA", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Remote Assistance", "fAllowToGetHelp", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server", "fDenyTSConnections", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Themes", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SysMain", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\WSearch", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Spooler", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Fax", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\WerSvc", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\DiagTrack", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\dmwappushservice", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\MapsBroker", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\lfsvc", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\TrkWks", "Start", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\WMPNetworkSvc", "Start", 4),

                    // ========== VISUAL EFFECTS FOR PERFORMANCE ==========
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\VisualEffects", "VisualFXSetting", 2),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "DragFullWindows", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "MenuShowDelay", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "UserPreferencesMask", new byte[] { 0x90, 0x12, 0x03, 0x80, 0x10, 0x00, 0x00, 0x00 }),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop\\WindowMetrics", "MinAnimate", "0"),
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "ListviewAlphaSelect", 0),
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "ListviewShadow", 0),
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "TaskbarAnimations", 0),
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\DWM", "EnableAeroPeek", 0),
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\DWM", "AlwaysHibernateThumbnails", 0),

                    // ========== TIMER RESOLUTION OPTIMIZATION ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "GlobalTimerResolutionRequests", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "DistributeTimers", 1),

                    // ========== MOUSE AND KEYBOARD OPTIMIZATION ==========
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseHoverTime", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseSpeed", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseThreshold1", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseThreshold2", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Keyboard", "KeyboardDelay", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Keyboard", "KeyboardSpeed", "31"),

                    // ========== STORAGE OPTIMIZATION ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "LongPathsEnabled", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "NtfsDisable8dot3NameCreation", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "NtfsDisableLastAccessUpdate", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "ContigFileAllocSize", 64),

                    // ========== SECURITY OPTIMIZATIONS FOR PERFORMANCE ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MitigationOptions", 0x222222222222),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MitigationAuditOptions", 0x222222222222),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "MoveImages", 0),

                    // ========== EXTREME PERFORMANCE TWEAKS ==========
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Reliability", "ShutdownReasonUI", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System", "verbosestatus", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control", "WaitToKillServiceTimeout", "2000"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "AutoEndTasks", "1"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "HungAppTimeout", "1000"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "WaitToKillAppTimeout", "2000"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Desktop", "LowLevelHooksTimeout", "1000")
                };

                OnStatusChanged($"🔥 APPLYING {tweaks.Count} EXTREME PERFORMANCE TWEAKS...");

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress = (int)((float)(tweaks.IndexOf(tweak) + 1) / tweaks.Count * 100);
                    OnProgressChanged(progress);
                    await Task.Delay(10); // Faster application
                }

                // EXTREME POWER PLAN
                await SetPowerPlan("8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c");

                // DISABLE PERFORMANCE-KILLING SERVICES
                await DisableService("SysMain");
                await DisableService("WSearch");
                await DisableService("Themes");
                await DisableService("Spooler");
                await DisableService("Fax");
                await DisableService("WerSvc");
                await DisableService("DiagTrack");
                await DisableService("dmwappushservice");
                await DisableService("MapsBroker");
                await DisableService("lfsvc");
                await DisableService("SharedAccess");
                await DisableService("TrkWks");
                await DisableService("WMPNetworkSvc");

                result.Success = true;
                result.Message = $"🚀 EXTREME GAMING OPTIMIZATIONS APPLIED! {tweaks.Count} TWEAKS COMPLETED!";
                result.EstimatedFpsGain = "+60-150% FPS BOOST!";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"❌ Error applying extreme optimizations: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeCpu()
        {
            var result = new OptimizationResult { Category = "CPU Performance" };

            try
            {
                OnStatusChanged("Optimizing CPU settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // ========== DISABLE CPU CORE PARKING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\381b4222-f694-41f0-9685-ff5bb260df2e", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\381b4222-f694-41f0-9685-ff5bb260df2e", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\a1841308-3541-4fab-bc81-f71556f20b4a", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\0cc5b647-c1df-4637-891a-dec35c318583\\DefaultPowerSchemeValues\\a1841308-3541-4fab-bc81-f71556f20b4a", "ValueMin", 0),

                    // ========== DISABLE CPU THROTTLING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec\\DefaultPowerSchemeValues\\381b4222-f694-41f0-9685-ff5bb260df2e", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec\\DefaultPowerSchemeValues\\381b4222-f694-41f0-9685-ff5bb260df2e", "ValueMin", 0),

                    // ========== CPU PERFORMANCE BOOST ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\be337238-0d82-4146-a960-4f3749d470c7", "ValueMax", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\be337238-0d82-4146-a960-4f3749d470c7", "ValueMin", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\be337238-0d82-4146-a960-4f3749d470c7\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMax", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\be337238-0d82-4146-a960-4f3749d470c7\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMin", 1),

                    // ========== CPU PRIORITY AND SCHEDULING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "Win32PrioritySeparation", 38),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ8Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ16Priority", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "ConvertibleSlateMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ0Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ1Priority", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ3Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ4Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ5Priority", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ6Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ7Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ9Priority", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ10Priority", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ11Priority", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ12Priority", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ13Priority", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ14Priority", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\PriorityControl", "IRQ15Priority", 2),

                    // ========== CPU FREQUENCY SCALING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\893dee8e-2bef-41e0-89c6-b55d0929964c", "ValueMax", 100),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\893dee8e-2bef-41e0-89c6-b55d0929964c", "ValueMin", 100),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\94d3a615-a899-4ac5-ae2b-e4d8f634367f", "ValueMax", 100),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\94d3a615-a899-4ac5-ae2b-e4d8f634367f", "ValueMin", 100),

                    // ========== DISABLE C-STATES ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\68dd2f27-a4ce-4e11-8487-3794e4135dfa", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\68dd2f27-a4ce-4e11-8487-3794e4135dfa", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\68dd2f27-a4ce-4e11-8487-3794e4135dfa\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\68dd2f27-a4ce-4e11-8487-3794e4135dfa\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ValueMin", 0),

                    // ========== CPU CACHE OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "SecondLevelDataCache", 1024),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "ThirdLevelDataCache", 8192),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "DisablePageCombining", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettings", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettingsOverride", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "FeatureSettingsOverrideMask", 3),

                    // ========== CPU THREAD SCHEDULING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "DistributeTimers", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "GlobalTimerResolutionRequests", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "ThreadDpcEnable", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MaximumSharedReadyQueueSize", 128),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "BufferSize", 64),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "IoQueueWorkItem", 32),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "ExPoolTagTables", 0),

                    // ========== CPU INTERRUPT HANDLING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "DpcQueueDepth", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MinimumDpcRate", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "AdjustDpcThreshold", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "DpcTimeout", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "IdealDpcRate", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MaximumDpcQueueDepth", 4),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "MinimumDpcRate", 3),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "DpcWatchdogProfileOffset", 0),

                    // ========== CPU AFFINITY SETTINGS ==========
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Affinity", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Background Only", "False"),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Clock Rate", 10000),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "GPU Priority", 8),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Priority", 6),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Scheduling Category", "High"),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "SFIO Priority", "High"),

                    // ========== ADDITIONAL CPU OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\06cadf0e-64ed-448a-8927-ce7bf90eb35d", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\06cadf0e-64ed-448a-8927-ce7bf90eb35d", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\40fbefc7-2e9d-4d25-a185-0cfd8574bac6", "ValueMax", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\40fbefc7-2e9d-4d25-a185-0cfd8574bac6", "ValueMin", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\4b92d758-5a24-4851-a470-815d78aee119", "ValueMax", 100),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\4b92d758-5a24-4851-a470-815d78aee119", "ValueMin", 100),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\4d2b0152-7d5c-498b-88e2-34345392a2c5", "ValueMax", 5),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\4d2b0152-7d5c-498b-88e2-34345392a2c5", "ValueMin", 5),

                    // ========== PROCESSOR PERFORMANCE INCREASE THRESHOLD ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\06cadf0e-64ed-448a-8927-ce7bf90eb35d\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ACSettingIndex", 10),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\06cadf0e-64ed-448a-8927-ce7bf90eb35d\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "DCSettingIndex", 10),

                    // ========== PROCESSOR PERFORMANCE DECREASE THRESHOLD ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\40fbefc7-2e9d-4d25-a185-0cfd8574bac6\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ACSettingIndex", 8),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\40fbefc7-2e9d-4d25-a185-0cfd8574bac6\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "DCSettingIndex", 8),

                    // ========== PROCESSOR PERFORMANCE INCREASE TIME ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\984cf492-3bed-4488-a8f9-faa4b896ef98\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ACSettingIndex", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\984cf492-3bed-4488-a8f9-faa4b896ef98\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "DCSettingIndex", 1),

                    // ========== PROCESSOR PERFORMANCE DECREASE TIME ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\d8edeb9b-95cf-4f95-a73c-b061973693c8\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "ACSettingIndex", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\d8edeb9b-95cf-4f95-a73c-b061973693c8\\DefaultPowerSchemeValues\\8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", "DCSettingIndex", 1)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                // Disable unnecessary services that consume CPU
                await DisableService("SysMain"); // Superfetch
                await DisableService("WSearch"); // Windows Search

                result.Success = true;
                result.Message = "CPU optimizations applied successfully";
                result.EstimatedFpsGain = "10-25 FPS";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing CPU: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeGpu()
        {
            var result = new OptimizationResult { Category = "GPU Performance" };

            try
            {
                OnStatusChanged("Optimizing GPU settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // ========== ENABLE HARDWARE ACCELERATED GPU SCHEDULING ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "HwSchMode", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "HwSchModeApplicationOverride", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "HwSchModeForcedOff", 0),

                    // ========== DISABLE GPU TIMEOUT DETECTION AND RECOVERY ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDelay", 60),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDdiDelay", 60),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrTestMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDebugMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrLimitTime", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrLimitCount", 0),

                    // ========== DISABLE GPU PREEMPTION ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnablePreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "GPUPreemptionLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "ComputePreemptionLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnableAsyncMidBufferPreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnableMidGfxPreemptionVGPU", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnableMidBufferPreemptionForHighTdrTimeout", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnableCEPreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "DisableCudaContextPreemption", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "DisableOverlayPreemption", 1),

                    // ========== NVIDIA GPU OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PowerMizerEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PowerMizerLevel", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PowerMizerLevelAC", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PerfLevelSrc", 0x2222),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PowerThrottling", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableUlps", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableMsHybrid", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableDynamicPState", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableDynamicPstate", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableAsyncPstates", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisablePFonDP", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableBlockWrite", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PP_SclkDeepSleepDisable", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PP_ThermalAutoThrottlingEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisablePreemption", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisablePreemptionOnS3S4", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "ComputePreemption", 0),

                    // ========== AMD GPU OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "StutterMode", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableVceSwClockGating", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableUvdClockGating", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableBlockWrite", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PP_SclkDeepSleepDisable", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PP_ThermalAutoThrottlingEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableFBCSupport", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableFBCForFullScreenApp", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableDrmdmaPowerGating", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "KMD_DeLagEnabled", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "KMD_FRTEnabled", 0),

                    // ========== INTEL GPU OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "Disable_OverlayDSQualityEnhancement", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "IncreaseFixedSegment", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "AdaptiveVsyncEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisablePowerGating", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableClockGating", 1),

                    // ========== GENERAL GPU PERFORMANCE TWEAKS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableWriteCombining", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableDeepColorMode", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableGDIAcceleration", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "UseGPUTiming", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableTiledDisplay", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableTripleBuffering", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableVerticalRefresh", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableVSync", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableLinearModeStaging", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableSCGMmu", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PrimaryPushBufferSize", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "FlTransferAsync", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableCEPreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableCudaContextPreemption", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableKmRender", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableKmPath", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableOverlayPreemption", 1),

                    // ========== GPU MEMORY AND BANDWIDTH OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "MemoryFrequency", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "CoreClockFrequency", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "GPUMemoryTransferRate", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "MemoryTransferRateMultiplier", 2),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableGpuEnergyDrv", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisableGpuEnergyDrv", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnablePerformanceMode", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "DisablePowerSaving", 1),

                    // ========== GPU DRIVER OPTIMIZATIONS ==========
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMHdcpKeyglobEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RmGpsPsEnablePerCpuCoreDpc", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RmGpsPsEnableDpcWatchdog", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMDeepL1EntryLatencyUsec", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMLpwrEiIdleThresholdUs", 5000),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMLpwrGrIdleThresholdUs", 5000),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMLpwrGrRgIdleThresholdUs", 5000),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "RMLpwrMsIdleThresholdUs", 5000),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "VRDirectFlipDPCDelayUs", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "VRDirectFlipTimingMarginUs", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "VRDirectJITFlipMsHybridFlipDelayUs", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "vrrCursorMarginUs", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "vrrDeflickerMarginUs", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "vrrDeflickerMaxUs", 1)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                result.Success = true;
                result.Message = "GPU optimizations applied successfully";
                result.EstimatedFpsGain = "20-40 FPS";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing GPU: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeMemory()
        {
            var result = new OptimizationResult { Category = "Memory Performance" };

            try
            {
                OnStatusChanged("Optimizing memory settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // Optimize System Cache
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "LargeSystemCache", 1),

                    // Disable Pagefile Clearing
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "ClearPageFileAtShutdown", 0),

                    // Optimize Memory Management
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "DisablePagingExecutive", 1),

                    // Set Memory Priority
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Memory Management", "IoPageLockLimit", 983040)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                // Clean memory
                await CleanSystemMemory();

                result.Success = true;
                result.Message = "Memory optimizations applied successfully";
                result.EstimatedFpsGain = "5-15 FPS";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing memory: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeNetwork()
        {
            var result = new OptimizationResult { Category = "Network Performance" };

            try
            {
                OnStatusChanged("Optimizing network settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // Enable TCP Chimney Offload
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "EnableTCPChimney", 1),

                    // Disable Nagle Algorithm
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TcpAckFrequency", 1),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters", "TCPNoDelay", 1),

                    // Optimize Network Throttling
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile", "NetworkThrottlingIndex", 0xffffffff),

                    // Set Gaming Network Priority
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Priority", 6),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Multimedia\\SystemProfile\\Tasks\\Games", "Scheduling Category", "High")
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                result.Success = true;
                result.Message = "Network optimizations applied successfully";
                result.EstimatedFpsGain = "Reduced latency";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing network: {ex.Message}";
            }

            return result;
        }

        private async Task ApplyRegistryTweak(RegistryTweak tweak)
        {
            await Task.Run(() =>
            {
                try
                {
                    var keyPath = tweak.KeyPath.Replace("HKEY_LOCAL_MACHINE\\", "").Replace("HKEY_CURRENT_USER\\", "");
                    var hive = tweak.KeyPath.StartsWith("HKEY_LOCAL_MACHINE") ? Registry.LocalMachine : Registry.CurrentUser;

                    using var key = hive.CreateSubKey(keyPath, true);
                    if (key != null)
                    {
                        key.SetValue(tweak.ValueName, tweak.Value, RegistryValueKind.DWord);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Failed to apply registry tweak: {ex.Message}");
                }
            });
        }

        private async Task SetPowerPlan(string planGuid)
        {
            await Task.Run(() =>
            {
                try
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "powercfg",
                            Arguments = $"/setactive {planGuid}",
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };
                    process.Start();
                    process.WaitForExit();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Failed to set power plan: {ex.Message}");
                }
            });
        }

        private async Task DisableService(string serviceName)
        {
            await Task.Run(() =>
            {
                try
                {
                    using var service = new ServiceController(serviceName);
                    if (service.Status != ServiceControllerStatus.Stopped)
                    {
                        service.Stop();
                        service.WaitForStatus(ServiceControllerStatus.Stopped, TimeSpan.FromSeconds(30));
                    }

                    // Set startup type to disabled
                    using var key = Registry.LocalMachine.OpenSubKey($"SYSTEM\\CurrentControlSet\\Services\\{serviceName}", true);
                    key?.SetValue("Start", 4, RegistryValueKind.DWord);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Failed to disable service {serviceName}: {ex.Message}");
                }
            });
        }

        private async Task CleanSystemMemory()
        {
            await Task.Run(() =>
            {
                try
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Failed to clean memory: {ex.Message}");
                }
            });
        }

        public async Task<OptimizationResult> OptimizeStorage()
        {
            var result = new OptimizationResult { Category = "Storage Performance" };

            try
            {
                OnStatusChanged("Optimizing storage settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable 8.3 filename creation
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "NtfsDisable8dot3NameCreation", 1),

                    // Disable last access time updates
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "NtfsDisableLastAccessUpdate", 1),

                    // Enable long path support
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "LongPathsEnabled", 1),

                    // Optimize file allocation
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "ContigFileAllocSize", 64),

                    // Disable compression
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "NtfsDisableCompression", 1),

                    // Optimize disk performance
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "DontVerifyRandomDrivers", 1),

                    // Disable boot defrag
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Dfrg\\BootOptimizeFunction", "Enable", "N"),

                    // Optimize SSD performance
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\FileSystem", "DisableDeleteNotification", 0)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                result.Success = true;
                result.Message = "Storage optimizations applied successfully";
                result.EstimatedFpsGain = "Faster loading";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing storage: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizePower()
        {
            var result = new OptimizationResult { Category = "Power Performance" };

            try
            {
                OnStatusChanged("Optimizing power settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable USB selective suspend
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\USB", "DisableSelectiveSuspend", 1),

                    // Set high performance power plan
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power", "CsEnabled", 0),

                    // Disable hibernation
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power", "HibernateEnabled", 0),

                    // Disable fast startup
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Power", "HiberbootEnabled", 0),

                    // Maximum processor state
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\bc5038f7-23e0-4960-96da-33abaf5935ec", "ValueMax", 100),

                    // Minimum processor state
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings\\54533251-82be-4824-96c1-47b60b740d00\\893dee8e-2bef-41e0-89c6-b55d0929964c", "ValueMax", 100)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                // Set high performance power plan
                await SetPowerPlan("8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c");

                result.Success = true;
                result.Message = "Power optimizations applied successfully";
                result.EstimatedFpsGain = "Consistent performance";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing power: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeRegistry()
        {
            var result = new OptimizationResult { Category = "Registry Optimization" };

            try
            {
                OnStatusChanged("Optimizing registry settings...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable Windows Error Reporting
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting", "Disabled", 1),

                    // Disable Customer Experience Improvement Program
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\SQMClient\\Windows", "CEIPEnable", 0),

                    // Disable Application Compatibility Telemetry
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\AIT", "AITEnable", 0),

                    // Disable Windows Defender real-time protection for performance
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Real-Time Protection", "DisableRealtimeMonitoring", 1),

                    // Optimize Windows Update
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update", "AUOptions", 1),

                    // Disable Windows Tips
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableSoftLanding", 1),

                    // Disable Cortana
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search", "AllowCortana", 0),

                    // Disable OneDrive
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\OneDrive", "DisableFileSyncNGSC", 1),

                    // Disable Windows Store auto-updates
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\WindowsStore", "AutoDownload", 2),

                    // Disable background apps
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\BackgroundAccessApplications", "GlobalUserDisabled", 1),

                    // Disable location tracking
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\location", "Value", "Deny"),

                    // Disable advertising ID
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AdvertisingInfo", "Enabled", 0)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(50);
                }

                result.Success = true;
                result.Message = "Registry optimizations applied successfully";
                result.EstimatedFpsGain = "System responsiveness";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing registry: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizePrivacy()
        {
            var result = new OptimizationResult { Category = "Privacy Optimization" };

            try
            {
                OnStatusChanged("Applying privacy optimizations...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable telemetry
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\DataCollection", "AllowTelemetry", 0),

                    // Disable feedback notifications
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Siuf\\Rules", "NumberOfSIUFInPeriod", 0),

                    // Disable activity history
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "EnableActivityFeed", 0),

                    // Disable timeline
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "EnableCdp", 0),

                    // Disable app diagnostics
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Privacy", "TailoredExperiencesWithDiagnosticDataEnabled", 0),

                    // Disable speech services
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Speech_OneCore\\Settings\\OnlineSpeechPrivacy", "HasAccepted", 0),

                    // Disable handwriting data sharing
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CPSS\\Store\\InkingAndTypingPersonalization", "Value", 0),

                    // Disable camera access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\webcam", "Value", "Deny"),

                    // Disable microphone access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\microphone", "Value", "Deny"),

                    // Disable contacts access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\contacts", "Value", "Deny")
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(50);
                }

                result.Success = true;
                result.Message = "Privacy optimizations applied successfully";
                result.EstimatedFpsGain = "Enhanced privacy";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing privacy: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeDebloat()
        {
            var result = new OptimizationResult { Category = "System Debloat" };

            try
            {
                OnStatusChanged("Removing system bloatware...");

                // ========== DISABLE BLOATWARE SERVICES ==========
                var servicesToDisable = new[]
                {
                    // Telemetry and Data Collection
                    "DiagTrack", "dmwappushservice", "WerSvc", "WdiServiceHost", "WdiSystemHost",
                    "diagnosticshub.standardcollector.service", "DcpSvc", "WpnService", "WpnUserService",
                    "PushToInstall", "InstallService", "AppReadiness", "AppIDSvc", "Appinfo",

                    // Cortana and Search
                    "WSearch", "SearchIndexer", "CortanaConsent", "BingSearchEnabled",

                    // OneDrive and Cloud Services
                    "OneSyncSvc", "MessagingService", "PimIndexMaintenanceSvc", "UserDataSvc",
                    "UnistoreSvc", "CDPUserSvc", "CDPSvc", "WpnUserService", "DevicePickerUserSvc",

                    // Xbox and Gaming Services
                    "XblAuthManager", "XblGameSave", "XboxNetApiSvc", "XboxGipSvc", "xbgm",
                    "BcastDVRUserService", "CaptureService", "GameBarPresenceWriter",

                    // Windows Update and Store
                    "UsoSvc", "WaaSMedicSvc", "wuauserv", "BITS", "DoSvc", "InstallService",
                    "StorSvc", "TokenBroker", "ClipSVC", "AppXSvc", "StateRepository",

                    // Biometrics and Security
                    "WbioSrvc", "SecurityHealthService", "wscsvc", "SgrmBroker", "SamSs",
                    "VaultSvc", "KeyIso", "NgcSvc", "NgcCtnrSvc", "WinDefend",

                    // Network and Sharing
                    "SharedAccess", "lfsvc", "MapsBroker", "lmhosts", "TrkWks", "NetTcpPortSharing",
                    "p2pimsvc", "p2psvc", "PNRPsvc", "HomeGroupListener", "HomeGroupProvider",
                    "upnphost", "SSDPSRV", "fdPHost", "FDResPub", "WlanSvc", "WwanSvc",

                    // Media and Entertainment
                    "WMPNetworkSvc", "QWAVE", "AudioSrv", "AudioEndpointBuilder", "Audiosrv",
                    "WPDBusEnum", "WpdUsb", "WerSvc", "Wecsvc", "EventSystem",

                    // Print and Fax Services
                    "Fax", "PrintNotify", "PrintWorkflowUserSvc", "Spooler", "PrintSpooler",

                    // Remote and Management Services
                    "RemoteRegistry", "RemoteAccess", "RasMan", "SessionEnv", "TermService",
                    "UmRdpService", "RdpVideoMiniport", "WinRM", "Winmgmt", "WMPNetworkSvc",

                    // System Maintenance
                    "wisvc", "FontCache", "stisvc", "AJRouter", "MSDTC", "WpcMonSvc", "PhoneSvc",
                    "PcaSvc", "SysMain", "Superfetch", "Themes", "TabletInputService", "TouchKeyboard",

                    // Background Services
                    "BackgroundTaskInfrastructureService", "TimeBrokerSvc", "UserManager", "ProfSvc",
                    "Schedule", "TaskScheduler", "SystemEventsBroker", "CoreMessagingRegistrar",

                    // Device Services
                    "DeviceAssociationService", "DeviceInstall", "PlugPlay", "DevQueryBroker",
                    "DmEnrollmentSvc", "DevicesFlowUserSvc", "DevicePickerUserSvc",

                    // Location and Sensors
                    "lfsvc", "SensrSvc", "SensorDataService", "SensorService", "WinHttpAutoProxySvc",

                    // Windows Features
                    "WerSvc", "wercplsupport", "Wecsvc", "EventLog", "EventSystem", "WEvtSvc",
                    "hidserv", "HvHost", "vmickvpexchange", "vmicguestinterface", "vmicshutdown",
                    "vmicheartbeat", "vmicvmsession", "vmicrdv", "vmictimesync", "vmicvss",

                    // Additional Bloatware Services
                    "RetailDemo", "shpamsvc", "smphost", "spectrum", "sppsvc", "SSDPSRV",
                    "SstpSvc", "StiSvc", "swprv", "TapiSrv", "TermService", "TieringEngineService",
                    "TrustedInstaller", "UI0Detect", "UevAgentService", "UmRdpService", "upnphost",
                    "VaultSvc", "vds", "VSS", "W32Time", "WalletService", "WarpJITSvc",
                    "WbioSrvc", "Wcmsvc", "wcncsvc", "WcsPlugInService", "WdNisSvc", "WdiServiceHost",
                    "WdiSystemHost", "WebClient", "Wecsvc", "WEPHOSTSVC", "wercplsupport", "WerSvc",
                    "WiaRpc", "WinDefend", "WinHttpAutoProxySvc", "Winmgmt", "WinRM", "wisvc",
                    "wlidsvc", "wmiApSrv", "WMPNetworkSvc", "workfolderssvc", "WPCSvc", "WPDBusEnum",
                    "wscsvc", "WSearch", "wuauserv", "wudfsvc", "WwanSvc"
                };

                // ========== DISABLE BLOATWARE REGISTRY TWEAKS ==========
                var debloatTweaks = new List<RegistryTweak>
                {
                    // Disable Windows Consumer Features
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableWindowsConsumerFeatures", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableConsumerAccountStateContent", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableCloudOptimizedContent", 1),

                    // Disable Windows Store Auto Updates
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\WindowsStore", "AutoDownload", 2),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\WindowsStore", "DisableStoreApps", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\WindowsStore", "RemoveWindowsStore", 1),

                    // Disable Cortana
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search", "AllowCortana", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search", "DisableWebSearch", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search", "ConnectedSearchUseWeb", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Search", "ConnectedSearchPrivacy", 3),

                    // Disable OneDrive
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\OneDrive", "DisableFileSyncNGSC", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\OneDrive", "DisableFileSync", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\OneDrive", "DisableMeteredNetworkFileSync", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\OneDrive", "DisableLibrariesDefaultSaveToOneDrive", 1),

                    // Disable Xbox Features
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\GameDVR", "AllowGameDVR", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer", "HideSCAMeetNow", 1),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "AppCaptureEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR", "GameDVR_Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\GameBar", "AutoGameModeEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\GameBar", "AllowAutoGameMode", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\GameBar", "UseNexusForGameBarEnabled", 0),

                    // Disable Windows Tips and Suggestions
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableSoftLanding", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableWindowsSpotlightFeatures", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableWindowsSpotlightOnActionCenter", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableWindowsSpotlightOnSettings", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableWindowsSpotlightWindowsWelcomeExperience", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\CloudContent", "DisableThirdPartySuggestions", 1),

                    // Disable Background Apps
                    new("HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\BackgroundAccessApplications", "GlobalUserDisabled", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AppPrivacy", "LetAppsRunInBackground", 2),

                    // Disable Windows Feedback
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Siuf\\Rules", "NumberOfSIUFInPeriod", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Siuf\\Rules", "PeriodInNanoSeconds", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\DataCollection", "DoNotShowFeedbackNotifications", 1),

                    // Disable Activity History
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "EnableActivityFeed", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "PublishUserActivities", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "UploadUserActivities", 0),

                    // Disable Timeline
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\System", "EnableCdp", 0),

                    // Disable Windows Spotlight
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SystemPaneSuggestionsEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SilentInstalledAppsEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "PreInstalledAppsEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "OemPreInstalledAppsEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "ContentDeliveryAllowed", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContentEnabled", 0),

                    // Disable Start Menu Suggestions
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SystemPaneSuggestionsEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-338388Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-338389Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-314559Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-338387Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-338393Enabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "SubscribedContent-353698Enabled", 0),

                    // Disable Lock Screen Spotlight
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "RotatingLockScreenEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\ContentDeliveryManager", "RotatingLockScreenOverlayEnabled", 0),

                    // Disable People Bar
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced\\People", "PeopleBand", 0),

                    // Disable Meet Now
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer", "HideSCAMeetNow", 1),

                    // Disable News and Interests
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Feeds", "EnableFeeds", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Feeds", "ShellFeedsTaskbarViewMode", 2),

                    // Disable Chat Icon
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "TaskbarMn", 0),

                    // Disable Widgets
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Dsh", "AllowNewsAndInterests", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced", "TaskbarDa", 0),

                    // Disable Web Search in Start Menu
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Search", "BingSearchEnabled", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Search", "CortanaConsent", 0),

                    // Disable Automatic Sample Submission
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Spynet", "SubmitSamplesConsent", 2),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender\\Spynet", "SpynetReporting", 0),

                    // Disable Windows Error Reporting
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting", "Disabled", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Windows Error Reporting", "Disabled", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\PCHealth\\ErrorReporting", "DoReport", 0),

                    // Disable Customer Experience Improvement Program
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\SQMClient\\Windows", "CEIPEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\SQMClient\\Windows", "CEIPEnable", 0),

                    // Disable Application Compatibility Telemetry
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\AppCompatFlags\\AIT", "AITEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AppCompat", "AITEnable", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AppCompat", "DisableInventory", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AppCompat", "DisablePCA", 1),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AppCompat", "DisableUAR", 1),

                    // Disable Handwriting Data Sharing
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CPSS\\Store\\InkingAndTypingPersonalization", "Value", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Personalization\\Settings", "AcceptedPrivacyPolicy", 0),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\InputPersonalization", "RestrictImplicitInkCollection", 1),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\InputPersonalization", "RestrictImplicitTextCollection", 1),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\InputPersonalization\\TrainedDataStore", "HarvestContacts", 0),

                    // Disable Speech Services
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Speech_OneCore\\Settings\\OnlineSpeechPrivacy", "HasAccepted", 0),

                    // Disable Advertising ID
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AdvertisingInfo", "Enabled", 0),
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\AdvertisingInfo", "DisabledByGroupPolicy", 1),

                    // Disable App Diagnostics
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Privacy", "TailoredExperiencesWithDiagnosticDataEnabled", 0),

                    // Disable Location Tracking
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\location", "Value", "Deny"),
                    new("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\location", "Value", "Deny"),

                    // Disable Camera Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\webcam", "Value", "Deny"),

                    // Disable Microphone Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\microphone", "Value", "Deny"),

                    // Disable Contacts Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\contacts", "Value", "Deny"),

                    // Disable Calendar Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\appointments", "Value", "Deny"),

                    // Disable Call History Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\phoneCallHistory", "Value", "Deny"),

                    // Disable Email Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\email", "Value", "Deny"),

                    // Disable Messaging Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\chat", "Value", "Deny"),

                    // Disable Radios Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\radios", "Value", "Deny"),

                    // Disable Other Devices Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\bluetoothSync", "Value", "Deny"),

                    // Disable App Notifications
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\userNotificationListener", "Value", "Deny"),

                    // Disable Account Info Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\userAccountInformation", "Value", "Deny"),

                    // Disable Tasks Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\userDataTasks", "Value", "Deny"),

                    // Disable Documents Library Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\documentsLibrary", "Value", "Deny"),

                    // Disable Downloads Folder Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\downloadsFolder", "Value", "Deny"),

                    // Disable Music Library Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\musicLibrary", "Value", "Deny"),

                    // Disable Pictures Library Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\picturesLibrary", "Value", "Deny"),

                    // Disable Videos Library Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\videosLibrary", "Value", "Deny"),

                    // Disable File System Access
                    new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\broadFileSystemAccess", "Value", "Deny")
                };

                int totalTweaks = servicesToDisable.Length + debloatTweaks.Count;
                int currentTweak = 0;

                // Apply service disabling
                foreach (var service in servicesToDisable)
                {
                    OnStatusChanged($"Disabling service: {service}");
                    await DisableService(service);
                    currentTweak++;
                    OnProgressChanged((currentTweak * 100) / totalTweaks);
                    await Task.Delay(50);
                }

                // Apply registry tweaks
                foreach (var tweak in debloatTweaks)
                {
                    OnStatusChanged($"Applying debloat tweak: {tweak.ValueName}");
                    await ApplyRegistryTweak(tweak);
                    currentTweak++;
                    OnProgressChanged((currentTweak * 100) / totalTweaks);
                    await Task.Delay(25);
                }

                result.Success = true;
                result.Message = $"System debloat complete! Disabled {servicesToDisable.Length} services and applied {debloatTweaks.Count} registry tweaks";
                result.EstimatedFpsGain = "Massive performance boost";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error debloating system: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeLatency()
        {
            var result = new OptimizationResult { Category = "Latency Optimization" };

            try
            {
                OnStatusChanged("Optimizing system latency...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable mouse acceleration
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseSpeed", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseThreshold1", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseThreshold2", "0"),

                    // Optimize keyboard response
                    new("HKEY_CURRENT_USER\\Control Panel\\Keyboard", "KeyboardDelay", "0"),
                    new("HKEY_CURRENT_USER\\Control Panel\\Keyboard", "KeyboardSpeed", "31"),

                    // Disable mouse hover time
                    new("HKEY_CURRENT_USER\\Control Panel\\Mouse", "MouseHoverTime", "0"),

                    // Optimize timer resolution
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\kernel", "GlobalTimerResolutionRequests", 1),

                    // Disable HPET (High Precision Event Timer) for gaming
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Enum\\ACPI\\PNP0103\\4&2f7a6a1&0", "ConfigFlags", 1)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                result.Success = true;
                result.Message = "Latency optimizations applied successfully";
                result.EstimatedFpsGain = "Reduced input lag";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error optimizing latency: {ex.Message}";
            }

            return result;
        }

        public async Task<OptimizationResult> OptimizeAdvancedGpu()
        {
            var result = new OptimizationResult { Category = "Advanced GPU Optimization" };

            try
            {
                OnStatusChanged("Applying advanced GPU optimizations...");

                var tweaks = new List<RegistryTweak>
                {
                    // Disable GPU timeout detection and recovery
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrLevel", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers", "TdrDelay", 60),

                    // Disable GPU preemption for maximum performance
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "EnablePreemption", 0),
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers\\Scheduler", "GPUPreemptionLevel", 0),

                    // Force GPU to maximum performance state
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "PowerMizerEnable", 0),

                    // Disable GPU power saving features
                    new("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4d36e968-e325-11ce-bfc1-08002be10318}\\0000", "EnableUlps", 0)
                };

                int progress = 0;
                foreach (var tweak in tweaks)
                {
                    await ApplyRegistryTweak(tweak);
                    progress += 100 / tweaks.Count;
                    OnProgressChanged(progress);
                    await Task.Delay(100);
                }

                result.Success = true;
                result.Message = "Advanced GPU optimizations applied successfully";
                result.EstimatedFpsGain = "Maximum GPU performance";

                _appliedOptimizations.Add(result);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error applying advanced GPU optimizations: {ex.Message}";
            }

            return result;
        }

        protected virtual void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, status);
        }

        protected virtual void OnProgressChanged(int progress)
        {
            ProgressChanged?.Invoke(this, progress);
        }

        public List<OptimizationResult> GetAppliedOptimizations()
        {
            return _appliedOptimizations.ToList();
        }
    }

    public class RegistryTweak
    {
        public string KeyPath { get; set; }
        public string ValueName { get; set; }
        public object Value { get; set; }

        public RegistryTweak(string keyPath, string valueName, object value)
        {
            KeyPath = keyPath;
            ValueName = valueName;
            Value = value;
        }
    }

    public class OptimizationResult
    {
        public string Category { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
        public string EstimatedFpsGain { get; set; }
        public DateTime AppliedAt { get; set; } = DateTime.Now;
    }
}
