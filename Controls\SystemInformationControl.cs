using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Controls
{
    public partial class SystemInformationControl : UserControl
    {
        private readonly PerformanceMonitor _performanceMonitor;

        public SystemInformationControl(PerformanceMonitor performanceMonitor)
        {
            _performanceMonitor = performanceMonitor;
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.BackColor = Color.FromArgb(8, 8, 8);
            this.Size = new Size(1200, 900);
            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            var titleLabel = new Label
            {
                Text = "📋 SYSTEM INFORMATION",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 191, 255),
                AutoSize = true,
                Location = new Point(30, 30)
            };

            var descLabel = new Label
            {
                Text = "DETAILED SYSTEM ANALYSIS & HARDWARE INFORMATION",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.FromArgb(200, 200, 200),
                AutoSize = true,
                Location = new Point(30, 80)
            };

            this.Controls.AddRange(new Control[] { titleLabel, descLabel });
        }
    }
}
