{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SwitchCamera = createLucideIcon(\"SwitchCamera\", [[\"path\", {\n  d: \"M11 19H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h5\",\n  key: \"mtk2lu\"\n}], [\"path\", {\n  d: \"M13 5h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-5\",\n  key: \"120jsl\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"m18 22-3-3 3-3\",\n  key: \"kgdoj7\"\n}], [\"path\", {\n  d: \"m6 2 3 3-3 3\",\n  key: \"1fnbkv\"\n}]]);\nexport { SwitchCamera as default };", "map": {"version": 3, "names": ["SwitchCamera", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\switch-camera.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SwitchCamera\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTlINGEyIDIgMCAwIDEtMi0yVjdhMiAyIDAgMCAxIDItMmg1IiAvPgogIDxwYXRoIGQ9Ik0xMyA1aDdhMiAyIDAgMCAxIDIgMnYxMGEyIDIgMCAwIDEtMiAyaC01IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+CiAgPHBhdGggZD0ibTE4IDIyLTMtMyAzLTMiIC8+CiAgPHBhdGggZD0ibTYgMiAzIDMtMyAzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/switch-camera\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SwitchCamera = createLucideIcon('SwitchCamera', [\n  ['path', { d: 'M11 19H4a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h5', key: 'mtk2lu' }],\n  ['path', { d: 'M13 5h7a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-5', key: '120jsl' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  ['path', { d: 'm18 22-3-3 3-3', key: 'kgdoj7' }],\n  ['path', { d: 'm6 2 3 3-3 3', key: '1fnbkv' }],\n]);\n\nexport default SwitchCamera;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}