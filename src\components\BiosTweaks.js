import React from 'react';
import { Settings2, AlertTriangle } from 'lucide-react';

const BiosTweaks = ({ isAdmin }) => {
  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          background: 'linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%)',
          borderRadius: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff'
        }}>
          <Settings2 size={32} />
        </div>
        <div>
          <h1 style={{ color: '#ff6b35', fontSize: '28px', fontWeight: '700', margin: '0 0 4px 0' }}>
            BIOS/UEFI Tweaks
          </h1>
          <p style={{ color: '#888', fontSize: '14px', margin: '0' }}>
            Hardware-level optimizations for maximum performance
          </p>
        </div>
      </div>
      
      <div style={{ 
        background: 'rgba(26, 26, 26, 0.8)',
        border: '1px solid rgba(255, 107, 53, 0.2)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <div style={{ 
          background: 'rgba(255, 107, 53, 0.1)',
          border: '1px solid rgba(255, 107, 53, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#ff6b35', margin: '0 0 8px 0' }}>⚠️ ADVANCED FEATURE</h3>
          <p style={{ color: '#888', margin: '0', fontSize: '14px' }}>
            BIOS tweaks require extreme caution and can potentially damage your system if applied incorrectly.
          </p>
        </div>
        
        <h2 style={{ color: '#fff', marginBottom: '16px' }}>BIOS Optimization Coming Soon</h2>
        <p style={{ color: '#888', marginBottom: '20px' }}>
          Hardware-level tweaks, memory timings, and UEFI optimizations will be available here.
        </p>
      </div>

      {!isAdmin && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          background: 'rgba(255, 165, 2, 0.1)',
          border: '1px solid rgba(255, 165, 2, 0.3)',
          borderRadius: '8px',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: '#ffa502'
        }}>
          <AlertTriangle size={20} />
          <span>Administrator privileges required</span>
        </div>
      )}
    </div>
  );
};

export default BiosTweaks;
