const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // System information
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  
  // PowerShell execution
  executePowerShell: (script) => ipcRenderer.invoke('execute-powershell', script),
  
  // Admin check
  checkAdmin: () => ipcRenderer.invoke('check-admin'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // Dialog
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Registry operations
  readRegistry: (key, value) => ipcRenderer.invoke('read-registry', key, value),
  writeRegistry: (key, value, data, type) => ipcRenderer.invoke('write-registry', key, value, data, type),
  
  // Service management
  getServices: () => ipcRenderer.invoke('get-services'),
  setServiceStartup: (serviceName, startupType) => ipcRenderer.invoke('set-service-startup', serviceName, startupType),
  
  // Process management
  getProcesses: () => ipcRenderer.invoke('get-processes'),
  killProcess: (processName) => ipcRenderer.invoke('kill-process', processName),
  
  // File operations
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  fileExists: (filePath) => ipcRenderer.invoke('file-exists', filePath),
  
  // Backup operations
  createBackup: (backupName) => ipcRenderer.invoke('create-backup', backupName),
  restoreBackup: (backupName) => ipcRenderer.invoke('restore-backup', backupName),
  listBackups: () => ipcRenderer.invoke('list-backups'),
  
  // Performance monitoring
  getPerformanceMetrics: () => ipcRenderer.invoke('get-performance-metrics'),
  
  // BIOS/UEFI operations
  getBiosInfo: () => ipcRenderer.invoke('get-bios-info'),
  
  // Network optimization
  optimizeNetwork: () => ipcRenderer.invoke('optimize-network'),
  
  // GPU optimization
  optimizeGPU: () => ipcRenderer.invoke('optimize-gpu'),
  
  // CPU optimization
  optimizeCPU: () => ipcRenderer.invoke('optimize-cpu'),
  
  // Memory optimization
  optimizeMemory: () => ipcRenderer.invoke('optimize-memory'),
  
  // Storage optimization
  optimizeStorage: () => ipcRenderer.invoke('optimize-storage'),
  
  // Gaming optimizations
  applyGamingTweaks: () => ipcRenderer.invoke('apply-gaming-tweaks'),
  
  // Debloat operations
  scanBloatware: () => ipcRenderer.invoke('scan-bloatware'),
  removeBloatware: (items) => ipcRenderer.invoke('remove-bloatware', items),
  
  // Advanced tweaks
  applyAdvancedTweaks: (tweaks) => ipcRenderer.invoke('apply-advanced-tweaks', tweaks),
  
  // System cleanup
  cleanSystem: () => ipcRenderer.invoke('clean-system'),
  
  // Startup optimization
  optimizeStartup: () => ipcRenderer.invoke('optimize-startup'),
  
  // Visual effects
  optimizeVisualEffects: () => ipcRenderer.invoke('optimize-visual-effects')
});
