{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Milestone = createLucideIcon(\"Milestone\", [[\"path\", {\n  d: \"M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z\",\n  key: \"1mp5s7\"\n}], [\"path\", {\n  d: \"M12 13v8\",\n  key: \"1l5pq0\"\n}], [\"path\", {\n  d: \"M12 3v3\",\n  key: \"1n5kay\"\n}]]);\nexport { Milestone as default };", "map": {"version": 3, "names": ["Milestone", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\milestone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Milestone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNkg1YTIgMiAwIDAgMC0yIDJ2M2EyIDIgMCAwIDAgMiAyaDEzbDQtMy41TDE4IDZaIiAvPgogIDxwYXRoIGQ9Ik0xMiAxM3Y4IiAvPgogIDxwYXRoIGQ9Ik0xMiAzdjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/milestone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Milestone = createLucideIcon('Milestone', [\n  ['path', { d: 'M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6Z', key: '1mp5s7' }],\n  ['path', { d: 'M12 13v8', key: '1l5pq0' }],\n  ['path', { d: 'M12 3v3', key: '1n5kay' }],\n]);\n\nexport default Milestone;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,sDAAwD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrF,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}