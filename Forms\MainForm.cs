using System;
using System.Drawing;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;
        private Label _titleLabel;
        private Label _statusLabel;
        private ProgressBar _progressBar;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();
            LoadCpuOptimization(); // Load CPU optimization by default

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // MainForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 650);
            this.MinimumSize = new Size(900, 600);
            this.Name = "MainForm";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = Color.White;
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Clean professional theme
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ForeColor = Color.FromArgb(33, 33, 33);
        }

        private void CreateLayout()
        {
            // Create main panels - SIMPLE & CLEAN
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 200,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(0, 0, 1, 0)
            };

            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Simple header
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.White
            };

            _titleLabel = new Label
            {
                Text = "Rodey Premium Tweaker",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            headerPanel.Controls.Add(_titleLabel);

            // Simple status bar
            var statusPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 25,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            _statusLabel = new Label
            {
                Text = "Ready",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(108, 117, 125),
                AutoSize = true,
                Location = new Point(10, 5)
            };

            _progressBar = new ProgressBar
            {
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                Location = new Point(this.Width - 220, 5),
                Size = new Size(200, 20),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            statusPanel.Controls.AddRange(new Control[] { _statusLabel, _progressBar });

            // Create sidebar menu
            CreateSidebarMenu();

            // Add all panels to form
            this.Controls.AddRange(new Control[] { _contentPanel, _sidebarPanel, headerPanel, statusPanel });
        }

        private void CreateSidebarMenu()
        {
            var menuItems = new[]
            {
                "CPU Optimization",
                "GPU Optimization",
                "Memory Optimization",
                "Network Optimization",
                "System Cleanup",
                "Registry Tweaks",
                "Performance Monitor",
                "Settings"
            };

            int yPos = 20;

            foreach (var item in menuItems)
            {
                var menuButton = new Button
                {
                    Text = item,
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(73, 80, 87),
                    BackColor = Color.Transparent,
                    FlatStyle = FlatStyle.Flat,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Size = new Size(180, 35),
                    Location = new Point(10, yPos),
                    Tag = item,
                    Cursor = Cursors.Hand
                };

                menuButton.FlatAppearance.BorderSize = 0;
                menuButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(233, 236, 239);
                menuButton.Click += MenuButton_Click;

                _sidebarPanel.Controls.Add(menuButton);
                yPos += 40;
            }
        }

        private void MenuButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            var menuText = button?.Tag?.ToString();

            // Clear content panel
            _contentPanel.Controls.Clear();

            // Load appropriate content based on menu selection
            switch (menuText)
            {
                case "CPU Optimization":
                    LoadCpuOptimization();
                    break;
                case "GPU Optimization":
                    LoadGpuOptimization();
                    break;
                case "Memory Optimization":
                    LoadMemoryOptimization();
                    break;
                case "Network Optimization":
                    LoadNetworkOptimization();
                    break;
                case "System Cleanup":
                    LoadSystemCleanup();
                    break;
                case "Registry Tweaks":
                    LoadRegistryTweaks();
                    break;
                case "Performance Monitor":
                    LoadPerformanceMonitor();
                    break;
                case "Settings":
                    LoadSettings();
                    break;
            }

            UpdateStatus($"Loaded {menuText}");
        }



        private void LoadCpuOptimization()
        {
            var cpuControl = new CpuOptimizationControl(_optimizer);
            cpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cpuControl);
        }

        private void LoadGpuOptimization()
        {
            var gpuControl = new GpuOptimizationControl(_optimizer);
            gpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(gpuControl);
        }

        private void LoadMemoryOptimization()
        {
            var memoryControl = new MemoryOptimizationControl(_optimizer);
            memoryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(memoryControl);
        }

        private void LoadStorageOptimization()
        {
            var storageControl = new StorageOptimizationControl(_optimizer);
            storageControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(storageControl);
        }

        private void LoadNetworkOptimization()
        {
            var networkControl = new NetworkOptimizationControl(_optimizer);
            networkControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(networkControl);
        }

        private void LoadSystemCleanup()
        {
            var cleanupControl = new SystemDebloaterControl(_optimizer);
            cleanupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cleanupControl);
        }

        private void LoadSystemDebloater()
        {
            var debloaterControl = new SystemDebloaterControl(_optimizer);
            debloaterControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(debloaterControl);
        }

        private void LoadRegistryTweaks()
        {
            var registryControl = new RegistryTweaksControl(_optimizer);
            registryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(registryControl);
        }

        private void LoadServicesOptimizer()
        {
            var servicesControl = new ServicesOptimizerControl(_optimizer);
            servicesControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(servicesControl);
        }

        private void LoadStartupOptimizer()
        {
            var startupControl = new StartupOptimizerControl(_optimizer);
            startupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(startupControl);
        }

        private void LoadPowerManagement()
        {
            var powerControl = new PowerManagementControl(_optimizer);
            powerControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(powerControl);
        }

        private void LoadSecurityTweaks()
        {
            var securityControl = new SecurityTweaksControl(_optimizer);
            securityControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(securityControl);
        }

        private void LoadBiosGuide()
        {
            var biosControl = new BiosOptimizationControl(_optimizer);
            biosControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(biosControl);
        }

        private void LoadSystemInformation()
        {
            var systemInfoControl = new SystemInformationControl(_performanceMonitor);
            systemInfoControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(systemInfoControl);
        }

        private void LoadBackupRestore()
        {
            var backupControl = new BackupRestoreControl();
            backupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(backupControl);
        }

        private void LoadPerformanceMonitor()
        {
            var monitorControl = new PerformanceMonitorControl(_performanceMonitor);
            monitorControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(monitorControl);
        }

        private void LoadSettings()
        {
            var settingsControl = new SettingsControl();
            settingsControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(settingsControl);
        }

        private void UpdateStatus(string message)
        {
            _statusLabel.Text = message;
            _statusLabel.Refresh();
        }

        public void ShowProgress(bool show, string message = "")
        {
            _progressBar.Visible = show;
            if (show && !string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
