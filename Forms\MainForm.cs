using System;
using System.Drawing;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;
        private Label _titleLabel;
        private Label _statusLabel;
        private ProgressBar _progressBar;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();
            LoadDashboard();

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // MainForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainForm";
            this.Text = "Rodey Premium Tweaker v1.0 - Professional Windows Optimization";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Dark theme colors
            this.BackColor = Color.FromArgb(10, 10, 10);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create main panels
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280,
                BackColor = Color.FromArgb(26, 26, 26),
                Padding = new Padding(0, 0, 1, 0)
            };

            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(15, 15, 15),
                Padding = new Padding(20)
            };

            // Create title bar
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(26, 26, 26)
            };

            _titleLabel = new Label
            {
                Text = "RODEY PREMIUM TWEAKER",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(20, 15)
            };

            var versionLabel = new Label
            {
                Text = "v1.0 Professional Edition",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 40)
            };

            titlePanel.Controls.AddRange(new Control[] { _titleLabel, versionLabel });

            // Create status bar
            var statusPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 30,
                BackColor = Color.FromArgb(26, 26, 26)
            };

            _statusLabel = new Label
            {
                Text = "Ready - Administrator Mode Active",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(0, 255, 136),
                AutoSize = true,
                Location = new Point(10, 6)
            };

            _progressBar = new ProgressBar
            {
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                Location = new Point(this.Width - 220, 5),
                Size = new Size(200, 20),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            statusPanel.Controls.AddRange(new Control[] { _statusLabel, _progressBar });

            // Create sidebar menu
            CreateSidebarMenu();

            // Add all panels to form
            this.Controls.AddRange(new Control[] { _contentPanel, _sidebarPanel, titlePanel, statusPanel });
        }

        private void CreateSidebarMenu()
        {
            var menuItems = new[]
            {
                new { Text = "Dashboard", Icon = "🏠", Category = "GENERAL" },
                new { Text = "Quick Optimizer", Icon = "⚡", Category = "GENERAL" },
                new { Text = "CPU Optimization", Icon = "🔧", Category = "HARDWARE" },
                new { Text = "GPU Optimization", Icon = "🎮", Category = "HARDWARE" },
                new { Text = "Memory Tweaks", Icon = "💾", Category = "HARDWARE" },
                new { Text = "Network Optimization", Icon = "🌐", Category = "SYSTEM" },
                new { Text = "System Debloater", Icon = "🗑️", Category = "SYSTEM" },
                new { Text = "Registry Tweaks", Icon = "⚙️", Category = "ADVANCED" },
                new { Text = "BIOS Optimization", Icon = "🔥", Category = "ADVANCED" },
                new { Text = "Performance Monitor", Icon = "📊", Category = "MONITORING" },
                new { Text = "Settings", Icon = "⚙️", Category = "TOOLS" }
            };

            int yPos = 20;
            string currentCategory = "";

            foreach (var item in menuItems)
            {
                // Add category header if new category
                if (item.Category != currentCategory)
                {
                    if (currentCategory != "") yPos += 10; // Add spacing between categories

                    var categoryLabel = new Label
                    {
                        Text = item.Category,
                        Font = new Font("Segoe UI", 8, FontStyle.Bold),
                        ForeColor = Color.Gray,
                        AutoSize = true,
                        Location = new Point(20, yPos)
                    };
                    _sidebarPanel.Controls.Add(categoryLabel);
                    yPos += 25;
                    currentCategory = item.Category;
                }

                // Create menu button
                var menuButton = new Button
                {
                    Text = $"{item.Icon} {item.Text}",
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.White,
                    BackColor = Color.Transparent,
                    FlatStyle = FlatStyle.Flat,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Size = new Size(240, 40),
                    Location = new Point(20, yPos),
                    Tag = item.Text
                };

                menuButton.FlatAppearance.BorderSize = 0;
                menuButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(0, 255, 136, 30);
                menuButton.Click += MenuButton_Click;

                _sidebarPanel.Controls.Add(menuButton);
                yPos += 45;
            }
        }

        private void MenuButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            var menuText = button?.Tag?.ToString();

            // Clear content panel
            _contentPanel.Controls.Clear();

            // Load appropriate content based on menu selection
            switch (menuText)
            {
                case "Dashboard":
                    LoadDashboard();
                    break;
                case "Quick Optimizer":
                    LoadQuickOptimizer();
                    break;
                case "CPU Optimization":
                    LoadCpuOptimization();
                    break;
                case "GPU Optimization":
                    LoadGpuOptimization();
                    break;
                case "Memory Tweaks":
                    LoadMemoryTweaks();
                    break;
                case "Network Optimization":
                    LoadNetworkOptimization();
                    break;
                case "System Debloater":
                    LoadSystemDebloater();
                    break;
                case "Registry Tweaks":
                    LoadRegistryTweaks();
                    break;
                case "BIOS Optimization":
                    LoadBiosOptimization();
                    break;
                case "Performance Monitor":
                    LoadPerformanceMonitor();
                    break;
                case "Settings":
                    LoadSettings();
                    break;
            }

            UpdateStatus($"Loaded {menuText}");
        }

        private void LoadDashboard()
        {
            var dashboardControl = new DashboardControl(_performanceMonitor);
            dashboardControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(dashboardControl);
        }

        private void LoadQuickOptimizer()
        {
            var optimizerControl = new QuickOptimizerControl(_optimizer);
            optimizerControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(optimizerControl);
        }

        private void LoadCpuOptimization()
        {
            var cpuControl = new CpuOptimizationControl(_optimizer);
            cpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cpuControl);
        }

        private void LoadGpuOptimization()
        {
            var gpuControl = new GpuOptimizationControl(_optimizer);
            gpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(gpuControl);
        }

        private void LoadMemoryTweaks()
        {
            var memoryControl = new MemoryOptimizationControl(_optimizer);
            memoryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(memoryControl);
        }

        private void LoadNetworkOptimization()
        {
            var networkControl = new NetworkOptimizationControl(_optimizer);
            networkControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(networkControl);
        }

        private void LoadSystemDebloater()
        {
            var debloaterControl = new SystemDebloaterControl(_optimizer);
            debloaterControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(debloaterControl);
        }

        private void LoadRegistryTweaks()
        {
            var registryControl = new RegistryTweaksControl(_optimizer);
            registryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(registryControl);
        }

        private void LoadBiosOptimization()
        {
            var biosControl = new BiosOptimizationControl(_optimizer);
            biosControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(biosControl);
        }

        private void LoadPerformanceMonitor()
        {
            var monitorControl = new PerformanceMonitorControl(_performanceMonitor);
            monitorControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(monitorControl);
        }

        private void LoadSettings()
        {
            var settingsControl = new SettingsControl();
            settingsControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(settingsControl);
        }

        private void UpdateStatus(string message)
        {
            _statusLabel.Text = message;
            _statusLabel.Refresh();
        }

        public void ShowProgress(bool show, string message = "")
        {
            _progressBar.Visible = show;
            if (show && !string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
