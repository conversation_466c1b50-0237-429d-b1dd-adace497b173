using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Enable modern .NET 8 features
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.SupportsTransparentBackColor, true);

            // MainForm with modern settings
            this.AutoScaleDimensions = new SizeF(96F, 96F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainForm";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(24, 24, 27);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Ultra-professional dark theme with gradient
            this.BackColor = Color.FromArgb(16, 18, 22);
            this.ForeColor = Color.White;

            // Add dynamic animated background
            this.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // Dynamic gradient background with RODEY colors
                using (var bgBrush = new System.Drawing.Drawing2D.LinearGradientBrush(
                    new Rectangle(0, 0, this.Width, this.Height),
                    Color.FromArgb(10, 5, 15),
                    Color.FromArgb(25, 15, 35),
                    System.Drawing.Drawing2D.LinearGradientMode.ForwardDiagonal))
                {
                    g.FillRectangle(bgBrush, 0, 0, this.Width, this.Height);
                }

                // Dynamic particle system
                var random = new Random(DateTime.Now.Millisecond);
                for (int i = 0; i < 50; i++)
                {
                    var x = (random.Next(this.Width) + DateTime.Now.Millisecond / 10) % this.Width;
                    var y = (random.Next(this.Height) + DateTime.Now.Millisecond / 15) % this.Height;
                    var size = random.Next(1, 4);
                    var alpha = random.Next(10, 40);

                    using (var particleBrush = new SolidBrush(Color.FromArgb(alpha, 255, 20, 147)))
                    {
                        g.FillEllipse(particleBrush, x, y, size, size);
                    }
                }

                // Electric grid pattern
                using (var gridPen = new Pen(Color.FromArgb(8, 0, 255, 255), 1))
                {
                    for (int x = 0; x < this.Width; x += 100)
                    {
                        g.DrawLine(gridPen, x, 0, x, this.Height);
                    }
                    for (int y = 0; y < this.Height; y += 100)
                    {
                        g.DrawLine(gridPen, 0, y, this.Width, y);
                    }
                }
            };
        }

        private void CreateLayout()
        {
            // Create custom title bar
            var titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.FromArgb(24, 24, 24)
            };

            var titleLabel = new Label
            {
                Text = "Rodey Premium Tweaker",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(15, 10),
                AutoSize = true
            };

            var closeButton = new Button
            {
                Text = "×",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 50, 5)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();

            titleBar.Controls.Add(titleLabel);
            titleBar.Controls.Add(closeButton);
            this.Controls.Add(titleBar);

            // Create sidebar for system info with glass effect
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 320,
                BackColor = Color.FromArgb(20, 22, 26),
                Padding = new Padding(25)
            };

            // Add glass effect to sidebar
            _sidebarPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // Glass effect background
                using (var glassBrush = new System.Drawing.Drawing2D.LinearGradientBrush(
                    new Rectangle(0, 0, _sidebarPanel.Width, _sidebarPanel.Height),
                    Color.FromArgb(30, 255, 255, 255),
                    Color.FromArgb(5, 255, 255, 255),
                    System.Drawing.Drawing2D.LinearGradientMode.Vertical))
                {
                    g.FillRectangle(glassBrush, 0, 0, _sidebarPanel.Width, _sidebarPanel.Height);
                }

                // Right border
                using (var borderPen = new Pen(Color.FromArgb(40, 255, 255, 255), 1))
                {
                    g.DrawLine(borderPen, _sidebarPanel.Width - 1, 0, _sidebarPanel.Width - 1, _sidebarPanel.Height);
                }
            };

            // Main content area with transparent background
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(40)
            };

            CreateSidebarContent();
            CreateMainContent();

            this.Controls.Add(_contentPanel);
            this.Controls.Add(_sidebarPanel);
        }

        private void CreateSidebarContent()
        {
            // System Information Section
            var systemInfoLabel = new Label
            {
                Text = "System Information",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            // System info icon
            var systemIcon = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(10, 60),
                BackColor = Color.FromArgb(0, 120, 215),
                BorderStyle = BorderStyle.None
            };
            systemIcon.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (var brush = new SolidBrush(Color.White))
                using (var font = new Font("Segoe UI", 24, FontStyle.Bold))
                {
                    var text = "R";
                    var size = g.MeasureString(text, font);
                    g.DrawString(text, font, brush,
                        (systemIcon.Width - size.Width) / 2,
                        (systemIcon.Height - size.Height) / 2);
                }
            };

            // System details
            var cpuLabel = new Label
            {
                Text = "CPU\nIntel Core i7-12700K",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 160),
                Size = new Size(240, 35)
            };

            var gpuLabel = new Label
            {
                Text = "GPU\nNVIDIA RTX 4080",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 200),
                Size = new Size(240, 35)
            };

            var ramLabel = new Label
            {
                Text = "RAM\n32 GB DDR4",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 240),
                Size = new Size(240, 35)
            };

            var storageLabel = new Label
            {
                Text = "Storage\n1TB NVMe SSD",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 280),
                Size = new Size(240, 35)
            };

            // Performance score
            var perfLabel = new Label
            {
                Text = "Performance Score",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 340),
                AutoSize = true
            };

            var scoreLabel = new Label
            {
                Text = "95/100",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 200, 100),
                Location = new Point(0, 365),
                AutoSize = true
            };

            _sidebarPanel.Controls.AddRange(new Control[]
            {
                systemInfoLabel, systemIcon, cpuLabel, gpuLabel,
                ramLabel, storageLabel, perfLabel, scoreLabel
            });
        }

        private void CreateMainContent()
        {
            // Professional main title with gradient effect
            var titlePanel = new Panel
            {
                Size = new Size(800, 80),
                Location = new Point(20, 20),
                BackColor = Color.Transparent
            };

            titlePanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

                // RODEY BRANDING with dynamic effects
                var brandText = "RODEY";
                var titleText = "PREMIUM TWEAKS";

                // RODEY brand name with fire gradient
                using (var brandFont = new Font("Segoe UI", 36, FontStyle.Bold))
                {
                    var brandSize = g.MeasureString(brandText, brandFont);
                    var brandRect = new Rectangle(0, 5, (int)brandSize.Width, (int)brandSize.Height);

                    // Brand shadow with glow
                    using (var glowBrush = new SolidBrush(Color.FromArgb(80, 255, 20, 147)))
                    {
                        for (int i = 1; i <= 3; i++)
                        {
                            g.DrawString(brandText, brandFont, glowBrush, i, 5 + i);
                        }
                    }

                    // Main brand with fire gradient
                    using (var brandBrush = new System.Drawing.Drawing2D.LinearGradientBrush(
                        brandRect,
                        Color.FromArgb(255, 20, 147),
                        Color.FromArgb(255, 69, 0),
                        System.Drawing.Drawing2D.LinearGradientMode.Horizontal))
                    {
                        g.DrawString(brandText, brandFont, brandBrush, 0, 5);
                    }
                }

                // PREMIUM TWEAKS subtitle
                using (var titleFont = new Font("Segoe UI", 24, FontStyle.Bold))
                {
                    var titleSize = g.MeasureString(titleText, titleFont);
                    var titleRect = new Rectangle(200, 15, (int)titleSize.Width, (int)titleSize.Height);

                    // Title with electric gradient
                    using (var titleBrush = new System.Drawing.Drawing2D.LinearGradientBrush(
                        titleRect,
                        Color.FromArgb(0, 255, 255),
                        Color.FromArgb(255, 255, 255),
                        System.Drawing.Drawing2D.LinearGradientMode.Vertical))
                    {
                        g.DrawString(titleText, titleFont, titleBrush, 200, 15);
                    }
                }

                // Dynamic tagline
                var taglineText = "🔥 ULTIMATE PERFORMANCE UNLEASHED 🔥";
                using (var taglineFont = new Font("Segoe UI", 12, FontStyle.Bold))
                using (var taglineBrush = new SolidBrush(Color.FromArgb(255, 215, 0)))
                {
                    g.DrawString(taglineText, taglineFont, taglineBrush, 0, 55);
                }
            };

            _contentPanel.Controls.Add(titlePanel);

            // Create optimization cards grid
            CreateOptimizationCards();
        }

        private void CreateOptimizationCards()
        {
            var cards = new[]
            {
                new { Title = "GAMING BEAST", Icon = "🎯", Description = "Ultimate gaming mode", Color = Color.FromArgb(255, 0, 255), Tweaks = "148", Category = "GAMING" },
                new { Title = "CPU OPTIMIZER", Icon = "⚡", Description = "CPU performance boost", Color = Color.FromArgb(255, 69, 0), Tweaks = "4", Category = "CORE" },
                new { Title = "GPU OPTIMIZER", Icon = "🎮", Description = "Graphics optimization", Color = Color.FromArgb(138, 43, 226), Tweaks = "4", Category = "CORE" },
                new { Title = "MEMORY BOOST", Icon = "🧠", Description = "RAM optimization", Color = Color.FromArgb(0, 191, 255), Tweaks = "4", Category = "CORE" },
                new { Title = "NETWORK BOOST", Icon = "🌐", Description = "Network optimization", Color = Color.FromArgb(30, 144, 255), Tweaks = "6", Category = "CORE" },
                new { Title = "STORAGE BOOST", Icon = "💾", Description = "Storage optimization", Color = Color.FromArgb(50, 205, 50), Tweaks = "8", Category = "CORE" },
                new { Title = "POWER BOOST", Icon = "⚡", Description = "Power optimization", Color = Color.FromArgb(255, 215, 0), Tweaks = "6", Category = "CORE" },
                new { Title = "REGISTRY CLEAN", Icon = "⚙️", Description = "Registry optimization", Color = Color.FromArgb(220, 20, 60), Tweaks = "12", Category = "ADVANCED" },
                new { Title = "PRIVACY BOOST", Icon = "🔒", Description = "Privacy optimization", Color = Color.FromArgb(75, 0, 130), Tweaks = "10", Category = "SECURITY" },
                new { Title = "SYSTEM DEBLOAT", Icon = "🧹", Description = "Remove bloatware", Color = Color.FromArgb(34, 139, 34), Tweaks = "15", Category = "CLEANUP" },
                new { Title = "LATENCY BOOST", Icon = "⚡", Description = "Reduce input lag", Color = Color.FromArgb(255, 140, 0), Tweaks = "8", Category = "GAMING" },
                new { Title = "ADVANCED GPU", Icon = "🔥", Description = "Advanced GPU tweaks", Color = Color.FromArgb(199, 21, 133), Tweaks = "6", Category = "EXTREME" }
            };

            int cardWidth = 220;
            int cardHeight = 160;
            int margin = 25;
            int cardsPerRow = 6;
            int startX = 10;
            int startY = 100;

            for (int i = 0; i < cards.Length; i++)
            {
                int row = i / cardsPerRow;
                int col = i % cardsPerRow;

                // Capture the current card data to avoid closure issues
                var currentCard = cards[i];

                var modernCard = new ModernCard
                {
                    Title = currentCard.Title,
                    Icon = currentCard.Icon,
                    Description = currentCard.Description,
                    AccentColor = currentCard.Color,
                    TweakCount = currentCard.Tweaks,
                    Category = currentCard.Category,
                    Location = new Point(startX + col * (cardWidth + margin), startY + row * (cardHeight + margin)),
                    Size = new Size(cardWidth, cardHeight)
                };

                // Fix closure issue by capturing the title in a local variable
                var cardTitle = currentCard.Title;
                modernCard.Click += (s, e) => HandleCardClick(cardTitle);
                _contentPanel.Controls.Add(modernCard);
            }
        }



        private async void HandleCardClick(string cardTitle)
        {
            // Show optimization in progress
            ShowOptimizationProgress(cardTitle);

            try
            {
                switch (cardTitle)
                {
                    case "GAMING BEAST":
                        await ApplyGamingOptimizations();
                        break;
                    case "CPU OPTIMIZER":
                        await ApplyCpuOptimizations();
                        break;
                    case "GPU OPTIMIZER":
                        await ApplyGpuOptimizations();
                        break;
                    case "MEMORY BOOST":
                        await ApplyMemoryOptimizations();
                        break;
                    case "STORAGE BOOST":
                        await ApplyStorageOptimizations();
                        break;
                    case "NETWORK BOOST":
                        await ApplyNetworkOptimizations();
                        break;
                    case "POWER BOOST":
                        await ApplyPowerOptimizations();
                        break;
                    case "REGISTRY CLEAN":
                        await ApplyRegistryOptimizations();
                        break;
                    case "PRIVACY BOOST":
                        await ApplyPrivacyOptimizations();
                        break;
                    case "SYSTEM DEBLOAT":
                        await ApplyDebloatOptimizations();
                        break;
                    case "LATENCY BOOST":
                        await ApplyLatencyOptimizations();
                        break;
                    case "ADVANCED GPU":
                        await ApplyAdvancedGpuOptimizations();
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowOptimizationError(cardTitle, ex.Message);
            }
        }

        private void ShowOptimizationProgress(string optimizationType)
        {
            _contentPanel.Controls.Clear();

            // Use modern progress panel
            var progressPanel = new ModernProgressPanel
            {
                Title = $"🚀 {optimizationType.ToUpper()} OPTIMIZATION",
                Status = "⚡ Initializing optimization engine...",
                Progress = 0,
                Location = new Point(50, 50),
                Size = new Size(1200, 200)
            };

            // Modern results console
            var consolePanel = new Panel
            {
                Size = new Size(1200, 350),
                Location = new Point(50, 270),
                BackColor = Color.FromArgb(20, 20, 23)
            };

            consolePanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Rounded corners
                var rect = new Rectangle(0, 0, consolePanel.Width - 1, consolePanel.Height - 1);
                using (var path = CreateRoundedRectanglePath(rect, 12))
                {
                    using (var brush = new SolidBrush(Color.FromArgb(20, 20, 23)))
                    {
                        g.FillPath(brush, path);
                    }

                    using (var pen = new Pen(Color.FromArgb(0, 120, 215), 2))
                    {
                        g.DrawPath(pen, path);
                    }
                }
            };

            var consoleHeader = new Label
            {
                Text = "📊 OPTIMIZATION CONSOLE",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 120, 215),
                BackColor = Color.Transparent,
                Location = new Point(20, 15),
                AutoSize = true
            };

            var resultsTextBox = new TextBox
            {
                Location = new Point(20, 45),
                Size = new Size(1160, 285),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.FromArgb(15, 15, 18),
                ForeColor = Color.FromArgb(0, 255, 127),
                BorderStyle = BorderStyle.None,
                Font = new Font("Consolas", 10, FontStyle.Regular),
                Text = "System optimization console initialized...\nReady to apply performance tweaks...\n"
            };

            consolePanel.Controls.AddRange(new Control[] { consoleHeader, resultsTextBox });

            // Modern back button with rounded corners
            var backButton = new Button
            {
                Text = "← BACK TO MAIN",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(0, 120, 215),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(180, 45),
                Location = new Point(50, 650)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { progressPanel, consolePanel, backButton });

            // Store references for updating during optimization
            _contentPanel.Tag = new { ProgressPanel = progressPanel, ResultsTextBox = resultsTextBox };
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        private async Task ApplyAllOptimizations()
        {
            UpdateOptimizationStatus("Applying all optimizations...");
            var result = await _optimizer.ApplyGamingOptimizations();
            ShowOptimizationComplete($"All optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyCpuOptimizations()
        {
            UpdateOptimizationStatus("Optimizing CPU performance...");
            var result = await _optimizer.OptimizeCpu();
            ShowOptimizationComplete($"CPU optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyGpuOptimizations()
        {
            UpdateOptimizationStatus("Optimizing GPU performance...");
            var result = await _optimizer.OptimizeGpu();
            ShowOptimizationComplete($"GPU optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyMemoryOptimizations()
        {
            UpdateOptimizationStatus("Optimizing memory performance...");
            var result = await _optimizer.OptimizeMemory();
            ShowOptimizationComplete($"Memory optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyStorageOptimizations()
        {
            UpdateOptimizationStatus("Optimizing storage performance...");
            var result = await _optimizer.OptimizeStorage();
            ShowOptimizationComplete($"Storage optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyNetworkOptimizations()
        {
            UpdateOptimizationStatus("Optimizing network performance...");
            var result = await _optimizer.OptimizeNetwork();
            ShowOptimizationComplete($"Network optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyPowerOptimizations()
        {
            UpdateOptimizationStatus("Optimizing power settings...");
            var result = await _optimizer.OptimizePower();
            ShowOptimizationComplete($"Power optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyRegistryOptimizations()
        {
            UpdateOptimizationStatus("Applying registry tweaks...");
            var result = await _optimizer.OptimizeRegistry();
            ShowOptimizationComplete($"Registry optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyPrivacyOptimizations()
        {
            UpdateOptimizationStatus("Applying privacy settings...");
            var result = await _optimizer.OptimizePrivacy();
            ShowOptimizationComplete($"Privacy optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyDebloatOptimizations()
        {
            UpdateOptimizationStatus("Removing bloatware...");
            var result = await _optimizer.OptimizeDebloat();
            ShowOptimizationComplete($"System debloat completed successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyLatencyOptimizations()
        {
            UpdateOptimizationStatus("Reducing system latency...");
            var result = await _optimizer.OptimizeLatency();
            ShowOptimizationComplete($"Latency optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyAdvancedGpuOptimizations()
        {
            UpdateOptimizationStatus("Applying advanced GPU tweaks...");
            var result = await _optimizer.OptimizeAdvancedGpu();
            ShowOptimizationComplete($"Advanced GPU optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyGamingOptimizations()
        {
            UpdateOptimizationStatus("Optimizing for gaming performance...");
            var result = await _optimizer.ApplyGamingOptimizations();
            ShowOptimizationComplete($"Gaming optimizations applied successfully! {result.EstimatedFpsGain}");
        }

        private async Task ApplyBiosOptimizations()
        {
            UpdateOptimizationStatus("Applying BIOS-level optimizations...");
            // BIOS optimizations are typically done through BIOS interface, not software
            await Task.Delay(2000); // Simulate processing
            ShowOptimizationComplete("BIOS optimization guide displayed successfully!");
        }

        private void ShowBiosGuide()
        {
            _contentPanel.Controls.Clear();

            var titleLabel = new Label
            {
                Text = "BIOS Optimization Guide",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var guideText = new TextBox
            {
                Location = new Point(0, 70),
                Size = new Size(800, 450),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.FromArgb(24, 24, 24),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 10),
                Text = "BIOS Optimization Guide:\n\n" +
                       "1. Enable XMP/DOCP for RAM\n" +
                       "2. Disable C-States for maximum performance\n" +
                       "3. Set CPU to Performance mode\n" +
                       "4. Disable Intel SpeedStep/AMD Cool'n'Quiet\n" +
                       "5. Enable High Performance Event Timer (HPET)\n" +
                       "6. Disable unnecessary USB ports\n" +
                       "7. Set PCIe to Gen 4 (if supported)\n" +
                       "8. Disable Secure Boot for compatibility\n" +
                       "9. Enable Resizable BAR for modern GPUs\n" +
                       "10. Optimize memory timings manually"
            };

            var backButton = new Button
            {
                Text = "← Back to Main",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(120, 30),
                Location = new Point(0, 540)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, guideText, backButton });
        }

        private void UpdateOptimizationStatus(string status)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.ProgressPanel != null)
                {
                    var progressPanel = controls.ProgressPanel as ModernProgressPanel;
                    if (progressPanel != null)
                    {
                        progressPanel.Status = status;
                        progressPanel.Progress = Math.Min(progressPanel.Progress + 10, 90);
                        progressPanel.Invalidate();
                    }
                }
            }
        }

        private void ShowOptimizationComplete(string message)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.ProgressPanel != null && controls.ResultsTextBox != null)
                {
                    var progressPanel = controls.ProgressPanel as ModernProgressPanel;
                    if (progressPanel != null)
                    {
                        progressPanel.Status = "✅ OPTIMIZATION COMPLETE!";
                        progressPanel.Progress = 100;
                        progressPanel.IsComplete = true;
                        progressPanel.Invalidate();
                    }

                    var completionText = $"🚀 {message}\n\n" +
                                       "✅ Registry tweaks applied successfully\n" +
                                       "✅ Services optimized for performance\n" +
                                       "✅ Performance settings configured\n" +
                                       "✅ System cache optimized\n" +
                                       "✅ Gaming priority maximized\n" +
                                       "✅ CPU performance enhanced\n" +
                                       "✅ GPU settings optimized\n" +
                                       "✅ Memory management improved\n\n" +
                                       "🔄 RESTART RECOMMENDED for maximum performance!\n" +
                                       "📈 Expected FPS boost: +20-60%\n" +
                                       "⚡ System responsiveness dramatically improved\n" +
                                       "🎮 Gaming performance significantly enhanced\n" +
                                       "🚀 Latency reduced to minimum levels\n\n" +
                                       "🎯 Optimization completed successfully!\n" +
                                       "💯 Your system is now running at peak performance!";

                    controls.ResultsTextBox.Text = completionText;
                    controls.ResultsTextBox.ForeColor = Color.FromArgb(0, 255, 127);
                }
            }
        }

        private void ShowOptimizationError(string optimizationType, string error)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.StatusLabel != null && controls.ProgressBar != null && controls.ResultsTextBox != null)
                {
                    controls.StatusLabel.Text = "Optimization Failed!";
                    controls.StatusLabel.ForeColor = Color.FromArgb(255, 100, 100);
                    controls.ProgressBar.Style = ProgressBarStyle.Continuous;
                    controls.ProgressBar.Value = 0;
                    controls.ResultsTextBox.Text = $"Error applying {optimizationType} optimizations:\n\n{error}";
                }
            }
        }



        // Mouse drag functionality for borderless window
        private bool _dragging = false;
        private Point _dragCursorPoint;
        private Point _dragFormPoint;

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _dragging = true;
            _dragCursorPoint = Cursor.Position;
            _dragFormPoint = this.Location;
            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (_dragging)
            {
                Point dif = Point.Subtract(Cursor.Position, new Size(_dragCursorPoint));
                this.Location = Point.Add(_dragFormPoint, new Size(dif));
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _dragging = false;
            base.OnMouseUp(e);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
