using System;
using System.Drawing;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;
        private Label _titleLabel;
        private Label _statusLabel;
        private ProgressBar _progressBar;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();
            LoadCpuOptimization(); // Load CPU optimization by default

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // MainForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1600, 1000);
            this.MinimumSize = new Size(1400, 900);
            this.Name = "MainForm";
            this.Text = "RODEY PREMIUM TWEAKER v2.0 - PROFESSIONAL EDITION";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(8, 8, 8);
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Clean professional theme
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.ForeColor = Color.FromArgb(33, 33, 33);
        }

        private void CreateLayout()
        {
            // Create main panels - CLEAN PROFESSIONAL STYLE
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 250,
                BackColor = Color.White,
                Padding = new Padding(0, 0, 1, 0)
            };

            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(30)
            };

            // Create clean title bar
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 70,
                BackColor = Color.FromArgb(41, 128, 185)
            };

            _titleLabel = new Label
            {
                Text = "RODEY PREMIUM TWEAKER",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(20, 15)
            };

            var versionLabel = new Label
            {
                Text = "Professional Windows Optimization Suite",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(220, 220, 220),
                AutoSize = true,
                Location = new Point(20, 42)
            };

            titlePanel.Controls.AddRange(new Control[] { _titleLabel, versionLabel });

            // Create clean status bar
            var statusPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 35,
                BackColor = Color.FromArgb(236, 240, 241)
            };

            _statusLabel = new Label
            {
                Text = "Ready - Administrator Mode Active",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(52, 73, 94),
                AutoSize = true,
                Location = new Point(15, 8)
            };

            _progressBar = new ProgressBar
            {
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                Location = new Point(this.Width - 220, 5),
                Size = new Size(200, 20),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            statusPanel.Controls.AddRange(new Control[] { _statusLabel, _progressBar });

            // Create sidebar menu
            CreateSidebarMenu();

            // Add all panels to form
            this.Controls.AddRange(new Control[] { _contentPanel, _sidebarPanel, titlePanel, statusPanel });
        }

        private void CreateSidebarMenu()
        {
            var menuItems = new[]
            {
                new { Text = "CPU Optimization", Category = "PERFORMANCE" },
                new { Text = "GPU Optimization", Category = "PERFORMANCE" },
                new { Text = "Memory Optimization", Category = "PERFORMANCE" },
                new { Text = "Storage Optimization", Category = "PERFORMANCE" },
                new { Text = "Network Optimization", Category = "SYSTEM" },
                new { Text = "System Debloater", Category = "SYSTEM" },
                new { Text = "Services Optimizer", Category = "SYSTEM" },
                new { Text = "Startup Optimizer", Category = "SYSTEM" },
                new { Text = "Registry Tweaks", Category = "ADVANCED" },
                new { Text = "Power Management", Category = "ADVANCED" },
                new { Text = "Security Tweaks", Category = "ADVANCED" },
                new { Text = "BIOS Guide", Category = "ADVANCED" },
                new { Text = "Performance Monitor", Category = "MONITORING" },
                new { Text = "System Information", Category = "MONITORING" },
                new { Text = "Backup & Restore", Category = "TOOLS" },
                new { Text = "Settings", Category = "TOOLS" }
            };

            int yPos = 20;
            string currentCategory = "";

            foreach (var item in menuItems)
            {
                // Add category header if new category
                if (item.Category != currentCategory)
                {
                    if (currentCategory != "") yPos += 15; // Add spacing between categories

                    var categoryLabel = new Label
                    {
                        Text = item.Category,
                        Font = new Font("Segoe UI", 9, FontStyle.Bold),
                        ForeColor = Color.FromArgb(149, 165, 166),
                        AutoSize = true,
                        Location = new Point(20, yPos)
                    };
                    _sidebarPanel.Controls.Add(categoryLabel);
                    yPos += 30;
                    currentCategory = item.Category;
                }

                // Create clean menu button
                var menuButton = new Button
                {
                    Text = item.Text,
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(52, 73, 94),
                    BackColor = Color.Transparent,
                    FlatStyle = FlatStyle.Flat,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Size = new Size(230, 35),
                    Location = new Point(15, yPos),
                    Tag = item.Text
                };

                menuButton.FlatAppearance.BorderSize = 0;
                menuButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(236, 240, 241);
                menuButton.Click += MenuButton_Click;

                _sidebarPanel.Controls.Add(menuButton);
                yPos += 45;
            }
        }

        private void MenuButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            var menuText = button?.Tag?.ToString();

            // Clear content panel
            _contentPanel.Controls.Clear();

            // Load appropriate content based on menu selection
            switch (menuText)
            {
                case "CPU Optimization":
                    LoadCpuOptimization();
                    break;
                case "GPU Optimization":
                    LoadGpuOptimization();
                    break;
                case "Memory Optimization":
                    LoadMemoryOptimization();
                    break;
                case "Storage Optimization":
                    LoadStorageOptimization();
                    break;
                case "Network Optimization":
                    LoadNetworkOptimization();
                    break;
                case "System Debloater":
                    LoadSystemDebloater();
                    break;
                case "Services Optimizer":
                    LoadServicesOptimizer();
                    break;
                case "Startup Optimizer":
                    LoadStartupOptimizer();
                    break;
                case "Registry Tweaks":
                    LoadRegistryTweaks();
                    break;
                case "Power Management":
                    LoadPowerManagement();
                    break;
                case "Security Tweaks":
                    LoadSecurityTweaks();
                    break;
                case "BIOS Guide":
                    LoadBiosGuide();
                    break;
                case "Performance Monitor":
                    LoadPerformanceMonitor();
                    break;
                case "System Information":
                    LoadSystemInformation();
                    break;
                case "Backup & Restore":
                    LoadBackupRestore();
                    break;
                case "Settings":
                    LoadSettings();
                    break;
            }

            UpdateStatus($"Loaded {menuText}");
        }



        private void LoadCpuOptimization()
        {
            var cpuControl = new CpuOptimizationControl(_optimizer);
            cpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cpuControl);
        }

        private void LoadGpuOptimization()
        {
            var gpuControl = new GpuOptimizationControl(_optimizer);
            gpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(gpuControl);
        }

        private void LoadMemoryOptimization()
        {
            var memoryControl = new MemoryOptimizationControl(_optimizer);
            memoryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(memoryControl);
        }

        private void LoadStorageOptimization()
        {
            var storageControl = new StorageOptimizationControl(_optimizer);
            storageControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(storageControl);
        }

        private void LoadNetworkOptimization()
        {
            var networkControl = new NetworkOptimizationControl(_optimizer);
            networkControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(networkControl);
        }

        private void LoadSystemDebloater()
        {
            var debloaterControl = new SystemDebloaterControl(_optimizer);
            debloaterControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(debloaterControl);
        }

        private void LoadRegistryTweaks()
        {
            var registryControl = new RegistryTweaksControl(_optimizer);
            registryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(registryControl);
        }

        private void LoadServicesOptimizer()
        {
            var servicesControl = new ServicesOptimizerControl(_optimizer);
            servicesControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(servicesControl);
        }

        private void LoadStartupOptimizer()
        {
            var startupControl = new StartupOptimizerControl(_optimizer);
            startupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(startupControl);
        }

        private void LoadPowerManagement()
        {
            var powerControl = new PowerManagementControl(_optimizer);
            powerControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(powerControl);
        }

        private void LoadSecurityTweaks()
        {
            var securityControl = new SecurityTweaksControl(_optimizer);
            securityControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(securityControl);
        }

        private void LoadBiosGuide()
        {
            var biosControl = new BiosOptimizationControl(_optimizer);
            biosControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(biosControl);
        }

        private void LoadSystemInformation()
        {
            var systemInfoControl = new SystemInformationControl(_performanceMonitor);
            systemInfoControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(systemInfoControl);
        }

        private void LoadBackupRestore()
        {
            var backupControl = new BackupRestoreControl();
            backupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(backupControl);
        }

        private void LoadPerformanceMonitor()
        {
            var monitorControl = new PerformanceMonitorControl(_performanceMonitor);
            monitorControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(monitorControl);
        }

        private void LoadSettings()
        {
            var settingsControl = new SettingsControl();
            settingsControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(settingsControl);
        }

        private void UpdateStatus(string message)
        {
            _statusLabel.Text = message;
            _statusLabel.Refresh();
        }

        public void ShowProgress(bool show, string message = "")
        {
            _progressBar.Visible = show;
            if (show && !string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
