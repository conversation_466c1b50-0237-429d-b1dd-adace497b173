using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Enable modern .NET 8 features
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.SupportsTransparentBackColor, true);

            // MainForm with modern settings
            this.AutoScaleDimensions = new SizeF(96F, 96F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(1400, 900);
            this.MinimumSize = new Size(1200, 800);
            this.Name = "MainForm";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(24, 24, 27);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Professional dark theme
            this.BackColor = Color.FromArgb(32, 32, 32);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create custom title bar
            var titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.FromArgb(24, 24, 24)
            };

            var titleLabel = new Label
            {
                Text = "Rodey Premium Tweaker",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(15, 10),
                AutoSize = true
            };

            var closeButton = new Button
            {
                Text = "×",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 50, 5)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();

            titleBar.Controls.Add(titleLabel);
            titleBar.Controls.Add(closeButton);
            this.Controls.Add(titleBar);

            // Create sidebar for system info
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280,
                BackColor = Color.FromArgb(40, 40, 40),
                Padding = new Padding(20)
            };

            // Main content area
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(32, 32, 32),
                Padding = new Padding(30)
            };

            CreateSidebarContent();
            CreateMainContent();

            this.Controls.Add(_contentPanel);
            this.Controls.Add(_sidebarPanel);
        }

        private void CreateSidebarContent()
        {
            // System Information Section
            var systemInfoLabel = new Label
            {
                Text = "System Information",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            // System info icon
            var systemIcon = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(10, 60),
                BackColor = Color.FromArgb(0, 120, 215),
                BorderStyle = BorderStyle.None
            };
            systemIcon.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (var brush = new SolidBrush(Color.White))
                using (var font = new Font("Segoe UI", 24, FontStyle.Bold))
                {
                    var text = "R";
                    var size = g.MeasureString(text, font);
                    g.DrawString(text, font, brush,
                        (systemIcon.Width - size.Width) / 2,
                        (systemIcon.Height - size.Height) / 2);
                }
            };

            // System details
            var cpuLabel = new Label
            {
                Text = "CPU\nIntel Core i7-12700K",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 160),
                Size = new Size(240, 35)
            };

            var gpuLabel = new Label
            {
                Text = "GPU\nNVIDIA RTX 4080",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 200),
                Size = new Size(240, 35)
            };

            var ramLabel = new Label
            {
                Text = "RAM\n32 GB DDR4",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 240),
                Size = new Size(240, 35)
            };

            var storageLabel = new Label
            {
                Text = "Storage\n1TB NVMe SSD",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 280),
                Size = new Size(240, 35)
            };

            // Performance score
            var perfLabel = new Label
            {
                Text = "Performance Score",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 340),
                AutoSize = true
            };

            var scoreLabel = new Label
            {
                Text = "95/100",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 200, 100),
                Location = new Point(0, 365),
                AutoSize = true
            };

            _sidebarPanel.Controls.AddRange(new Control[]
            {
                systemInfoLabel, systemIcon, cpuLabel, gpuLabel,
                ramLabel, storageLabel, perfLabel, scoreLabel
            });
        }

        private void CreateMainContent()
        {
            // Main title
            var titleLabel = new Label
            {
                Text = "System Optimization",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            _contentPanel.Controls.Add(titleLabel);

            // Create optimization cards grid
            CreateOptimizationCards();
        }

        private void CreateOptimizationCards()
        {
            var cards = new[]
            {
                new { Title = "Optimize All", Icon = "🚀", Description = "Apply all optimizations", Color = Color.FromArgb(0, 120, 215), Tweaks = "3,247" },
                new { Title = "CPU", Icon = "⚡", Description = "Processor optimization", Color = Color.FromArgb(76, 175, 80), Tweaks = "347" },
                new { Title = "GPU", Icon = "🎮", Description = "Graphics optimization", Color = Color.FromArgb(156, 39, 176), Tweaks = "234" },
                new { Title = "Memory", Icon = "📊", Description = "RAM optimization", Color = Color.FromArgb(255, 152, 0), Tweaks = "156" },
                new { Title = "Storage", Icon = "💾", Description = "Disk optimization", Color = Color.FromArgb(96, 125, 139), Tweaks = "89" },
                new { Title = "Network", Icon = "🌐", Description = "Internet optimization", Color = Color.FromArgb(63, 81, 181), Tweaks = "67" },
                new { Title = "Power", Icon = "⚡", Description = "Power management", Color = Color.FromArgb(255, 193, 7), Tweaks = "45" },
                new { Title = "Registry", Icon = "⚙️", Description = "Registry tweaks", Color = Color.FromArgb(121, 85, 72), Tweaks = "892" },
                new { Title = "Privacy", Icon = "🔒", Description = "Privacy settings", Color = Color.FromArgb(244, 67, 54), Tweaks = "123" },
                new { Title = "Debloat", Icon = "🧹", Description = "Remove bloatware", Color = Color.FromArgb(139, 195, 74), Tweaks = "78" },
                new { Title = "Latency", Icon = "⚡", Description = "Reduce system latency", Color = Color.FromArgb(255, 87, 34), Tweaks = "234" },
                new { Title = "Advanced GPU", Icon = "🎮", Description = "Advanced graphics", Color = Color.FromArgb(103, 58, 183), Tweaks = "456" },
                new { Title = "Gaming", Icon = "🎯", Description = "Gaming optimization", Color = Color.FromArgb(233, 30, 99), Tweaks = "1,330" },
                new { Title = "BIOS", Icon = "⚡", Description = "BIOS optimization", Color = Color.FromArgb(0, 150, 136), Tweaks = "Guide" }
            };

            int cardWidth = 220;
            int cardHeight = 160;
            int margin = 30;
            int cardsPerRow = 5;
            int startX = 0;
            int startY = 100;

            for (int i = 0; i < cards.Length; i++)
            {
                int row = i / cardsPerRow;
                int col = i % cardsPerRow;

                var modernCard = new ModernCard
                {
                    Title = cards[i].Title,
                    Icon = cards[i].Icon,
                    Description = cards[i].Description,
                    AccentColor = cards[i].Color,
                    TweakCount = cards[i].Tweaks,
                    Location = new Point(startX + col * (cardWidth + margin), startY + row * (cardHeight + margin)),
                    Size = new Size(cardWidth, cardHeight)
                };

                modernCard.Click += (s, e) => HandleCardClick(cards[i].Title);
                _contentPanel.Controls.Add(modernCard);
            }
        }



        private async void HandleCardClick(string cardTitle)
        {
            // Show optimization in progress
            ShowOptimizationProgress(cardTitle);

            try
            {
                switch (cardTitle)
                {
                    case "Optimize All":
                        await ApplyAllOptimizations();
                        break;
                    case "CPU":
                        await ApplyCpuOptimizations();
                        break;
                    case "GPU":
                        await ApplyGpuOptimizations();
                        break;
                    case "Memory":
                        await ApplyMemoryOptimizations();
                        break;
                    case "Storage":
                        await ApplyStorageOptimizations();
                        break;
                    case "Network":
                        await ApplyNetworkOptimizations();
                        break;
                    case "Power":
                        await ApplyPowerOptimizations();
                        break;
                    case "Registry":
                        await ApplyRegistryOptimizations();
                        break;
                    case "Privacy":
                        await ApplyPrivacyOptimizations();
                        break;
                    case "Debloat":
                        await ApplyDebloatOptimizations();
                        break;
                    case "Latency":
                        await ApplyLatencyOptimizations();
                        break;
                    case "Advanced GPU":
                        await ApplyAdvancedGpuOptimizations();
                        break;
                    case "Gaming":
                        await ApplyGamingOptimizations();
                        break;
                    case "BIOS":
                        ShowBiosGuide();
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowOptimizationError(cardTitle, ex.Message);
            }
        }

        private void ShowOptimizationProgress(string optimizationType)
        {
            _contentPanel.Controls.Clear();

            // Use modern progress panel
            var progressPanel = new ModernProgressPanel
            {
                Title = $"🚀 {optimizationType.ToUpper()} OPTIMIZATION",
                Status = "⚡ Initializing optimization engine...",
                Progress = 0,
                Location = new Point(50, 50),
                Size = new Size(1200, 200)
            };

            // Modern results console
            var consolePanel = new Panel
            {
                Size = new Size(1200, 350),
                Location = new Point(50, 270),
                BackColor = Color.FromArgb(20, 20, 23)
            };

            consolePanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // Rounded corners
                var rect = new Rectangle(0, 0, consolePanel.Width - 1, consolePanel.Height - 1);
                using (var path = CreateRoundedRectanglePath(rect, 12))
                {
                    using (var brush = new SolidBrush(Color.FromArgb(20, 20, 23)))
                    {
                        g.FillPath(brush, path);
                    }

                    using (var pen = new Pen(Color.FromArgb(0, 120, 215), 2))
                    {
                        g.DrawPath(pen, path);
                    }
                }
            };

            var consoleHeader = new Label
            {
                Text = "📊 OPTIMIZATION CONSOLE",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 120, 215),
                BackColor = Color.Transparent,
                Location = new Point(20, 15),
                AutoSize = true
            };

            var resultsTextBox = new TextBox
            {
                Location = new Point(20, 45),
                Size = new Size(1160, 285),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.FromArgb(15, 15, 18),
                ForeColor = Color.FromArgb(0, 255, 127),
                BorderStyle = BorderStyle.None,
                Font = new Font("Consolas", 10, FontStyle.Regular),
                Text = "System optimization console initialized...\nReady to apply performance tweaks...\n"
            };

            consolePanel.Controls.AddRange(new Control[] { consoleHeader, resultsTextBox });

            // Modern back button with rounded corners
            var backButton = new Button
            {
                Text = "← BACK TO MAIN",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(0, 120, 215),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(180, 45),
                Location = new Point(50, 650)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { progressPanel, consolePanel, backButton });

            // Store references for updating during optimization
            _contentPanel.Tag = new { ProgressPanel = progressPanel, ResultsTextBox = resultsTextBox };
        }

        private GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        private async Task ApplyAllOptimizations()
        {
            UpdateOptimizationStatus("Applying all optimizations...");
            await _optimizer.OptimizeAllAsync();
            ShowOptimizationComplete("All optimizations applied successfully!");
        }

        private async Task ApplyCpuOptimizations()
        {
            UpdateOptimizationStatus("Optimizing CPU performance...");
            await _optimizer.OptimizeCpuAsync();
            ShowOptimizationComplete("CPU optimizations applied successfully!");
        }

        private async Task ApplyGpuOptimizations()
        {
            UpdateOptimizationStatus("Optimizing GPU performance...");
            await _optimizer.OptimizeGpuAsync();
            ShowOptimizationComplete("GPU optimizations applied successfully!");
        }

        private async Task ApplyMemoryOptimizations()
        {
            UpdateOptimizationStatus("Optimizing memory performance...");
            await _optimizer.OptimizeMemoryAsync();
            ShowOptimizationComplete("Memory optimizations applied successfully!");
        }

        private async Task ApplyStorageOptimizations()
        {
            UpdateOptimizationStatus("Optimizing storage performance...");
            await _optimizer.OptimizeStorageAsync();
            ShowOptimizationComplete("Storage optimizations applied successfully!");
        }

        private async Task ApplyNetworkOptimizations()
        {
            UpdateOptimizationStatus("Optimizing network performance...");
            await _optimizer.OptimizeNetworkAsync();
            ShowOptimizationComplete("Network optimizations applied successfully!");
        }

        private async Task ApplyPowerOptimizations()
        {
            UpdateOptimizationStatus("Optimizing power settings...");
            await _optimizer.OptimizePowerAsync();
            ShowOptimizationComplete("Power optimizations applied successfully!");
        }

        private async Task ApplyRegistryOptimizations()
        {
            UpdateOptimizationStatus("Applying registry tweaks...");
            await _optimizer.OptimizeRegistryAsync();
            ShowOptimizationComplete("Registry optimizations applied successfully!");
        }

        private async Task ApplyPrivacyOptimizations()
        {
            UpdateOptimizationStatus("Applying privacy settings...");
            await _optimizer.OptimizePrivacyAsync();
            ShowOptimizationComplete("Privacy optimizations applied successfully!");
        }

        private async Task ApplyDebloatOptimizations()
        {
            UpdateOptimizationStatus("Removing bloatware...");
            await _optimizer.DebloatSystemAsync();
            ShowOptimizationComplete("System debloat completed successfully!");
        }

        private async Task ApplyLatencyOptimizations()
        {
            UpdateOptimizationStatus("Reducing system latency...");
            await _optimizer.OptimizeLatencyAsync();
            ShowOptimizationComplete("Latency optimizations applied successfully!");
        }

        private async Task ApplyAdvancedGpuOptimizations()
        {
            UpdateOptimizationStatus("Applying advanced GPU tweaks...");
            await _optimizer.OptimizeAdvancedGpuAsync();
            ShowOptimizationComplete("Advanced GPU optimizations applied successfully!");
        }

        private async Task ApplyGamingOptimizations()
        {
            UpdateOptimizationStatus("Optimizing for gaming performance...");
            await _optimizer.OptimizeGamingAsync();
            ShowOptimizationComplete("Gaming optimizations applied successfully!");
        }

        private void ShowBiosGuide()
        {
            _contentPanel.Controls.Clear();

            var titleLabel = new Label
            {
                Text = "BIOS Optimization Guide",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var guideText = new TextBox
            {
                Location = new Point(0, 70),
                Size = new Size(800, 450),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.FromArgb(24, 24, 24),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 10),
                Text = "BIOS Optimization Guide:\n\n" +
                       "1. Enable XMP/DOCP for RAM\n" +
                       "2. Disable C-States for maximum performance\n" +
                       "3. Set CPU to Performance mode\n" +
                       "4. Disable Intel SpeedStep/AMD Cool'n'Quiet\n" +
                       "5. Enable High Performance Event Timer (HPET)\n" +
                       "6. Disable unnecessary USB ports\n" +
                       "7. Set PCIe to Gen 4 (if supported)\n" +
                       "8. Disable Secure Boot for compatibility\n" +
                       "9. Enable Resizable BAR for modern GPUs\n" +
                       "10. Optimize memory timings manually"
            };

            var backButton = new Button
            {
                Text = "← Back to Main",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(120, 30),
                Location = new Point(0, 540)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, guideText, backButton });
        }

        private void UpdateOptimizationStatus(string status)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.ProgressPanel != null)
                {
                    var progressPanel = controls.ProgressPanel as ModernProgressPanel;
                    if (progressPanel != null)
                    {
                        progressPanel.Status = status;
                        progressPanel.Progress = Math.Min(progressPanel.Progress + 10, 90);
                        progressPanel.Invalidate();
                    }
                }
            }
        }

        private void ShowOptimizationComplete(string message)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.ProgressPanel != null && controls.ResultsTextBox != null)
                {
                    var progressPanel = controls.ProgressPanel as ModernProgressPanel;
                    if (progressPanel != null)
                    {
                        progressPanel.Status = "✅ OPTIMIZATION COMPLETE!";
                        progressPanel.Progress = 100;
                        progressPanel.IsComplete = true;
                        progressPanel.Invalidate();
                    }

                    var completionText = $"🚀 {message}\n\n" +
                                       "✅ Registry tweaks applied successfully\n" +
                                       "✅ Services optimized for performance\n" +
                                       "✅ Performance settings configured\n" +
                                       "✅ System cache optimized\n" +
                                       "✅ Gaming priority maximized\n" +
                                       "✅ CPU performance enhanced\n" +
                                       "✅ GPU settings optimized\n" +
                                       "✅ Memory management improved\n\n" +
                                       "🔄 RESTART RECOMMENDED for maximum performance!\n" +
                                       "📈 Expected FPS boost: +20-60%\n" +
                                       "⚡ System responsiveness dramatically improved\n" +
                                       "🎮 Gaming performance significantly enhanced\n" +
                                       "🚀 Latency reduced to minimum levels\n\n" +
                                       "🎯 Optimization completed successfully!\n" +
                                       "💯 Your system is now running at peak performance!";

                    controls.ResultsTextBox.Text = completionText;
                    controls.ResultsTextBox.ForeColor = Color.FromArgb(0, 255, 127);
                }
            }
        }

        private void ShowOptimizationError(string optimizationType, string error)
        {
            if (_contentPanel.Tag != null)
            {
                var controls = (dynamic)_contentPanel.Tag;
                if (controls.StatusLabel != null && controls.ProgressBar != null && controls.ResultsTextBox != null)
                {
                    controls.StatusLabel.Text = "Optimization Failed!";
                    controls.StatusLabel.ForeColor = Color.FromArgb(255, 100, 100);
                    controls.ProgressBar.Style = ProgressBarStyle.Continuous;
                    controls.ProgressBar.Value = 0;
                    controls.ResultsTextBox.Text = $"Error applying {optimizationType} optimizations:\n\n{error}";
                }
            }
        }



        // Mouse drag functionality for borderless window
        private bool _dragging = false;
        private Point _dragCursorPoint;
        private Point _dragFormPoint;

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _dragging = true;
            _dragCursorPoint = Cursor.Position;
            _dragFormPoint = this.Location;
            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (_dragging)
            {
                Point dif = Point.Subtract(Cursor.Position, new Size(_dragCursorPoint));
                this.Location = Point.Add(_dragFormPoint, new Size(dif));
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _dragging = false;
            base.OnMouseUp(e);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
