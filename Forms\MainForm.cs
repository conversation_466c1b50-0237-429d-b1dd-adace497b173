using System;
using System.Drawing;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;
using RodeyPremiumTweaker.Controls;

namespace RodeyPremiumTweaker.Forms
{
    public partial class MainForm : Form
    {
        private readonly SystemOptimizer _optimizer;
        private readonly PerformanceMonitor _performanceMonitor;
        private Panel _sidebarPanel;
        private Panel _contentPanel;

        public MainForm()
        {
            _optimizer = new SystemOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            InitializeComponent();
            SetupDarkTheme();
            CreateLayout();

            // Start performance monitoring
            _performanceMonitor.StartMonitoring();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // MainForm
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.MinimumSize = new Size(1100, 700);
            this.Name = "MainForm";
            this.Text = "Rodey Premium Tweaker";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(32, 32, 32);
            // this.Icon = Properties.Resources.AppIcon;

            this.ResumeLayout(false);
        }

        private void SetupDarkTheme()
        {
            // Professional dark theme
            this.BackColor = Color.FromArgb(32, 32, 32);
            this.ForeColor = Color.White;
        }

        private void CreateLayout()
        {
            // Create custom title bar
            var titleBar = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.FromArgb(24, 24, 24)
            };

            var titleLabel = new Label
            {
                Text = "Rodey Premium Tweaker",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(15, 10),
                AutoSize = true
            };

            var closeButton = new Button
            {
                Text = "×",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(40, 30),
                Location = new Point(this.Width - 50, 5)
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();

            titleBar.Controls.Add(titleLabel);
            titleBar.Controls.Add(closeButton);
            this.Controls.Add(titleBar);

            // Create sidebar for system info
            _sidebarPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 280,
                BackColor = Color.FromArgb(40, 40, 40),
                Padding = new Padding(20)
            };

            // Main content area
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(32, 32, 32),
                Padding = new Padding(30)
            };

            CreateSidebarContent();
            CreateMainContent();

            this.Controls.Add(_contentPanel);
            this.Controls.Add(_sidebarPanel);
        }

        private void CreateSidebarContent()
        {
            // System Information Section
            var systemInfoLabel = new Label
            {
                Text = "System Information",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            // System info icon
            var systemIcon = new Panel
            {
                Size = new Size(80, 80),
                Location = new Point(10, 60),
                BackColor = Color.FromArgb(0, 120, 215),
                BorderStyle = BorderStyle.None
            };
            systemIcon.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (var brush = new SolidBrush(Color.White))
                using (var font = new Font("Segoe UI", 24, FontStyle.Bold))
                {
                    var text = "R";
                    var size = g.MeasureString(text, font);
                    g.DrawString(text, font, brush,
                        (systemIcon.Width - size.Width) / 2,
                        (systemIcon.Height - size.Height) / 2);
                }
            };

            // System details
            var cpuLabel = new Label
            {
                Text = "CPU\nIntel Core i7-12700K",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 160),
                Size = new Size(240, 35)
            };

            var gpuLabel = new Label
            {
                Text = "GPU\nNVIDIA RTX 4080",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 200),
                Size = new Size(240, 35)
            };

            var ramLabel = new Label
            {
                Text = "RAM\n32 GB DDR4",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 240),
                Size = new Size(240, 35)
            };

            var storageLabel = new Label
            {
                Text = "Storage\n1TB NVMe SSD",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(0, 280),
                Size = new Size(240, 35)
            };

            // Performance score
            var perfLabel = new Label
            {
                Text = "Performance Score",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 340),
                AutoSize = true
            };

            var scoreLabel = new Label
            {
                Text = "95/100",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 200, 100),
                Location = new Point(0, 365),
                AutoSize = true
            };

            _sidebarPanel.Controls.AddRange(new Control[]
            {
                systemInfoLabel, systemIcon, cpuLabel, gpuLabel,
                ramLabel, storageLabel, perfLabel, scoreLabel
            });
        }

        private void CreateMainContent()
        {
            // Main title
            var titleLabel = new Label
            {
                Text = "System Optimization",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            _contentPanel.Controls.Add(titleLabel);

            // Create optimization cards grid
            CreateOptimizationCards();
        }

        private void CreateOptimizationCards()
        {
            var cards = new[]
            {
                new { Title = "Optimize All", Icon = "🚀", Description = "Apply all optimizations", Color = Color.FromArgb(0, 120, 215) },
                new { Title = "CPU", Icon = "⚡", Description = "Processor optimization", Color = Color.FromArgb(76, 175, 80) },
                new { Title = "GPU", Icon = "🎮", Description = "Graphics optimization", Color = Color.FromArgb(156, 39, 176) },
                new { Title = "Memory", Icon = "📊", Description = "RAM optimization", Color = Color.FromArgb(255, 152, 0) },
                new { Title = "Storage", Icon = "💾", Description = "Disk optimization", Color = Color.FromArgb(96, 125, 139) },
                new { Title = "Network", Icon = "🌐", Description = "Internet optimization", Color = Color.FromArgb(63, 81, 181) },
                new { Title = "Power", Icon = "⚡", Description = "Power management", Color = Color.FromArgb(255, 193, 7) },
                new { Title = "Registry", Icon = "⚙️", Description = "Registry tweaks", Color = Color.FromArgb(121, 85, 72) },
                new { Title = "Privacy", Icon = "🔒", Description = "Privacy settings", Color = Color.FromArgb(244, 67, 54) },
                new { Title = "Debloat", Icon = "🧹", Description = "Remove bloatware", Color = Color.FromArgb(139, 195, 74) },
                new { Title = "Latency", Icon = "⚡", Description = "Reduce system latency", Color = Color.FromArgb(255, 87, 34) },
                new { Title = "Advanced GPU", Icon = "🎮", Description = "Advanced graphics", Color = Color.FromArgb(103, 58, 183) },
                new { Title = "Gaming", Icon = "🎯", Description = "Gaming optimization", Color = Color.FromArgb(233, 30, 99) },
                new { Title = "BIOS", Icon = "⚡", Description = "BIOS optimization", Color = Color.FromArgb(0, 150, 136) }
            };

            int cardWidth = 180;
            int cardHeight = 120;
            int margin = 20;
            int cardsPerRow = 4;
            int startX = 0;
            int startY = 100;

            for (int i = 0; i < cards.Length; i++)
            {
                int row = i / cardsPerRow;
                int col = i % cardsPerRow;

                var card = CreateOptimizationCard(
                    cards[i].Title,
                    cards[i].Icon,
                    cards[i].Description,
                    cards[i].Color,
                    new Point(startX + col * (cardWidth + margin), startY + row * (cardHeight + margin)),
                    new Size(cardWidth, cardHeight)
                );

                _contentPanel.Controls.Add(card);
            }
        }

        private Panel CreateOptimizationCard(string title, string icon, string description, Color accentColor, Point location, Size size)
        {
            var card = new Panel
            {
                Size = size,
                Location = location,
                BackColor = Color.FromArgb(48, 48, 48),
                BorderStyle = BorderStyle.None,
                Cursor = Cursors.Hand
            };

            // Add subtle border effect
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width - 1, card.Height - 1);
                using (var pen = new Pen(Color.FromArgb(64, 64, 64), 1))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            // Icon background
            var iconPanel = new Panel
            {
                Size = new Size(50, 50),
                Location = new Point(20, 20),
                BackColor = accentColor,
                BorderStyle = BorderStyle.None
            };

            // Icon text
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI", 18),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            iconPanel.Controls.Add(iconLabel);

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                Location = new Point(20, 80),
                AutoSize = true
            };

            // Description
            var descLabel = new Label
            {
                Text = description,
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(180, 180, 180),
                BackColor = Color.Transparent,
                Location = new Point(20, 100),
                AutoSize = true
            };

            // Hover effects
            card.MouseEnter += (s, e) =>
            {
                card.BackColor = Color.FromArgb(56, 56, 56);
            };

            card.MouseLeave += (s, e) =>
            {
                card.BackColor = Color.FromArgb(48, 48, 48);
            };

            // Click handler
            card.Click += (s, e) => HandleCardClick(title);
            iconPanel.Click += (s, e) => HandleCardClick(title);
            iconLabel.Click += (s, e) => HandleCardClick(title);
            titleLabel.Click += (s, e) => HandleCardClick(title);
            descLabel.Click += (s, e) => HandleCardClick(title);

            card.Controls.AddRange(new Control[] { iconPanel, titleLabel, descLabel });

            return card;
        }

        private void HandleCardClick(string cardTitle)
        {
            // Clear content panel and load specific optimization
            _contentPanel.Controls.Clear();

            switch (cardTitle)
            {
                case "Optimize All":
                    LoadOptimizeAll();
                    break;
                case "CPU":
                    LoadCpuOptimization();
                    break;
                case "GPU":
                    LoadGpuOptimization();
                    break;
                case "Memory":
                    LoadMemoryOptimization();
                    break;
                case "Storage":
                    LoadStorageOptimization();
                    break;
                case "Network":
                    LoadNetworkOptimization();
                    break;
                case "Power":
                    LoadPowerManagement();
                    break;
                case "Registry":
                    LoadRegistryTweaks();
                    break;
                case "Privacy":
                    LoadSecurityTweaks();
                    break;
                case "Debloat":
                    LoadSystemCleanup();
                    break;
                case "Latency":
                    LoadLatencyOptimization();
                    break;
                case "Advanced GPU":
                    LoadAdvancedGpuOptimization();
                    break;
                case "Gaming":
                    LoadGamingOptimization();
                    break;
                case "BIOS":
                    LoadBiosGuide();
                    break;
            }
        }

        private void LoadOptimizeAll()
        {
            // Create a comprehensive optimization view
            var titleLabel = new Label
            {
                Text = "Optimize All",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var backButton = new Button
            {
                Text = "← Back",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(80, 30),
                Location = new Point(0, 70)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, backButton });
        }

        private void LoadLatencyOptimization()
        {
            var titleLabel = new Label
            {
                Text = "Latency Optimization",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var backButton = new Button
            {
                Text = "← Back",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(80, 30),
                Location = new Point(0, 70)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, backButton });
        }

        private void LoadAdvancedGpuOptimization()
        {
            var titleLabel = new Label
            {
                Text = "Advanced GPU Optimization",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var backButton = new Button
            {
                Text = "← Back",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(80, 30),
                Location = new Point(0, 70)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, backButton });
        }

        private void LoadGamingOptimization()
        {
            var titleLabel = new Label
            {
                Text = "Gaming Optimization",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(0, 20),
                AutoSize = true
            };

            var backButton = new Button
            {
                Text = "← Back",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(64, 64, 64),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(80, 30),
                Location = new Point(0, 70)
            };
            backButton.FlatAppearance.BorderSize = 0;
            backButton.Click += (s, e) => { _contentPanel.Controls.Clear(); CreateMainContent(); };

            _contentPanel.Controls.AddRange(new Control[] { titleLabel, backButton });
        }

        private void LoadCpuOptimization()
        {
            var cpuControl = new CpuOptimizationControl(_optimizer);
            cpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cpuControl);
        }

        private void LoadGpuOptimization()
        {
            var gpuControl = new GpuOptimizationControl(_optimizer);
            gpuControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(gpuControl);
        }

        private void LoadMemoryOptimization()
        {
            var memoryControl = new MemoryOptimizationControl(_optimizer);
            memoryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(memoryControl);
        }

        private void LoadStorageOptimization()
        {
            var storageControl = new StorageOptimizationControl(_optimizer);
            storageControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(storageControl);
        }

        private void LoadNetworkOptimization()
        {
            var networkControl = new NetworkOptimizationControl(_optimizer);
            networkControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(networkControl);
        }

        private void LoadSystemCleanup()
        {
            var cleanupControl = new SystemDebloaterControl(_optimizer);
            cleanupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(cleanupControl);
        }

        private void LoadSystemDebloater()
        {
            var debloaterControl = new SystemDebloaterControl(_optimizer);
            debloaterControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(debloaterControl);
        }

        private void LoadRegistryTweaks()
        {
            var registryControl = new RegistryTweaksControl(_optimizer);
            registryControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(registryControl);
        }

        private void LoadServicesOptimizer()
        {
            var servicesControl = new ServicesOptimizerControl(_optimizer);
            servicesControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(servicesControl);
        }

        private void LoadStartupOptimizer()
        {
            var startupControl = new StartupOptimizerControl(_optimizer);
            startupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(startupControl);
        }

        private void LoadPowerManagement()
        {
            var powerControl = new PowerManagementControl(_optimizer);
            powerControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(powerControl);
        }

        private void LoadSecurityTweaks()
        {
            var securityControl = new SecurityTweaksControl(_optimizer);
            securityControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(securityControl);
        }

        private void LoadBiosGuide()
        {
            var biosControl = new BiosOptimizationControl(_optimizer);
            biosControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(biosControl);
        }

        private void LoadSystemInformation()
        {
            var systemInfoControl = new SystemInformationControl(_performanceMonitor);
            systemInfoControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(systemInfoControl);
        }

        private void LoadBackupRestore()
        {
            var backupControl = new BackupRestoreControl();
            backupControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(backupControl);
        }

        private void LoadPerformanceMonitor()
        {
            var monitorControl = new PerformanceMonitorControl(_performanceMonitor);
            monitorControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(monitorControl);
        }

        private void LoadSettings()
        {
            var settingsControl = new SettingsControl();
            settingsControl.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(settingsControl);
        }

        // Mouse drag functionality for borderless window
        private bool _dragging = false;
        private Point _dragCursorPoint;
        private Point _dragFormPoint;

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _dragging = true;
            _dragCursorPoint = Cursor.Position;
            _dragFormPoint = this.Location;
            base.OnMouseDown(e);
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (_dragging)
            {
                Point dif = Point.Subtract(Cursor.Position, new Size(_dragCursorPoint));
                this.Location = Point.Add(_dragFormPoint, new Size(dif));
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _dragging = false;
            base.OnMouseUp(e);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _performanceMonitor?.StopMonitoring();
            base.OnFormClosing(e);
        }
    }
}
