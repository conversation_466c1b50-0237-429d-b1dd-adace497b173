{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BarChartBig = createLucideIcon(\"BarChartBig\", [[\"path\", {\n  d: \"M3 3v18h18\",\n  key: \"1s2lah\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"7\",\n  x: \"7\",\n  y: \"10\",\n  rx: \"1\",\n  key: \"14u6mf\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"12\",\n  x: \"15\",\n  y: \"5\",\n  rx: \"1\",\n  key: \"b3pek6\"\n}]]);\nexport { BarChartBig as default };", "map": {"version": 3, "names": ["BarChartBig", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\bar-chart-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BarChartBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE4aDE4IiAvPgogIDxyZWN0IHdpZHRoPSI0IiBoZWlnaHQ9IjciIHg9IjciIHk9IjEwIiByeD0iMSIgLz4KICA8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSIxMiIgeD0iMTUiIHk9IjUiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bar-chart-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BarChartBig = createLucideIcon('BarChartBig', [\n  ['path', { d: 'M3 3v18h18', key: '1s2lah' }],\n  ['rect', { width: '4', height: '7', x: '7', y: '10', rx: '1', key: '14u6mf' }],\n  ['rect', { width: '4', height: '12', x: '15', y: '5', rx: '1', key: 'b3pek6' }],\n]);\n\nexport default BarChartBig;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}