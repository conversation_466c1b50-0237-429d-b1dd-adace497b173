{"ast": null, "code": "const appearAnimationStore = new Map();\nexport { appearAnimationStore };", "map": {"version": 3, "names": ["appearAnimationStore", "Map"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/animation/optimized-appear/store.mjs"], "sourcesContent": ["const appearAnimationStore = new Map();\n\nexport { appearAnimationStore };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEtC,SAASD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}