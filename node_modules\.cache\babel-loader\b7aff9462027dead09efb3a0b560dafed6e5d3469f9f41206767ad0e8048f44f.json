{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BringToFront = createLucideIcon(\"BringToFront\", [[\"rect\", {\n  x: \"8\",\n  y: \"8\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"yj20xf\"\n}], [\"path\", {\n  d: \"M4 10a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2\",\n  key: \"1ltk23\"\n}], [\"path\", {\n  d: \"M14 20a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2\",\n  key: \"1q24h9\"\n}]]);\nexport { BringToFront as default };", "map": {"version": 3, "names": ["BringToFront", "createLucideIcon", "x", "y", "width", "height", "rx", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\bring-to-front.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BringToFront\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSI4IiB5PSI4IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNNCAxMGEyIDIgMCAwIDEtMi0yVjRhMiAyIDAgMCAxIDItMmg0YTIgMiAwIDAgMSAyIDIiIC8+CiAgPHBhdGggZD0iTTE0IDIwYTIgMiAwIDAgMCAyIDJoNGEyIDIgMCAwIDAgMi0ydi00YTIgMiAwIDAgMC0yLTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bring-to-front\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BringToFront = createLucideIcon('BringToFront', [\n  ['rect', { x: '8', y: '8', width: '8', height: '8', rx: '2', key: 'yj20xf' }],\n  ['path', { d: 'M4 10a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2', key: '1ltk23' }],\n  ['path', { d: 'M14 20a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2', key: '1q24h9' }],\n]);\n\nexport default BringToFront;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAEC,CAAA,EAAG,qDAAuD;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,MAAQ;EAAEC,CAAA,EAAG,uDAAyD;EAAAD,GAAA,EAAK;AAAA,CAAU,EACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}