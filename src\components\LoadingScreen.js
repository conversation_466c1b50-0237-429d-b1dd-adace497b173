import React, { useState, useEffect } from 'react';
import { Shield, Zap, Activity, CheckCircle } from 'lucide-react';
import './LoadingScreen.css';

const LoadingScreen = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const loadingSteps = [
    { icon: Shield, text: 'Initializing Security Protocols...', duration: 800 },
    { icon: Activity, text: 'Scanning System Configuration...', duration: 600 },
    { icon: Zap, text: 'Loading Optimization Engine...', duration: 700 },
    { icon: CheckCircle, text: 'Ready for Maximum Performance!', duration: 400 }
  ];

  useEffect(() => {
    let stepTimer;
    let progressTimer;

    const startStep = (stepIndex) => {
      if (stepIndex >= loadingSteps.length) return;

      setCurrentStep(stepIndex);
      const step = loadingSteps[stepIndex];
      
      // Animate progress for current step
      const progressIncrement = 100 / loadingSteps.length;
      const startProgress = stepIndex * progressIncrement;
      const endProgress = (stepIndex + 1) * progressIncrement;
      
      let currentProgress = startProgress;
      const progressStep = (endProgress - startProgress) / (step.duration / 50);
      
      progressTimer = setInterval(() => {
        currentProgress += progressStep;
        if (currentProgress >= endProgress) {
          currentProgress = endProgress;
          clearInterval(progressTimer);
        }
        setProgress(currentProgress);
      }, 50);

      stepTimer = setTimeout(() => {
        startStep(stepIndex + 1);
      }, step.duration);
    };

    startStep(0);

    return () => {
      clearTimeout(stepTimer);
      clearInterval(progressTimer);
    };
  }, []);

  const currentStepData = loadingSteps[currentStep] || loadingSteps[loadingSteps.length - 1];
  const StepIcon = currentStepData.icon;

  return (
    <div className="loading-screen">
      <div className="loading-background">
        <div className="loading-particles">
          {[...Array(20)].map((_, i) => (
            <div key={i} className="particle" style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }} />
          ))}
        </div>
      </div>

      <div className="loading-content">
        <div className="loading-logo">
          <div className="logo-container">
            <Shield size={48} className="main-logo" />
            <div className="logo-glow"></div>
          </div>
          <div className="logo-text">
            <h1>RODEY PREMIUM</h1>
            <p>TWEAKER</p>
          </div>
        </div>

        <div className="loading-progress">
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="progress-text">{Math.round(progress)}%</div>
          </div>
        </div>

        <div className="loading-status">
          <div className="status-icon">
            <StepIcon size={24} />
          </div>
          <div className="status-text">
            {currentStepData.text}
          </div>
        </div>

        <div className="loading-steps">
          {loadingSteps.map((step, index) => {
            const StepIconComponent = step.icon;
            return (
              <div 
                key={index} 
                className={`step ${index <= currentStep ? 'completed' : ''} ${index === currentStep ? 'active' : ''}`}
              >
                <div className="step-icon">
                  <StepIconComponent size={16} />
                </div>
                <div className="step-indicator"></div>
              </div>
            );
          })}
        </div>

        <div className="loading-footer">
          <div className="version-info">
            <span>Version 1.0.0 Premium</span>
            <span>•</span>
            <span>Build 2024.1</span>
          </div>
          <div className="copyright">
            © 2024 Rodey Premium. All rights reserved.
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
