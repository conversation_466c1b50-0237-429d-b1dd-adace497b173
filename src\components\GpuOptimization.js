import React, { useState, useEffect } from 'react';
import { 
  Monitor, 
  Zap, 
  Thermometer, 
  Activity, 
  Settings, 
  Play, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  BarChart3,
  Gauge
} from 'lucide-react';
import './GpuOptimization.css';

const GpuOptimization = ({ isAdmin, systemInfo }) => {
  const [gpuTweaks, setGpuTweaks] = useState({
    powerLimit: false,
    memoryOverclock: false,
    coreOverclock: false,
    fanCurve: false,
    msiMode: false,
    preRenderedFrames: false,
    powerManagement: false,
    textureFiltering: false,
    vsync: false,
    gsync: false,
    hags: false,
    gameMode: false
  });

  const [gpuMetrics, setGpuMetrics] = useState({
    temperature: 42,
    usage: 8,
    memoryUsage: 15,
    coreClock: 1920,
    memoryClock: 7000,
    powerDraw: 85,
    fanSpeed: 35
  });

  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);

  const gpuOptimizations = [
    {
      id: 'powerLimit',
      title: 'Increase Power Limit',
      description: 'Raise GPU power limit to maximum for sustained performance',
      impact: 'Very High',
      category: 'Power Management',
      riskLevel: 'Medium',
      estimatedGain: '+20-40% performance',
      fpsGain: '+15-30 FPS'
    },
    {
      id: 'coreOverclock',
      title: 'GPU Core Overclock',
      description: 'Safely overclock GPU core for increased performance',
      impact: 'Very High',
      category: 'Overclocking',
      riskLevel: 'High',
      estimatedGain: '+15-25% performance',
      fpsGain: '+20-35 FPS'
    },
    {
      id: 'memoryOverclock',
      title: 'Memory Overclock',
      description: 'Overclock VRAM for better texture streaming and bandwidth',
      impact: 'High',
      category: 'Memory',
      riskLevel: 'High',
      estimatedGain: '+10-20% in memory-bound games',
      fpsGain: '+10-25 FPS'
    },
    {
      id: 'msiMode',
      title: 'Enable MSI Mode',
      description: 'Enable Message Signaled Interrupts for reduced latency',
      impact: 'High',
      category: 'Latency',
      riskLevel: 'Low',
      estimatedGain: '+5-15% latency reduction',
      fpsGain: '+5-10 FPS'
    },
    {
      id: 'preRenderedFrames',
      title: 'Optimize Pre-rendered Frames',
      description: 'Set optimal pre-rendered frames for reduced input lag',
      impact: 'Medium',
      category: 'Latency',
      riskLevel: 'Low',
      estimatedGain: 'Reduced input lag',
      fpsGain: '+2-8 FPS'
    },
    {
      id: 'powerManagement',
      title: 'Maximum Performance Mode',
      description: 'Set GPU to prefer maximum performance over power saving',
      impact: 'High',
      category: 'Power',
      riskLevel: 'Low',
      estimatedGain: '+10-20% consistent performance',
      fpsGain: '+8-15 FPS'
    },
    {
      id: 'textureFiltering',
      title: 'Optimize Texture Filtering',
      description: 'Set texture filtering to performance mode',
      impact: 'Medium',
      category: 'Graphics',
      riskLevel: 'Low',
      estimatedGain: '+5-10% in texture-heavy games',
      fpsGain: '+3-12 FPS'
    },
    {
      id: 'hags',
      title: 'Hardware Accelerated GPU Scheduling',
      description: 'Enable HAGS for reduced CPU overhead and better frame pacing',
      impact: 'Medium',
      category: 'Scheduling',
      riskLevel: 'Low',
      estimatedGain: '+3-8% CPU-bound scenarios',
      fpsGain: '+5-15 FPS'
    },
    {
      id: 'gameMode',
      title: 'Windows Game Mode',
      description: 'Enable Windows Game Mode for gaming optimizations',
      impact: 'Medium',
      category: 'System',
      riskLevel: 'Low',
      estimatedGain: '+2-5% overall gaming performance',
      fpsGain: '+2-8 FPS'
    },
    {
      id: 'fanCurve',
      title: 'Aggressive Fan Curve',
      description: 'Set aggressive fan curve to maintain lower temperatures',
      impact: 'Medium',
      category: 'Cooling',
      riskLevel: 'Low',
      estimatedGain: 'Better thermal throttling prevention',
      fpsGain: '+5-15 FPS (thermal limited)'
    }
  ];

  const handleTweakToggle = (tweakId) => {
    if (!isAdmin) return;
    
    setGpuTweaks(prev => ({
      ...prev,
      [tweakId]: !prev[tweakId]
    }));
  };

  const applyOptimizations = async () => {
    if (!isAdmin) return;
    
    setIsOptimizing(true);
    setOptimizationProgress(0);

    const enabledTweaks = Object.entries(gpuTweaks)
      .filter(([_, enabled]) => enabled)
      .map(([id, _]) => id);

    for (let i = 0; i < enabledTweaks.length; i++) {
      const tweakId = enabledTweaks[i];
      
      try {
        // Simulate applying tweak
        await new Promise(resolve => setTimeout(resolve, 1200));
        
        // Here you would call the actual optimization functions
        // await window.electronAPI.applyGpuTweak(tweakId);
        
        setOptimizationProgress(((i + 1) / enabledTweaks.length) * 100);
      } catch (error) {
        console.error(`Failed to apply ${tweakId}:`, error);
      }
    }

    setIsOptimizing(false);
    
    // Calculate estimated FPS gain
    const totalFpsGain = enabledTweaks.reduce((total, tweakId) => {
      const tweak = gpuOptimizations.find(t => t.id === tweakId);
      const fpsRange = tweak?.fpsGain?.match(/\+(\d+)-(\d+)/);
      if (fpsRange) {
        const avgGain = (parseInt(fpsRange[1]) + parseInt(fpsRange[2])) / 2;
        return total + avgGain;
      }
      return total;
    }, 0);

    await window.electronAPI.showMessageBox({
      type: 'info',
      title: 'GPU Optimization Complete',
      message: `Successfully applied ${enabledTweaks.length} GPU optimizations!\nEstimated FPS gain: +${Math.round(totalFpsGain)} FPS`,
      buttons: ['OK']
    });
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'Low': return '#00ff88';
      case 'Medium': return '#ffa502';
      case 'High': return '#ff4757';
      default: return '#888';
    }
  };

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'Very High': return '#ff6b35';
      case 'High': return '#00ff88';
      case 'Medium': return '#74b9ff';
      case 'Low': return '#888';
      default: return '#888';
    }
  };

  return (
    <div className="gpu-optimization">
      <div className="page-header">
        <div className="header-content">
          <div className="header-icon">
            <Monitor size={32} />
          </div>
          <div className="header-text">
            <h1>GPU Optimization</h1>
            <p>Graphics card tweaks and overclocking for maximum FPS</p>
          </div>
        </div>
        
        {systemInfo?.graphics?.[0] && (
          <div className="gpu-info-card">
            <h3>{systemInfo.graphics[0].model}</h3>
            <div className="gpu-specs">
              <span>{systemInfo.graphics[0].vendor}</span>
              {systemInfo.graphics[0].vram && (
                <>
                  <span>•</span>
                  <span>{Math.round(systemInfo.graphics[0].vram / 1024)} GB VRAM</span>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="gpu-content">
        {/* GPU Metrics */}
        <div className="metrics-section">
          <h2>Real-time GPU Metrics</h2>
          <div className="metrics-grid">
            <div className="metric-card">
              <div className="metric-icon temp">
                <Thermometer size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.temperature}°C</div>
                <div className="metric-label">Temperature</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon usage">
                <Activity size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.usage}%</div>
                <div className="metric-label">GPU Usage</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon memory">
                <BarChart3 size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.memoryUsage}%</div>
                <div className="metric-label">VRAM Usage</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon clock">
                <Gauge size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.coreClock} MHz</div>
                <div className="metric-label">Core Clock</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon power">
                <Zap size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.powerDraw}W</div>
                <div className="metric-label">Power Draw</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon fan">
                <Settings size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{gpuMetrics.fanSpeed}%</div>
                <div className="metric-label">Fan Speed</div>
              </div>
            </div>
          </div>
        </div>

        {/* Optimization Controls */}
        <div className="optimizations-section">
          <div className="section-header">
            <h2>GPU Optimizations</h2>
            <button 
              className="apply-btn"
              onClick={applyOptimizations}
              disabled={!isAdmin || isOptimizing || Object.values(gpuTweaks).every(v => !v)}
            >
              {isOptimizing ? (
                <>
                  <Activity size={16} className="spinning" />
                  Optimizing... {Math.round(optimizationProgress)}%
                </>
              ) : (
                <>
                  <Play size={16} />
                  Apply Selected Tweaks
                </>
              )}
            </button>
          </div>

          <div className="tweaks-grid">
            {gpuOptimizations.map((tweak) => (
              <div key={tweak.id} className={`tweak-card ${gpuTweaks[tweak.id] ? 'enabled' : ''}`}>
                <div className="tweak-header">
                  <div className="tweak-title">
                    <h3>{tweak.title}</h3>
                    <div className="tweak-badges">
                      <span 
                        className="impact-badge"
                        style={{ backgroundColor: getImpactColor(tweak.impact) }}
                      >
                        {tweak.impact} Impact
                      </span>
                      <span 
                        className="risk-badge"
                        style={{ backgroundColor: getRiskColor(tweak.riskLevel) }}
                      >
                        {tweak.riskLevel} Risk
                      </span>
                    </div>
                  </div>
                  
                  <label className="toggle">
                    <input
                      type="checkbox"
                      checked={gpuTweaks[tweak.id]}
                      onChange={() => handleTweakToggle(tweak.id)}
                      disabled={!isAdmin}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
                
                <p className="tweak-description">{tweak.description}</p>
                
                <div className="tweak-details">
                  <div className="detail-item">
                    <span className="detail-label">Category:</span>
                    <span className="detail-value">{tweak.category}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Performance Gain:</span>
                    <span className="detail-value gain">{tweak.estimatedGain}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">FPS Boost:</span>
                    <span className="detail-value fps-gain">{tweak.fpsGain}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {!isAdmin && (
        <div className="admin-required">
          <AlertTriangle size={20} />
          <span>Administrator privileges required for GPU optimizations</span>
        </div>
      )}
    </div>
  );
};

export default GpuOptimization;
