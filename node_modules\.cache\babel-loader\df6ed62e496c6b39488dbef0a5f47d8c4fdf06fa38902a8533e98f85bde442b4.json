{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\SystemOptimizer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Zap, Play, Pause, RotateCcw, Shield, AlertTriangle, CheckCircle, TrendingUp, Activity, Cpu, Monitor, MemoryStick, Wifi, HardDrive, Settings2 } from 'lucide-react';\nimport './SystemOptimizer.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SystemOptimizer = ({\n  isAdmin\n}) => {\n  _s();\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n  const [currentStep, setCurrentStep] = useState('');\n  const [optimizationResults, setOptimizationResults] = useState(null);\n  const [selectedProfile, setSelectedProfile] = useState('gaming');\n  const optimizationProfiles = {\n    gaming: {\n      name: 'Gaming Performance',\n      description: 'Maximum FPS and lowest latency for gaming',\n      icon: Zap,\n      color: '#00ff88',\n      tweaks: ['High Performance Power Plan', 'Disable Windows Game Bar', 'Optimize GPU Settings', 'Disable Fullscreen Optimizations', 'Set CPU Priority to High', 'Disable Windows Updates', 'Clean Temporary Files', 'Optimize Network Settings', 'Disable Background Apps', 'Registry Gaming Tweaks'],\n      estimatedGain: '+35-60% FPS boost'\n    },\n    extreme: {\n      name: 'Extreme Performance',\n      description: 'Maximum performance with all advanced tweaks',\n      icon: TrendingUp,\n      color: '#ff6b35',\n      tweaks: ['All Gaming Tweaks', 'Disable Security Mitigations', 'Aggressive CPU Overclocking', 'GPU Memory Overclock', 'Disable C-States', 'MSI Mode for GPU', 'Disable HPET', 'Registry Performance Tweaks', 'Kernel Optimizations', 'BIOS-level Tweaks'],\n      estimatedGain: '+60-100% FPS boost'\n    },\n    balanced: {\n      name: 'Balanced Optimization',\n      description: 'Good performance while maintaining stability',\n      icon: Shield,\n      color: '#74b9ff',\n      tweaks: ['Balanced Power Plan', 'Basic GPU Optimization', 'Memory Cleanup', 'Network Optimization', 'Startup Optimization', 'Visual Effects Tweaks', 'Safe Registry Tweaks', 'Service Optimization'],\n      estimatedGain: '+15-30% FPS boost'\n    }\n  };\n  const optimizationSteps = [{\n    name: 'Analyzing System',\n    icon: Activity,\n    duration: 2000\n  }, {\n    name: 'Optimizing CPU Settings',\n    icon: Cpu,\n    duration: 3000\n  }, {\n    name: 'Configuring GPU',\n    icon: Monitor,\n    duration: 2500\n  }, {\n    name: 'Optimizing Memory',\n    icon: MemoryStick,\n    duration: 2000\n  }, {\n    name: 'Network Optimization',\n    icon: Wifi,\n    duration: 1500\n  }, {\n    name: 'Registry Tweaks',\n    icon: Settings2,\n    duration: 4000\n  }, {\n    name: 'System Cleanup',\n    icon: HardDrive,\n    duration: 3000\n  }, {\n    name: 'Finalizing Changes',\n    icon: CheckCircle,\n    duration: 1000\n  }];\n  const startOptimization = async () => {\n    if (!isAdmin) {\n      await window.electronAPI.showMessageBox({\n        type: 'warning',\n        title: 'Administrator Required',\n        message: 'Administrator privileges are required for system optimization.',\n        buttons: ['OK']\n      });\n      return;\n    }\n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n    setOptimizationResults(null);\n    const profile = optimizationProfiles[selectedProfile];\n    let totalProgress = 0;\n    for (let i = 0; i < optimizationSteps.length; i++) {\n      const step = optimizationSteps[i];\n      setCurrentStep(step.name);\n\n      // Simulate optimization step\n      await new Promise(resolve => setTimeout(resolve, step.duration));\n      totalProgress = (i + 1) / optimizationSteps.length * 100;\n      setOptimizationProgress(totalProgress);\n    }\n\n    // Generate results\n    const results = {\n      profile: profile.name,\n      tweaksApplied: profile.tweaks.length,\n      estimatedFpsGain: profile.estimatedGain,\n      optimizationTime: optimizationSteps.reduce((total, step) => total + step.duration, 0) / 1000,\n      categories: {\n        cpu: Math.floor(Math.random() * 20) + 10,\n        gpu: Math.floor(Math.random() * 25) + 15,\n        memory: Math.floor(Math.random() * 15) + 8,\n        network: Math.floor(Math.random() * 10) + 5,\n        system: Math.floor(Math.random() * 18) + 12\n      }\n    };\n    setOptimizationResults(results);\n    setIsOptimizing(false);\n    setCurrentStep('');\n  };\n  const createRestorePoint = async () => {\n    if (!isAdmin) return;\n    try {\n      await window.electronAPI.showMessageBox({\n        type: 'info',\n        title: 'Creating Restore Point',\n        message: 'Creating system restore point before optimization...',\n        buttons: ['OK']\n      });\n\n      // Here you would create actual restore point\n      // await window.electronAPI.createRestorePoint('Rodey Premium Pre-Optimization');\n    } catch (error) {\n      console.error('Failed to create restore point:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"system-optimizer\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-icon\",\n          children: /*#__PURE__*/_jsxDEV(Zap, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"System Optimizer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"One-click optimization for maximum gaming performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"optimizer-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Optimization Profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-grid\",\n          children: Object.entries(optimizationProfiles).map(([key, profile]) => {\n            const ProfileIcon = profile.icon;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `profile-card ${selectedProfile === key ? 'selected' : ''}`,\n              onClick: () => setSelectedProfile(key),\n              style: {\n                '--profile-color': profile.color\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-icon\",\n                children: /*#__PURE__*/_jsxDEV(ProfileIcon, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: profile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: profile.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-gain\",\n                  children: profile.estimatedGain\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"profile-tweaks\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [profile.tweaks.length, \" tweaks\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"main-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"optimize-btn\",\n            onClick: startOptimization,\n            disabled: isOptimizing || !isAdmin,\n            children: isOptimizing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Pause, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), \"Optimizing System...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), \"Start Optimization\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"restore-btn\",\n            onClick: createRestorePoint,\n            disabled: isOptimizing || !isAdmin,\n            children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), \"Create Restore Point\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), isOptimizing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-step\",\n              children: currentStep\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"progress-percent\",\n              children: [Math.round(optimizationProgress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${optimizationProgress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-indicators\",\n            children: optimizationSteps.map((step, index) => {\n              const StepIcon = step.icon;\n              const isCompleted = (index + 1) / optimizationSteps.length * 100 <= optimizationProgress;\n              const isCurrent = step.name === currentStep;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `step-indicator ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(StepIcon, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), optimizationResults && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-header\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 24,\n            className: \"success-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Optimization Complete!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Profile Applied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-value\",\n              children: optimizationResults.profile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Tweaks Applied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-value\",\n              children: optimizationResults.tweaksApplied\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Estimated FPS Gain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-value gain\",\n              children: optimizationResults.estimatedFpsGain\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Optimization Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"result-value\",\n              children: [optimizationResults.optimizationTime, \"s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Performance Improvements by Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"category-grid\",\n            children: Object.entries(optimizationResults.categories).map(([category, improvement]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-name\",\n                children: category.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"category-improvement\",\n                children: [\"+\", improvement, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, category, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Included Optimizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tweaks-list\",\n          children: optimizationProfiles[selectedProfile].tweaks.map((tweak, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tweak-item\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: tweak\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-warning\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Administrator Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"System optimization requires administrator privileges. Please restart the application as administrator.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemOptimizer, \"hiVQ250zHt6ac7UWWN9GXnCVJVQ=\");\n_c = SystemOptimizer;\nexport default SystemOptimizer;\nvar _c;\n$RefreshReg$(_c, \"SystemOptimizer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Zap", "Play", "Pause", "RotateCcw", "Shield", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "TrendingUp", "Activity", "Cpu", "Monitor", "MemoryStick", "Wifi", "HardDrive", "Settings2", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SystemOptimizer", "isAdmin", "_s", "isOptimizing", "setIsOptimizing", "optimizationProgress", "setOptimizationProgress", "currentStep", "setCurrentStep", "optimizationResults", "setOptimizationResults", "selectedProfile", "setSelectedProfile", "optimizationProfiles", "gaming", "name", "description", "icon", "color", "tweaks", "estimatedGain", "extreme", "balanced", "optimizationSteps", "duration", "startOptimization", "window", "electronAPI", "showMessageBox", "type", "title", "message", "buttons", "profile", "totalProgress", "i", "length", "step", "Promise", "resolve", "setTimeout", "results", "tweaksApplied", "estimatedFpsGain", "optimizationTime", "reduce", "total", "categories", "cpu", "Math", "floor", "random", "gpu", "memory", "network", "system", "createRestorePoint", "error", "console", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "map", "key", "ProfileIcon", "onClick", "style", "disabled", "round", "width", "index", "StepIcon", "isCompleted", "isCurrent", "category", "improvement", "toUpperCase", "tweak", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/SystemOptimizer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Zap, \n  Play, \n  Pause, \n  RotateCcw, \n  Shield, \n  AlertTriangle,\n  CheckCircle,\n  TrendingUp,\n  Activity,\n  Cpu,\n  Monitor,\n  MemoryStick,\n  Wifi,\n  HardDrive,\n  Settings2\n} from 'lucide-react';\nimport './SystemOptimizer.css';\n\nconst SystemOptimizer = ({ isAdmin }) => {\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n  const [currentStep, setCurrentStep] = useState('');\n  const [optimizationResults, setOptimizationResults] = useState(null);\n  const [selectedProfile, setSelectedProfile] = useState('gaming');\n\n  const optimizationProfiles = {\n    gaming: {\n      name: 'Gaming Performance',\n      description: 'Maximum FPS and lowest latency for gaming',\n      icon: Zap,\n      color: '#00ff88',\n      tweaks: [\n        'High Performance Power Plan',\n        'Disable Windows Game Bar',\n        'Optimize GPU Settings',\n        'Disable Fullscreen Optimizations',\n        'Set CPU Priority to High',\n        'Disable Windows Updates',\n        'Clean Temporary Files',\n        'Optimize Network Settings',\n        'Disable Background Apps',\n        'Registry Gaming Tweaks'\n      ],\n      estimatedGain: '+35-60% FPS boost'\n    },\n    extreme: {\n      name: 'Extreme Performance',\n      description: 'Maximum performance with all advanced tweaks',\n      icon: TrendingUp,\n      color: '#ff6b35',\n      tweaks: [\n        'All Gaming Tweaks',\n        'Disable Security Mitigations',\n        'Aggressive CPU Overclocking',\n        'GPU Memory Overclock',\n        'Disable C-States',\n        'MSI Mode for GPU',\n        'Disable HPET',\n        'Registry Performance Tweaks',\n        'Kernel Optimizations',\n        'BIOS-level Tweaks'\n      ],\n      estimatedGain: '+60-100% FPS boost'\n    },\n    balanced: {\n      name: 'Balanced Optimization',\n      description: 'Good performance while maintaining stability',\n      icon: Shield,\n      color: '#74b9ff',\n      tweaks: [\n        'Balanced Power Plan',\n        'Basic GPU Optimization',\n        'Memory Cleanup',\n        'Network Optimization',\n        'Startup Optimization',\n        'Visual Effects Tweaks',\n        'Safe Registry Tweaks',\n        'Service Optimization'\n      ],\n      estimatedGain: '+15-30% FPS boost'\n    }\n  };\n\n  const optimizationSteps = [\n    { name: 'Analyzing System', icon: Activity, duration: 2000 },\n    { name: 'Optimizing CPU Settings', icon: Cpu, duration: 3000 },\n    { name: 'Configuring GPU', icon: Monitor, duration: 2500 },\n    { name: 'Optimizing Memory', icon: MemoryStick, duration: 2000 },\n    { name: 'Network Optimization', icon: Wifi, duration: 1500 },\n    { name: 'Registry Tweaks', icon: Settings2, duration: 4000 },\n    { name: 'System Cleanup', icon: HardDrive, duration: 3000 },\n    { name: 'Finalizing Changes', icon: CheckCircle, duration: 1000 }\n  ];\n\n  const startOptimization = async () => {\n    if (!isAdmin) {\n      await window.electronAPI.showMessageBox({\n        type: 'warning',\n        title: 'Administrator Required',\n        message: 'Administrator privileges are required for system optimization.',\n        buttons: ['OK']\n      });\n      return;\n    }\n\n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n    setOptimizationResults(null);\n\n    const profile = optimizationProfiles[selectedProfile];\n    let totalProgress = 0;\n\n    for (let i = 0; i < optimizationSteps.length; i++) {\n      const step = optimizationSteps[i];\n      setCurrentStep(step.name);\n\n      // Simulate optimization step\n      await new Promise(resolve => setTimeout(resolve, step.duration));\n\n      totalProgress = ((i + 1) / optimizationSteps.length) * 100;\n      setOptimizationProgress(totalProgress);\n    }\n\n    // Generate results\n    const results = {\n      profile: profile.name,\n      tweaksApplied: profile.tweaks.length,\n      estimatedFpsGain: profile.estimatedGain,\n      optimizationTime: optimizationSteps.reduce((total, step) => total + step.duration, 0) / 1000,\n      categories: {\n        cpu: Math.floor(Math.random() * 20) + 10,\n        gpu: Math.floor(Math.random() * 25) + 15,\n        memory: Math.floor(Math.random() * 15) + 8,\n        network: Math.floor(Math.random() * 10) + 5,\n        system: Math.floor(Math.random() * 18) + 12\n      }\n    };\n\n    setOptimizationResults(results);\n    setIsOptimizing(false);\n    setCurrentStep('');\n  };\n\n  const createRestorePoint = async () => {\n    if (!isAdmin) return;\n\n    try {\n      await window.electronAPI.showMessageBox({\n        type: 'info',\n        title: 'Creating Restore Point',\n        message: 'Creating system restore point before optimization...',\n        buttons: ['OK']\n      });\n\n      // Here you would create actual restore point\n      // await window.electronAPI.createRestorePoint('Rodey Premium Pre-Optimization');\n    } catch (error) {\n      console.error('Failed to create restore point:', error);\n    }\n  };\n\n  return (\n    <div className=\"system-optimizer\">\n      <div className=\"page-header\">\n        <div className=\"header-content\">\n          <div className=\"header-icon\">\n            <Zap size={32} />\n          </div>\n          <div className=\"header-text\">\n            <h1>System Optimizer</h1>\n            <p>One-click optimization for maximum gaming performance</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"optimizer-content\">\n        {/* Profile Selection */}\n        <div className=\"profile-section\">\n          <h2>Optimization Profile</h2>\n          <div className=\"profile-grid\">\n            {Object.entries(optimizationProfiles).map(([key, profile]) => {\n              const ProfileIcon = profile.icon;\n              return (\n                <div \n                  key={key}\n                  className={`profile-card ${selectedProfile === key ? 'selected' : ''}`}\n                  onClick={() => setSelectedProfile(key)}\n                  style={{ '--profile-color': profile.color }}\n                >\n                  <div className=\"profile-icon\">\n                    <ProfileIcon size={24} />\n                  </div>\n                  <div className=\"profile-content\">\n                    <h3>{profile.name}</h3>\n                    <p>{profile.description}</p>\n                    <div className=\"profile-gain\">{profile.estimatedGain}</div>\n                  </div>\n                  <div className=\"profile-tweaks\">\n                    <span>{profile.tweaks.length} tweaks</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Optimization Controls */}\n        <div className=\"controls-section\">\n          <div className=\"main-controls\">\n            <button \n              className=\"optimize-btn\"\n              onClick={startOptimization}\n              disabled={isOptimizing || !isAdmin}\n            >\n              {isOptimizing ? (\n                <>\n                  <Pause size={20} />\n                  Optimizing System...\n                </>\n              ) : (\n                <>\n                  <Play size={20} />\n                  Start Optimization\n                </>\n              )}\n            </button>\n\n            <button \n              className=\"restore-btn\"\n              onClick={createRestorePoint}\n              disabled={isOptimizing || !isAdmin}\n            >\n              <RotateCcw size={16} />\n              Create Restore Point\n            </button>\n          </div>\n\n          {isOptimizing && (\n            <div className=\"progress-section\">\n              <div className=\"progress-header\">\n                <span className=\"current-step\">{currentStep}</span>\n                <span className=\"progress-percent\">{Math.round(optimizationProgress)}%</span>\n              </div>\n              <div className=\"progress-bar\">\n                <div \n                  className=\"progress-fill\"\n                  style={{ width: `${optimizationProgress}%` }}\n                ></div>\n              </div>\n              <div className=\"step-indicators\">\n                {optimizationSteps.map((step, index) => {\n                  const StepIcon = step.icon;\n                  const isCompleted = (index + 1) / optimizationSteps.length * 100 <= optimizationProgress;\n                  const isCurrent = step.name === currentStep;\n                  \n                  return (\n                    <div \n                      key={index}\n                      className={`step-indicator ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}`}\n                    >\n                      <StepIcon size={16} />\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Results */}\n        {optimizationResults && (\n          <div className=\"results-section\">\n            <div className=\"results-header\">\n              <CheckCircle size={24} className=\"success-icon\" />\n              <h2>Optimization Complete!</h2>\n            </div>\n            \n            <div className=\"results-grid\">\n              <div className=\"result-card\">\n                <h3>Profile Applied</h3>\n                <div className=\"result-value\">{optimizationResults.profile}</div>\n              </div>\n              \n              <div className=\"result-card\">\n                <h3>Tweaks Applied</h3>\n                <div className=\"result-value\">{optimizationResults.tweaksApplied}</div>\n              </div>\n              \n              <div className=\"result-card\">\n                <h3>Estimated FPS Gain</h3>\n                <div className=\"result-value gain\">{optimizationResults.estimatedFpsGain}</div>\n              </div>\n              \n              <div className=\"result-card\">\n                <h3>Optimization Time</h3>\n                <div className=\"result-value\">{optimizationResults.optimizationTime}s</div>\n              </div>\n            </div>\n\n            <div className=\"category-results\">\n              <h3>Performance Improvements by Category</h3>\n              <div className=\"category-grid\">\n                {Object.entries(optimizationResults.categories).map(([category, improvement]) => (\n                  <div key={category} className=\"category-item\">\n                    <span className=\"category-name\">{category.toUpperCase()}</span>\n                    <span className=\"category-improvement\">+{improvement}%</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Selected Profile Details */}\n        <div className=\"profile-details\">\n          <h2>Included Optimizations</h2>\n          <div className=\"tweaks-list\">\n            {optimizationProfiles[selectedProfile].tweaks.map((tweak, index) => (\n              <div key={index} className=\"tweak-item\">\n                <CheckCircle size={16} />\n                <span>{tweak}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {!isAdmin && (\n        <div className=\"admin-warning\">\n          <AlertTriangle size={20} />\n          <div>\n            <strong>Administrator Required</strong>\n            <p>System optimization requires administrator privileges. Please restart the application as administrator.</p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SystemOptimizer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,GAAG,EACHC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,SAAS,QACJ,cAAc;AACrB,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,QAAQ,CAAC;EAEhE,MAAMkC,oBAAoB,GAAG;IAC3BC,MAAM,EAAE;MACNC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,2CAA2C;MACxDC,IAAI,EAAEpC,GAAG;MACTqC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CACN,6BAA6B,EAC7B,0BAA0B,EAC1B,uBAAuB,EACvB,kCAAkC,EAClC,0BAA0B,EAC1B,yBAAyB,EACzB,uBAAuB,EACvB,2BAA2B,EAC3B,yBAAyB,EACzB,wBAAwB,CACzB;MACDC,aAAa,EAAE;IACjB,CAAC;IACDC,OAAO,EAAE;MACPN,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,8CAA8C;MAC3DC,IAAI,EAAE7B,UAAU;MAChB8B,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CACN,mBAAmB,EACnB,8BAA8B,EAC9B,6BAA6B,EAC7B,sBAAsB,EACtB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,6BAA6B,EAC7B,sBAAsB,EACtB,mBAAmB,CACpB;MACDC,aAAa,EAAE;IACjB,CAAC;IACDE,QAAQ,EAAE;MACRP,IAAI,EAAE,uBAAuB;MAC7BC,WAAW,EAAE,8CAA8C;MAC3DC,IAAI,EAAEhC,MAAM;MACZiC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CACN,qBAAqB,EACrB,wBAAwB,EACxB,gBAAgB,EAChB,sBAAsB,EACtB,sBAAsB,EACtB,uBAAuB,EACvB,sBAAsB,EACtB,sBAAsB,CACvB;MACDC,aAAa,EAAE;IACjB;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAG,CACxB;IAAER,IAAI,EAAE,kBAAkB;IAAEE,IAAI,EAAE5B,QAAQ;IAAEmC,QAAQ,EAAE;EAAK,CAAC,EAC5D;IAAET,IAAI,EAAE,yBAAyB;IAAEE,IAAI,EAAE3B,GAAG;IAAEkC,QAAQ,EAAE;EAAK,CAAC,EAC9D;IAAET,IAAI,EAAE,iBAAiB;IAAEE,IAAI,EAAE1B,OAAO;IAAEiC,QAAQ,EAAE;EAAK,CAAC,EAC1D;IAAET,IAAI,EAAE,mBAAmB;IAAEE,IAAI,EAAEzB,WAAW;IAAEgC,QAAQ,EAAE;EAAK,CAAC,EAChE;IAAET,IAAI,EAAE,sBAAsB;IAAEE,IAAI,EAAExB,IAAI;IAAE+B,QAAQ,EAAE;EAAK,CAAC,EAC5D;IAAET,IAAI,EAAE,iBAAiB;IAAEE,IAAI,EAAEtB,SAAS;IAAE6B,QAAQ,EAAE;EAAK,CAAC,EAC5D;IAAET,IAAI,EAAE,gBAAgB;IAAEE,IAAI,EAAEvB,SAAS;IAAE8B,QAAQ,EAAE;EAAK,CAAC,EAC3D;IAAET,IAAI,EAAE,oBAAoB;IAAEE,IAAI,EAAE9B,WAAW;IAAEqC,QAAQ,EAAE;EAAK,CAAC,CAClE;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxB,OAAO,EAAE;MACZ,MAAMyB,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE,gEAAgE;QACzEC,OAAO,EAAE,CAAC,IAAI;MAChB,CAAC,CAAC;MACF;IACF;IAEA5B,eAAe,CAAC,IAAI,CAAC;IACrBE,uBAAuB,CAAC,CAAC,CAAC;IAC1BI,sBAAsB,CAAC,IAAI,CAAC;IAE5B,MAAMuB,OAAO,GAAGpB,oBAAoB,CAACF,eAAe,CAAC;IACrD,IAAIuB,aAAa,GAAG,CAAC;IAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,iBAAiB,CAACa,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,MAAME,IAAI,GAAGd,iBAAiB,CAACY,CAAC,CAAC;MACjC3B,cAAc,CAAC6B,IAAI,CAACtB,IAAI,CAAC;;MAEzB;MACA,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,IAAI,CAACb,QAAQ,CAAC,CAAC;MAEhEU,aAAa,GAAI,CAACC,CAAC,GAAG,CAAC,IAAIZ,iBAAiB,CAACa,MAAM,GAAI,GAAG;MAC1D9B,uBAAuB,CAAC4B,aAAa,CAAC;IACxC;;IAEA;IACA,MAAMO,OAAO,GAAG;MACdR,OAAO,EAAEA,OAAO,CAAClB,IAAI;MACrB2B,aAAa,EAAET,OAAO,CAACd,MAAM,CAACiB,MAAM;MACpCO,gBAAgB,EAAEV,OAAO,CAACb,aAAa;MACvCwB,gBAAgB,EAAErB,iBAAiB,CAACsB,MAAM,CAAC,CAACC,KAAK,EAAET,IAAI,KAAKS,KAAK,GAAGT,IAAI,CAACb,QAAQ,EAAE,CAAC,CAAC,GAAG,IAAI;MAC5FuB,UAAU,EAAE;QACVC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;QACxCC,GAAG,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;QACxCE,MAAM,EAAEJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;QAC1CG,OAAO,EAAEL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;QAC3CI,MAAM,EAAEN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;MAC3C;IACF,CAAC;IAEDzC,sBAAsB,CAAC+B,OAAO,CAAC;IAC/BrC,eAAe,CAAC,KAAK,CAAC;IACtBI,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMgD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACvD,OAAO,EAAE;IAEd,IAAI;MACF,MAAMyB,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC;QACtCC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE,sDAAsD;QAC/DC,OAAO,EAAE,CAAC,IAAI;MAChB,CAAC,CAAC;;MAEF;MACA;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,oBACE5D,OAAA;IAAK8D,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/B/D,OAAA;MAAK8D,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B/D,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/D,OAAA;UAAK8D,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/D,OAAA,CAAChB,GAAG;YAACgF,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNpE,OAAA;UAAK8D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/D,OAAA;YAAA+D,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBpE,OAAA;YAAA+D,QAAA,EAAG;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpE,OAAA;MAAK8D,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhC/D,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/D,OAAA;UAAA+D,QAAA,EAAI;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BpE,OAAA;UAAK8D,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BM,MAAM,CAACC,OAAO,CAACtD,oBAAoB,CAAC,CAACuD,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEpC,OAAO,CAAC,KAAK;YAC5D,MAAMqC,WAAW,GAAGrC,OAAO,CAAChB,IAAI;YAChC,oBACEpB,OAAA;cAEE8D,SAAS,EAAE,gBAAgBhD,eAAe,KAAK0D,GAAG,GAAG,UAAU,GAAG,EAAE,EAAG;cACvEE,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAACyD,GAAG,CAAE;cACvCG,KAAK,EAAE;gBAAE,iBAAiB,EAAEvC,OAAO,CAACf;cAAM,CAAE;cAAA0C,QAAA,gBAE5C/D,OAAA;gBAAK8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3B/D,OAAA,CAACyE,WAAW;kBAACT,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNpE,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/D,OAAA;kBAAA+D,QAAA,EAAK3B,OAAO,CAAClB;gBAAI;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBpE,OAAA;kBAAA+D,QAAA,EAAI3B,OAAO,CAACjB;gBAAW;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5BpE,OAAA;kBAAK8D,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAE3B,OAAO,CAACb;gBAAa;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNpE,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B/D,OAAA;kBAAA+D,QAAA,GAAO3B,OAAO,CAACd,MAAM,CAACiB,MAAM,EAAC,SAAO;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA,GAfDI,GAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBL,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpE,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/D,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/D,OAAA;YACE8D,SAAS,EAAC,cAAc;YACxBY,OAAO,EAAE9C,iBAAkB;YAC3BgD,QAAQ,EAAEtE,YAAY,IAAI,CAACF,OAAQ;YAAA2D,QAAA,EAElCzD,YAAY,gBACXN,OAAA,CAAAE,SAAA;cAAA6D,QAAA,gBACE/D,OAAA,CAACd,KAAK;gBAAC8E,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAErB;YAAA,eAAE,CAAC,gBAEHpE,OAAA,CAAAE,SAAA;cAAA6D,QAAA,gBACE/D,OAAA,CAACf,IAAI;gBAAC+E,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEpB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAETpE,OAAA;YACE8D,SAAS,EAAC,aAAa;YACvBY,OAAO,EAAEf,kBAAmB;YAC5BiB,QAAQ,EAAEtE,YAAY,IAAI,CAACF,OAAQ;YAAA2D,QAAA,gBAEnC/D,OAAA,CAACb,SAAS;cAAC6E,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL9D,YAAY,iBACXN,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/D,OAAA;YAAK8D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B/D,OAAA;cAAM8D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAErD;YAAW;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDpE,OAAA;cAAM8D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAEX,IAAI,CAACyB,KAAK,CAACrE,oBAAoB,CAAC,EAAC,GAAC;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eACNpE,OAAA;YAAK8D,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B/D,OAAA;cACE8D,SAAS,EAAC,eAAe;cACzBa,KAAK,EAAE;gBAAEG,KAAK,EAAE,GAAGtE,oBAAoB;cAAI;YAAE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNpE,OAAA;YAAK8D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BrC,iBAAiB,CAAC6C,GAAG,CAAC,CAAC/B,IAAI,EAAEuC,KAAK,KAAK;cACtC,MAAMC,QAAQ,GAAGxC,IAAI,CAACpB,IAAI;cAC1B,MAAM6D,WAAW,GAAG,CAACF,KAAK,GAAG,CAAC,IAAIrD,iBAAiB,CAACa,MAAM,GAAG,GAAG,IAAI/B,oBAAoB;cACxF,MAAM0E,SAAS,GAAG1C,IAAI,CAACtB,IAAI,KAAKR,WAAW;cAE3C,oBACEV,OAAA;gBAEE8D,SAAS,EAAE,kBAAkBmB,WAAW,GAAG,WAAW,GAAG,EAAE,IAAIC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;gBAAAnB,QAAA,eAE5F/D,OAAA,CAACgF,QAAQ;kBAAChB,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC,GAHjBW,KAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIP,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLxD,mBAAmB,iBAClBZ,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/D,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/D,OAAA,CAACV,WAAW;YAAC0E,IAAI,EAAE,EAAG;YAACF,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDpE,OAAA;YAAA+D,QAAA,EAAI;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAENpE,OAAA;UAAK8D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/D,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAA+D,QAAA,EAAI;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxBpE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEnD,mBAAmB,CAACwB;YAAO;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENpE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAA+D,QAAA,EAAI;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBpE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEnD,mBAAmB,CAACiC;YAAa;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAENpE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAA+D,QAAA,EAAI;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BpE,OAAA;cAAK8D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEnD,mBAAmB,CAACkC;YAAgB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC,eAENpE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAA+D,QAAA,EAAI;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BpE,OAAA;cAAK8D,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAEnD,mBAAmB,CAACmC,gBAAgB,EAAC,GAAC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpE,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B/D,OAAA;YAAA+D,QAAA,EAAI;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CpE,OAAA;YAAK8D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BM,MAAM,CAACC,OAAO,CAAC1D,mBAAmB,CAACsC,UAAU,CAAC,CAACqB,GAAG,CAAC,CAAC,CAACY,QAAQ,EAAEC,WAAW,CAAC,kBAC1EpF,OAAA;cAAoB8D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC3C/D,OAAA;gBAAM8D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEoB,QAAQ,CAACE,WAAW,CAAC;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/DpE,OAAA;gBAAM8D,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GAAC,GAAC,EAACqB,WAAW,EAAC,GAAC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFrDe,QAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDpE,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/D,OAAA;UAAA+D,QAAA,EAAI;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BpE,OAAA;UAAK8D,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzB/C,oBAAoB,CAACF,eAAe,CAAC,CAACQ,MAAM,CAACiD,GAAG,CAAC,CAACe,KAAK,EAAEP,KAAK,kBAC7D/E,OAAA;YAAiB8D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACrC/D,OAAA,CAACV,WAAW;cAAC0E,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBpE,OAAA;cAAA+D,QAAA,EAAOuB;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAFZW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAAChE,OAAO,iBACPJ,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/D,OAAA,CAACX,aAAa;QAAC2E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BpE,OAAA;QAAA+D,QAAA,gBACE/D,OAAA;UAAA+D,QAAA,EAAQ;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvCpE,OAAA;UAAA+D,QAAA,EAAG;QAAuG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAhUIF,eAAe;AAAAoF,EAAA,GAAfpF,eAAe;AAkUrB,eAAeA,eAAe;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}