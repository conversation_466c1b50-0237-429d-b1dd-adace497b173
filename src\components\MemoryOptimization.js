import React from 'react';
import { MemoryStick, AlertTriangle } from 'lucide-react';

const MemoryOptimization = ({ isAdmin, systemInfo }) => {
  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          background: 'linear-gradient(135deg, #00ff88 0%, #00cc6a 100%)',
          borderRadius: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#000'
        }}>
          <MemoryStick size={32} />
        </div>
        <div>
          <h1 style={{ color: '#00ff88', fontSize: '28px', fontWeight: '700', margin: '0 0 4px 0' }}>
            Memory Optimization
          </h1>
          <p style={{ color: '#888', fontSize: '14px', margin: '0' }}>
            RAM optimization and memory management tweaks
          </p>
        </div>
      </div>
      
      <div style={{ 
        background: 'rgba(26, 26, 26, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <h2 style={{ color: '#fff', marginBottom: '16px' }}>Memory Optimization Coming Soon</h2>
        <p style={{ color: '#888', marginBottom: '20px' }}>
          Advanced memory tweaks, RAM optimization, and cache management will be available here.
        </p>
        
        {systemInfo?.memory && (
          <div style={{ 
            background: 'rgba(0, 255, 136, 0.1)',
            border: '1px solid rgba(0, 255, 136, 0.2)',
            borderRadius: '8px',
            padding: '16px',
            marginTop: '16px'
          }}>
            <h3 style={{ color: '#00ff88', margin: '0 0 8px 0' }}>
              Total RAM: {(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB
            </h3>
            <p style={{ color: '#888', margin: '0' }}>
              Available: {(systemInfo.memory.free / 1024 / 1024 / 1024).toFixed(1)} GB
            </p>
          </div>
        )}
      </div>

      {!isAdmin && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          background: 'rgba(255, 165, 2, 0.1)',
          border: '1px solid rgba(255, 165, 2, 0.3)',
          borderRadius: '8px',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: '#ffa502'
        }}>
          <AlertTriangle size={20} />
          <span>Administrator privileges required</span>
        </div>
      )}
    </div>
  );
};

export default MemoryOptimization;
