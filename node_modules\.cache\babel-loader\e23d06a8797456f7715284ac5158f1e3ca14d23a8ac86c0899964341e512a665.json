{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BoomBox = createLucideIcon(\"BoomBox\", [[\"path\", {\n  d: \"M4 9V5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4\",\n  key: \"vvzvr1\"\n}], [\"path\", {\n  d: \"M8 8v1\",\n  key: \"xcqmfk\"\n}], [\"path\", {\n  d: \"M12 8v1\",\n  key: \"1rj8u4\"\n}], [\"path\", {\n  d: \"M16 8v1\",\n  key: \"1q12zr\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"12\",\n  x: \"2\",\n  y: \"9\",\n  rx: \"2\",\n  key: \"igpb89\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"fa4a8s\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"15\",\n  r: \"2\",\n  key: \"14c3ya\"\n}]]);\nexport { BoomBox as default };", "map": {"version": 3, "names": ["BoomBox", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx", "cx", "cy", "r"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\boom-box.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BoomBox\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA5VjVhMiAyIDAgMCAxIDItMmgxMmEyIDIgMCAwIDEgMiAydjQiIC8+CiAgPHBhdGggZD0iTTggOHYxIiAvPgogIDxwYXRoIGQ9Ik0xMiA4djEiIC8+CiAgPHBhdGggZD0iTTE2IDh2MSIgLz4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjkiIHJ4PSIyIiAvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSIxNSIgcj0iMiIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE1IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/boom-box\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BoomBox = createLucideIcon('BoomBox', [\n  ['path', { d: 'M4 9V5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4', key: 'vvzvr1' }],\n  ['path', { d: 'M8 8v1', key: 'xcqmfk' }],\n  ['path', { d: 'M12 8v1', key: '1rj8u4' }],\n  ['path', { d: 'M16 8v1', key: '1q12zr' }],\n  ['rect', { width: '20', height: '12', x: '2', y: '9', rx: '2', key: 'igpb89' }],\n  ['circle', { cx: '8', cy: '15', r: '2', key: 'fa4a8s' }],\n  ['circle', { cx: '16', cy: '15', r: '2', key: '14c3ya' }],\n]);\n\nexport default BoomBox;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAU;EAAEM,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEM,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKR,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}