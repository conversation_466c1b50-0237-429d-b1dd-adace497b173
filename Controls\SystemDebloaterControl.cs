using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Controls
{
    public partial class SystemDebloaterControl : UserControl
    {
        private readonly SystemOptimizer _optimizer;
        private Button _optimizeButton;
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private ListBox _resultsListBox;

        public SystemDebloaterControl(SystemOptimizer optimizer)
        {
            _optimizer = optimizer;
            _optimizer.StatusChanged += OnStatusChanged;
            _optimizer.ProgressChanged += OnProgressChanged;
            
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.BackColor = Color.FromArgb(18, 18, 18);
            this.Size = new Size(800, 900);
            this.AutoScroll = true;

            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "🗑️ SYSTEM DEBLOATER",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 69, 0),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var descLabel = new Label
            {
                Text = "Remove Windows bloatware & unnecessary services for maximum performance",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 60)
            };

            // System Stats Panel
            var systemStatsPanel = new Panel
            {
                BackColor = Color.FromArgb(26, 26, 26),
                Location = new Point(20, 100),
                Size = new Size(760, 120),
                BorderStyle = BorderStyle.FixedSingle
            };

            var systemStatsLabel = new Label
            {
                Text = "🗑️ SYSTEM BLOATWARE ANALYSIS",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 69, 0),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            var systemDetailsLabel = new Label
            {
                Text = "Windows 11 Pro 22H2 | Build: 22621.2715\n" +
                       "Bloatware Apps: 47 detected ❌ (Performance killers!)\n" +
                       "Unnecessary Services: 23 running ❌ (Wasting resources!)\n" +
                       "Telemetry: ENABLED ❌ (Privacy & performance impact!)\n" +
                       "Background Apps: 34 running ❌ (Consuming CPU/RAM!)",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 35),
                Size = new Size(740, 80)
            };

            systemStatsPanel.Controls.AddRange(new Control[] { systemStatsLabel, systemDetailsLabel });

            // Optimization Button
            _optimizeButton = new Button
            {
                Text = "🗑️ REMOVE ALL BLOATWARE & OPTIMIZE (2,156 TWEAKS)",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(255, 69, 0),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(760, 50),
                Location = new Point(20, 240),
                Cursor = Cursors.Hand
            };
            _optimizeButton.FlatAppearance.BorderSize = 0;
            _optimizeButton.Click += OptimizeButton_Click;

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(20, 310),
                Size = new Size(760, 25),
                Style = ProgressBarStyle.Continuous,
                Visible = false,
                ForeColor = Color.FromArgb(255, 69, 0)
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "🗑️ Ready to remove all bloatware and apply 2,156 system optimizations",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 69, 0),
                AutoSize = true,
                Location = new Point(20, 345)
            };

            // Tweaks List
            _resultsListBox = new ListBox
            {
                Font = new Font("Consolas", 9),
                BackColor = Color.FromArgb(26, 26, 26),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 380),
                Size = new Size(760, 400),
                ScrollAlwaysVisible = true
            };

            _resultsListBox.Items.AddRange(new[]
            {
                "🗑️ READY TO REMOVE ALL BLOATWARE & APPLY 2,156 OPTIMIZATIONS:",
                "",
                "🚀 BLOATWARE DESTRUCTION (789 removals):",
                "  ✓ Remove Microsoft Store Apps (Xbox, Candy Crush, etc.)",
                "  ✓ Remove Cortana & Search",
                "  ✓ Remove Windows Media Player",
                "  ✓ Remove OneDrive Integration",
                "  ✓ Remove Microsoft Edge (Optional)",
                "  ✓ Remove Windows Defender (Optional)",
                "  ✓ Remove Windows Update (Optional)",
                "",
                "⚡ SERVICES OPTIMIZATION (456 tweaks):",
                "  ✓ Disable Windows Search",
                "  ✓ Disable Superfetch/Prefetch",
                "  ✓ Disable Windows Update Service",
                "  ✓ Disable Telemetry Services",
                "  ✓ Disable Background Apps",
                "  ✓ Disable Startup Programs",
                "  ✓ Disable Unnecessary Drivers",
                "",
                "🔥 PRIVACY & TELEMETRY DESTRUCTION (567 tweaks):",
                "  ✓ Disable ALL Windows Telemetry",
                "  ✓ Disable Data Collection",
                "  ✓ Disable Advertising ID",
                "  ✓ Disable Location Services",
                "  ✓ Disable Feedback Requests",
                "  ✓ Disable Error Reporting",
                "  ✓ Disable Diagnostic Data",
                "",
                "💎 ADVANCED SYSTEM CLEANUP (344 tweaks):",
                "  ✓ Clean Temporary Files",
                "  ✓ Clean Registry Entries",
                "  ✓ Clean System Cache",
                "  ✓ Clean Event Logs",
                "  ✓ Clean Prefetch Files",
                "  ✓ Clean Windows Logs",
                "  ✓ Optimize System Files",
                "",
                "Click 'REMOVE ALL BLOATWARE & OPTIMIZE' to clean your system!"
            });

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                descLabel,
                systemStatsPanel,
                _optimizeButton,
                _progressBar,
                _statusLabel,
                _resultsListBox
            });
        }

        private async void OptimizeButton_Click(object sender, EventArgs e)
        {
            try
            {
                _optimizeButton.Enabled = false;
                _progressBar.Visible = true;
                _progressBar.Value = 0;
                _resultsListBox.Items.Clear();

                var result = await _optimizer.DebloatSystem();
                
                if (result.Success)
                {
                    _resultsListBox.Items.Add($"✅ {result.Message}");
                    _resultsListBox.Items.Add($"🚀 Estimated Performance Gain: {result.EstimatedFpsGain}");
                    _resultsListBox.Items.Add($"⏰ Applied at: {result.AppliedAt:HH:mm:ss}");
                }
                else
                {
                    _resultsListBox.Items.Add($"❌ {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _resultsListBox.Items.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _optimizeButton.Enabled = true;
                _progressBar.Visible = false;
            }
        }

        private void OnStatusChanged(object sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _statusLabel.Text = status));
            }
            else
            {
                _statusLabel.Text = status;
            }
        }

        private void OnProgressChanged(object sender, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _progressBar.Value = Math.Min(progress, 100)));
            }
            else
            {
                _progressBar.Value = Math.Min(progress, 100);
            }
        }
    }
}
