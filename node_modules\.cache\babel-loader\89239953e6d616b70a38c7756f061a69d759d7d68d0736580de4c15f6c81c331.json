{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\CpuOptimization.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Cpu, Zap, Thermometer, Activity, Settings, Play, AlertTriangle, CheckCircle, TrendingUp, BarChart3 } from 'lucide-react';\nimport './CpuOptimization.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CpuOptimization = ({\n  isAdmin,\n  systemInfo\n}) => {\n  _s();\n  const [cpuTweaks, setCpuTweaks] = useState({\n    powerPlan: false,\n    coreParking: false,\n    threadScheduling: false,\n    cpuPriority: false,\n    turboBoost: false,\n    cStates: false,\n    prefetcher: false,\n    spectre: false\n  });\n  const [cpuMetrics, setCpuMetrics] = useState({\n    temperature: 45,\n    usage: 12,\n    frequency: 3200,\n    voltage: 1.25,\n    powerDraw: 65\n  });\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n  const cpuOptimizations = [{\n    id: 'powerPlan',\n    title: 'High Performance Power Plan',\n    description: 'Set Windows to High Performance mode for maximum CPU performance',\n    impact: 'High',\n    category: 'Power Management',\n    riskLevel: 'Low',\n    estimatedGain: '+15-25% performance'\n  }, {\n    id: 'coreParking',\n    title: 'Disable CPU Core Parking',\n    description: 'Prevent Windows from parking CPU cores to maintain consistent performance',\n    impact: 'High',\n    category: 'Core Management',\n    riskLevel: 'Low',\n    estimatedGain: '+10-20% in multi-threaded tasks'\n  }, {\n    id: 'threadScheduling',\n    title: 'Optimize Thread Scheduling',\n    description: 'Improve CPU thread distribution and scheduling algorithms',\n    impact: 'Medium',\n    category: 'Scheduling',\n    riskLevel: 'Low',\n    estimatedGain: '*****% in gaming'\n  }, {\n    id: 'cpuPriority',\n    title: 'CPU Priority Optimization',\n    description: 'Set optimal CPU priority for foreground applications',\n    impact: 'Medium',\n    category: 'Priority',\n    riskLevel: 'Low',\n    estimatedGain: '*****% responsiveness'\n  }, {\n    id: 'turboBoost',\n    title: 'Intel Turbo Boost / AMD Precision Boost',\n    description: 'Optimize boost algorithms for maximum single-core performance',\n    impact: 'High',\n    category: 'Boost Technology',\n    riskLevel: 'Medium',\n    estimatedGain: '+20-35% single-core performance'\n  }, {\n    id: 'cStates',\n    title: 'Disable C-States',\n    description: 'Prevent CPU from entering sleep states for consistent performance',\n    impact: 'High',\n    category: 'Power States',\n    riskLevel: 'Medium',\n    estimatedGain: '+10-25% latency reduction'\n  }, {\n    id: 'prefetcher',\n    title: 'Hardware Prefetcher Optimization',\n    description: 'Optimize CPU prefetcher settings for better cache performance',\n    impact: 'Medium',\n    category: 'Cache',\n    riskLevel: 'Medium',\n    estimatedGain: '*****% memory performance'\n  }, {\n    id: 'spectre',\n    title: 'Disable Spectre/Meltdown Mitigations',\n    description: 'Disable security mitigations for maximum performance (security risk)',\n    impact: 'Very High',\n    category: 'Security',\n    riskLevel: 'High',\n    estimatedGain: '+15-30% performance'\n  }];\n  const handleTweakToggle = tweakId => {\n    if (!isAdmin) return;\n    setCpuTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n  const applyOptimizations = async () => {\n    if (!isAdmin) return;\n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n    const enabledTweaks = Object.entries(cpuTweaks).filter(([_, enabled]) => enabled).map(([id, _]) => id);\n    for (let i = 0; i < enabledTweaks.length; i++) {\n      const tweakId = enabledTweaks[i];\n      try {\n        // Simulate applying tweak\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Here you would call the actual optimization functions\n        // await window.electronAPI.applyCpuTweak(tweakId);\n\n        setOptimizationProgress((i + 1) / enabledTweaks.length * 100);\n      } catch (error) {\n        console.error(`Failed to apply ${tweakId}:`, error);\n      }\n    }\n    setIsOptimizing(false);\n\n    // Show success message\n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'CPU Optimization Complete',\n      message: `Successfully applied ${enabledTweaks.length} CPU optimizations!`,\n      buttons: ['OK']\n    });\n  };\n  const getRiskColor = risk => {\n    switch (risk) {\n      case 'Low':\n        return '#00ff88';\n      case 'Medium':\n        return '#ffa502';\n      case 'High':\n        return '#ff4757';\n      default:\n        return '#888';\n    }\n  };\n  const getImpactColor = impact => {\n    switch (impact) {\n      case 'Very High':\n        return '#ff6b35';\n      case 'High':\n        return '#00ff88';\n      case 'Medium':\n        return '#74b9ff';\n      case 'Low':\n        return '#888';\n      default:\n        return '#888';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cpu-optimization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-icon\",\n          children: /*#__PURE__*/_jsxDEV(Cpu, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"CPU Optimization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Advanced processor tweaks and performance optimizations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), (systemInfo === null || systemInfo === void 0 ? void 0 : systemInfo.cpu) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cpu-info-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: systemInfo.cpu.brand\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cpu-specs\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [systemInfo.cpu.cores, \" cores\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [systemInfo.cpu.physicalCores, \" physical\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [systemInfo.cpu.speed, \" GHz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cpu-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Real-time CPU Metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metrics-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon temp\",\n              children: /*#__PURE__*/_jsxDEV(Thermometer, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [cpuMetrics.temperature, \"\\xB0C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Temperature\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon usage\",\n              children: /*#__PURE__*/_jsxDEV(Activity, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [cpuMetrics.usage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Usage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon freq\",\n              children: /*#__PURE__*/_jsxDEV(BarChart3, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [cpuMetrics.frequency, \" MHz\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Frequency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-icon power\",\n              children: /*#__PURE__*/_jsxDEV(Zap, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [cpuMetrics.powerDraw, \"W\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Power Draw\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"optimizations-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"CPU Optimizations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"apply-btn\",\n            onClick: applyOptimizations,\n            disabled: !isAdmin || isOptimizing || Object.values(cpuTweaks).every(v => !v),\n            children: isOptimizing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                size: 16,\n                className: \"spinning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), \"Optimizing... \", Math.round(optimizationProgress), \"%\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), \"Apply Selected Tweaks\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tweaks-grid\",\n          children: cpuOptimizations.map(tweak => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `tweak-card ${cpuTweaks[tweak.id] ? 'enabled' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tweak-title\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: tweak.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tweak-badges\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"impact-badge\",\n                    style: {\n                      backgroundColor: getImpactColor(tweak.impact)\n                    },\n                    children: [tweak.impact, \" Impact\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"risk-badge\",\n                    style: {\n                      backgroundColor: getRiskColor(tweak.riskLevel)\n                    },\n                    children: [tweak.riskLevel, \" Risk\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: cpuTweaks[tweak.id],\n                  onChange: () => handleTweakToggle(tweak.id),\n                  disabled: !isAdmin\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"tweak-description\",\n              children: tweak.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-label\",\n                  children: \"Category:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-value\",\n                  children: tweak.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-label\",\n                  children: \"Estimated Gain:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"detail-value gain\",\n                  children: tweak.estimatedGain\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, tweak.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-required\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Administrator privileges required for CPU optimizations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(CpuOptimization, \"rWV9WBa1/xpYSYdUd2YQNynPM3E=\");\n_c = CpuOptimization;\nexport default CpuOptimization;\nvar _c;\n$RefreshReg$(_c, \"CpuOptimization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Cpu", "Zap", "Thermometer", "Activity", "Settings", "Play", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "TrendingUp", "BarChart3", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CpuOptimization", "isAdmin", "systemInfo", "_s", "cpuTweaks", "setCpuTweaks", "powerPlan", "coreParking", "threadScheduling", "cpuPriority", "turboBoost", "cStates", "prefetcher", "spectre", "cpuMetrics", "setCpuMetrics", "temperature", "usage", "frequency", "voltage", "powerDraw", "isOptimizing", "setIsOptimizing", "optimizationProgress", "setOptimizationProgress", "cpuOptimizations", "id", "title", "description", "impact", "category", "riskLevel", "estimatedGain", "handleTweakToggle", "tweakId", "prev", "applyOptimizations", "enabledTweaks", "Object", "entries", "filter", "_", "enabled", "map", "i", "length", "Promise", "resolve", "setTimeout", "error", "console", "window", "electronAPI", "showMessageBox", "type", "message", "buttons", "getRiskColor", "risk", "getImpactColor", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cpu", "brand", "cores", "physicalCores", "speed", "onClick", "disabled", "values", "every", "v", "Math", "round", "tweak", "style", "backgroundColor", "checked", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/CpuOptimization.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Cpu, \n  Zap, \n  Thermometer, \n  Activity, \n  Settings, \n  Play, \n  AlertTriangle,\n  CheckCircle,\n  TrendingUp,\n  BarChart3\n} from 'lucide-react';\nimport './CpuOptimization.css';\n\nconst CpuOptimization = ({ isAdmin, systemInfo }) => {\n  const [cpuTweaks, setCpuTweaks] = useState({\n    powerPlan: false,\n    coreParking: false,\n    threadScheduling: false,\n    cpuPriority: false,\n    turboBoost: false,\n    cStates: false,\n    prefetcher: false,\n    spectre: false\n  });\n\n  const [cpuMetrics, setCpuMetrics] = useState({\n    temperature: 45,\n    usage: 12,\n    frequency: 3200,\n    voltage: 1.25,\n    powerDraw: 65\n  });\n\n  const [isOptimizing, setIsOptimizing] = useState(false);\n  const [optimizationProgress, setOptimizationProgress] = useState(0);\n\n  const cpuOptimizations = [\n    {\n      id: 'powerPlan',\n      title: 'High Performance Power Plan',\n      description: 'Set Windows to High Performance mode for maximum CPU performance',\n      impact: 'High',\n      category: 'Power Management',\n      riskLevel: 'Low',\n      estimatedGain: '+15-25% performance'\n    },\n    {\n      id: 'coreParking',\n      title: 'Disable CPU Core Parking',\n      description: 'Prevent Windows from parking CPU cores to maintain consistent performance',\n      impact: 'High',\n      category: 'Core Management',\n      riskLevel: 'Low',\n      estimatedGain: '+10-20% in multi-threaded tasks'\n    },\n    {\n      id: 'threadScheduling',\n      title: 'Optimize Thread Scheduling',\n      description: 'Improve CPU thread distribution and scheduling algorithms',\n      impact: 'Medium',\n      category: 'Scheduling',\n      riskLevel: 'Low',\n      estimatedGain: '*****% in gaming'\n    },\n    {\n      id: 'cpuPriority',\n      title: 'CPU Priority Optimization',\n      description: 'Set optimal CPU priority for foreground applications',\n      impact: 'Medium',\n      category: 'Priority',\n      riskLevel: 'Low',\n      estimatedGain: '*****% responsiveness'\n    },\n    {\n      id: 'turboBoost',\n      title: 'Intel Turbo Boost / AMD Precision Boost',\n      description: 'Optimize boost algorithms for maximum single-core performance',\n      impact: 'High',\n      category: 'Boost Technology',\n      riskLevel: 'Medium',\n      estimatedGain: '+20-35% single-core performance'\n    },\n    {\n      id: 'cStates',\n      title: 'Disable C-States',\n      description: 'Prevent CPU from entering sleep states for consistent performance',\n      impact: 'High',\n      category: 'Power States',\n      riskLevel: 'Medium',\n      estimatedGain: '+10-25% latency reduction'\n    },\n    {\n      id: 'prefetcher',\n      title: 'Hardware Prefetcher Optimization',\n      description: 'Optimize CPU prefetcher settings for better cache performance',\n      impact: 'Medium',\n      category: 'Cache',\n      riskLevel: 'Medium',\n      estimatedGain: '*****% memory performance'\n    },\n    {\n      id: 'spectre',\n      title: 'Disable Spectre/Meltdown Mitigations',\n      description: 'Disable security mitigations for maximum performance (security risk)',\n      impact: 'Very High',\n      category: 'Security',\n      riskLevel: 'High',\n      estimatedGain: '+15-30% performance'\n    }\n  ];\n\n  const handleTweakToggle = (tweakId) => {\n    if (!isAdmin) return;\n    \n    setCpuTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n\n  const applyOptimizations = async () => {\n    if (!isAdmin) return;\n    \n    setIsOptimizing(true);\n    setOptimizationProgress(0);\n\n    const enabledTweaks = Object.entries(cpuTweaks)\n      .filter(([_, enabled]) => enabled)\n      .map(([id, _]) => id);\n\n    for (let i = 0; i < enabledTweaks.length; i++) {\n      const tweakId = enabledTweaks[i];\n      \n      try {\n        // Simulate applying tweak\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        \n        // Here you would call the actual optimization functions\n        // await window.electronAPI.applyCpuTweak(tweakId);\n        \n        setOptimizationProgress(((i + 1) / enabledTweaks.length) * 100);\n      } catch (error) {\n        console.error(`Failed to apply ${tweakId}:`, error);\n      }\n    }\n\n    setIsOptimizing(false);\n    \n    // Show success message\n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'CPU Optimization Complete',\n      message: `Successfully applied ${enabledTweaks.length} CPU optimizations!`,\n      buttons: ['OK']\n    });\n  };\n\n  const getRiskColor = (risk) => {\n    switch (risk) {\n      case 'Low': return '#00ff88';\n      case 'Medium': return '#ffa502';\n      case 'High': return '#ff4757';\n      default: return '#888';\n    }\n  };\n\n  const getImpactColor = (impact) => {\n    switch (impact) {\n      case 'Very High': return '#ff6b35';\n      case 'High': return '#00ff88';\n      case 'Medium': return '#74b9ff';\n      case 'Low': return '#888';\n      default: return '#888';\n    }\n  };\n\n  return (\n    <div className=\"cpu-optimization\">\n      <div className=\"page-header\">\n        <div className=\"header-content\">\n          <div className=\"header-icon\">\n            <Cpu size={32} />\n          </div>\n          <div className=\"header-text\">\n            <h1>CPU Optimization</h1>\n            <p>Advanced processor tweaks and performance optimizations</p>\n          </div>\n        </div>\n        \n        {systemInfo?.cpu && (\n          <div className=\"cpu-info-card\">\n            <h3>{systemInfo.cpu.brand}</h3>\n            <div className=\"cpu-specs\">\n              <span>{systemInfo.cpu.cores} cores</span>\n              <span>•</span>\n              <span>{systemInfo.cpu.physicalCores} physical</span>\n              <span>•</span>\n              <span>{systemInfo.cpu.speed} GHz</span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      <div className=\"cpu-content\">\n        {/* CPU Metrics */}\n        <div className=\"metrics-section\">\n          <h2>Real-time CPU Metrics</h2>\n          <div className=\"metrics-grid\">\n            <div className=\"metric-card\">\n              <div className=\"metric-icon temp\">\n                <Thermometer size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{cpuMetrics.temperature}°C</div>\n                <div className=\"metric-label\">Temperature</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon usage\">\n                <Activity size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{cpuMetrics.usage}%</div>\n                <div className=\"metric-label\">Usage</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon freq\">\n                <BarChart3 size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{cpuMetrics.frequency} MHz</div>\n                <div className=\"metric-label\">Frequency</div>\n              </div>\n            </div>\n            \n            <div className=\"metric-card\">\n              <div className=\"metric-icon power\">\n                <Zap size={20} />\n              </div>\n              <div className=\"metric-content\">\n                <div className=\"metric-value\">{cpuMetrics.powerDraw}W</div>\n                <div className=\"metric-label\">Power Draw</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Optimization Controls */}\n        <div className=\"optimizations-section\">\n          <div className=\"section-header\">\n            <h2>CPU Optimizations</h2>\n            <button \n              className=\"apply-btn\"\n              onClick={applyOptimizations}\n              disabled={!isAdmin || isOptimizing || Object.values(cpuTweaks).every(v => !v)}\n            >\n              {isOptimizing ? (\n                <>\n                  <Activity size={16} className=\"spinning\" />\n                  Optimizing... {Math.round(optimizationProgress)}%\n                </>\n              ) : (\n                <>\n                  <Play size={16} />\n                  Apply Selected Tweaks\n                </>\n              )}\n            </button>\n          </div>\n\n          <div className=\"tweaks-grid\">\n            {cpuOptimizations.map((tweak) => (\n              <div key={tweak.id} className={`tweak-card ${cpuTweaks[tweak.id] ? 'enabled' : ''}`}>\n                <div className=\"tweak-header\">\n                  <div className=\"tweak-title\">\n                    <h3>{tweak.title}</h3>\n                    <div className=\"tweak-badges\">\n                      <span \n                        className=\"impact-badge\"\n                        style={{ backgroundColor: getImpactColor(tweak.impact) }}\n                      >\n                        {tweak.impact} Impact\n                      </span>\n                      <span \n                        className=\"risk-badge\"\n                        style={{ backgroundColor: getRiskColor(tweak.riskLevel) }}\n                      >\n                        {tweak.riskLevel} Risk\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <label className=\"toggle\">\n                    <input\n                      type=\"checkbox\"\n                      checked={cpuTweaks[tweak.id]}\n                      onChange={() => handleTweakToggle(tweak.id)}\n                      disabled={!isAdmin}\n                    />\n                    <span className=\"toggle-slider\"></span>\n                  </label>\n                </div>\n                \n                <p className=\"tweak-description\">{tweak.description}</p>\n                \n                <div className=\"tweak-details\">\n                  <div className=\"detail-item\">\n                    <span className=\"detail-label\">Category:</span>\n                    <span className=\"detail-value\">{tweak.category}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <span className=\"detail-label\">Estimated Gain:</span>\n                    <span className=\"detail-value gain\">{tweak.estimatedGain}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {!isAdmin && (\n        <div className=\"admin-required\">\n          <AlertTriangle size={20} />\n          <span>Administrator privileges required for CPU optimizations</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CpuOptimization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,GAAG,EACHC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,IAAI,EACJC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,SAAS,QACJ,cAAc;AACrB,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC;IACzCsB,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAE,KAAK;IACjBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC;IAC3CgC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAEnE,MAAMyC,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,kEAAkE;IAC/EC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,2EAA2E;IACxFC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,2DAA2D;IACxEC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,sDAAsD;IACnEC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,yCAAyC;IAChDC,WAAW,EAAE,+DAA+D;IAC5EC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,kBAAkB;IAC5BC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,mEAAmE;IAChFC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,kCAAkC;IACzCC,WAAW,EAAE,+DAA+D;IAC5EC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACEN,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,sCAAsC;IAC7CC,WAAW,EAAE,sEAAsE;IACnFC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE;EACjB,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAIC,OAAO,IAAK;IACrC,IAAI,CAACjC,OAAO,EAAE;IAEdI,YAAY,CAAC8B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACnC,OAAO,EAAE;IAEdqB,eAAe,CAAC,IAAI,CAAC;IACrBE,uBAAuB,CAAC,CAAC,CAAC;IAE1B,MAAMa,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACnC,SAAS,CAAC,CAC5CoC,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,OAAO,CAAC,KAAKA,OAAO,CAAC,CACjCC,GAAG,CAAC,CAAC,CAACjB,EAAE,EAAEe,CAAC,CAAC,KAAKf,EAAE,CAAC;IAEvB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,aAAa,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMV,OAAO,GAAGG,aAAa,CAACO,CAAC,CAAC;MAEhC,IAAI;QACF;QACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA;;QAEAvB,uBAAuB,CAAE,CAACoB,CAAC,GAAG,CAAC,IAAIP,aAAa,CAACQ,MAAM,GAAI,GAAG,CAAC;MACjE,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmBf,OAAO,GAAG,EAAEe,KAAK,CAAC;MACrD;IACF;IAEA3B,eAAe,CAAC,KAAK,CAAC;;IAEtB;IACA,MAAM6B,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC;MACtCC,IAAI,EAAE,MAAM;MACZ3B,KAAK,EAAE,2BAA2B;MAClC4B,OAAO,EAAE,wBAAwBlB,aAAa,CAACQ,MAAM,qBAAqB;MAC1EW,OAAO,EAAE,CAAC,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMC,cAAc,GAAI9B,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,MAAM;MACzB;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,oBACEhC,OAAA;IAAK+D,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BhE,OAAA;MAAK+D,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhE,OAAA;QAAK+D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhE,OAAA;UAAK+D,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhE,OAAA,CAACX,GAAG;YAAC4E,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNrE,OAAA;UAAK+D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhE,OAAA;YAAAgE,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBrE,OAAA;YAAAgE,QAAA,EAAG;UAAuD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL,CAAAhE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiE,GAAG,kBACdtE,OAAA;QAAK+D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BhE,OAAA;UAAAgE,QAAA,EAAK3D,UAAU,CAACiE,GAAG,CAACC;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/BrE,OAAA;UAAK+D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhE,OAAA;YAAAgE,QAAA,GAAO3D,UAAU,CAACiE,GAAG,CAACE,KAAK,EAAC,QAAM;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCrE,OAAA;YAAAgE,QAAA,EAAM;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdrE,OAAA;YAAAgE,QAAA,GAAO3D,UAAU,CAACiE,GAAG,CAACG,aAAa,EAAC,WAAS;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDrE,OAAA;YAAAgE,QAAA,EAAM;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdrE,OAAA;YAAAgE,QAAA,GAAO3D,UAAU,CAACiE,GAAG,CAACI,KAAK,EAAC,MAAI;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrE,OAAA;MAAK+D,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1BhE,OAAA;QAAK+D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BhE,OAAA;UAAAgE,QAAA,EAAI;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BrE,OAAA;UAAK+D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhE,OAAA;cAAK+D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BhE,OAAA,CAACT,WAAW;gBAAC0E,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACNrE,OAAA;cAAK+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/C,UAAU,CAACE,WAAW,EAAC,OAAE;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DrE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhE,OAAA;cAAK+D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChChE,OAAA,CAACR,QAAQ;gBAACyE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNrE,OAAA;cAAK+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/C,UAAU,CAACG,KAAK,EAAC,GAAC;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDrE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhE,OAAA;cAAK+D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BhE,OAAA,CAACF,SAAS;gBAACmE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNrE,OAAA;cAAK+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/C,UAAU,CAACI,SAAS,EAAC,MAAI;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DrE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA;YAAK+D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhE,OAAA;cAAK+D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChChE,OAAA,CAACV,GAAG;gBAAC2E,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNrE,OAAA;cAAK+D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE/C,UAAU,CAACM,SAAS,EAAC,GAAC;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DrE,OAAA;gBAAK+D,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAK+D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpChE,OAAA;UAAK+D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhE,OAAA;YAAAgE,QAAA,EAAI;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BrE,OAAA;YACE+D,SAAS,EAAC,WAAW;YACrBY,OAAO,EAAEpC,kBAAmB;YAC5BqC,QAAQ,EAAE,CAACxE,OAAO,IAAIoB,YAAY,IAAIiB,MAAM,CAACoC,MAAM,CAACtE,SAAS,CAAC,CAACuE,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAAE;YAAAf,QAAA,EAE7ExC,YAAY,gBACXxB,OAAA,CAAAE,SAAA;cAAA8D,QAAA,gBACEhE,OAAA,CAACR,QAAQ;gBAACyE,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAC7B,EAACW,IAAI,CAACC,KAAK,CAACvD,oBAAoB,CAAC,EAAC,GAClD;YAAA,eAAE,CAAC,gBAEH1B,OAAA,CAAAE,SAAA;cAAA8D,QAAA,gBACEhE,OAAA,CAACN,IAAI;gBAACuE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEpB;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrE,OAAA;UAAK+D,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBpC,gBAAgB,CAACkB,GAAG,CAAEoC,KAAK,iBAC1BlF,OAAA;YAAoB+D,SAAS,EAAE,cAAcxD,SAAS,CAAC2E,KAAK,CAACrD,EAAE,CAAC,GAAG,SAAS,GAAG,EAAE,EAAG;YAAAmC,QAAA,gBAClFhE,OAAA;cAAK+D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhE,OAAA;gBAAK+D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhE,OAAA;kBAAAgE,QAAA,EAAKkB,KAAK,CAACpD;gBAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtBrE,OAAA;kBAAK+D,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BhE,OAAA;oBACE+D,SAAS,EAAC,cAAc;oBACxBoB,KAAK,EAAE;sBAAEC,eAAe,EAAEtB,cAAc,CAACoB,KAAK,CAAClD,MAAM;oBAAE,CAAE;oBAAAgC,QAAA,GAExDkB,KAAK,CAAClD,MAAM,EAAC,SAChB;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPrE,OAAA;oBACE+D,SAAS,EAAC,YAAY;oBACtBoB,KAAK,EAAE;sBAAEC,eAAe,EAAExB,YAAY,CAACsB,KAAK,CAAChD,SAAS;oBAAE,CAAE;oBAAA8B,QAAA,GAEzDkB,KAAK,CAAChD,SAAS,EAAC,OACnB;kBAAA;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrE,OAAA;gBAAO+D,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACvBhE,OAAA;kBACEyD,IAAI,EAAC,UAAU;kBACf4B,OAAO,EAAE9E,SAAS,CAAC2E,KAAK,CAACrD,EAAE,CAAE;kBAC7ByD,QAAQ,EAAEA,CAAA,KAAMlD,iBAAiB,CAAC8C,KAAK,CAACrD,EAAE,CAAE;kBAC5C+C,QAAQ,EAAE,CAACxE;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFrE,OAAA;kBAAM+D,SAAS,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENrE,OAAA;cAAG+D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEkB,KAAK,CAACnD;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExDrE,OAAA;cAAK+D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BhE,OAAA;gBAAK+D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhE,OAAA;kBAAM+D,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CrE,OAAA;kBAAM+D,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEkB,KAAK,CAACjD;gBAAQ;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNrE,OAAA;gBAAK+D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhE,OAAA;kBAAM+D,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDrE,OAAA;kBAAM+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAEkB,KAAK,CAAC/C;gBAAa;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA1CEa,KAAK,CAACrD,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2Cb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACjE,OAAO,iBACPJ,OAAA;MAAK+D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BhE,OAAA,CAACL,aAAa;QAACsE,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BrE,OAAA;QAAAgE,QAAA,EAAM;MAAuD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/D,EAAA,CA/TIH,eAAe;AAAAoF,EAAA,GAAfpF,eAAe;AAiUrB,eAAeA,eAAe;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}