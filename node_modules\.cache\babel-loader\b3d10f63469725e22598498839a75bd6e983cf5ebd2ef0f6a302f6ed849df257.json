{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Shapes = createLucideIcon(\"Shapes\", [[\"path\", {\n  d: \"M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z\",\n  key: \"1bo67w\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"14\",\n  width: \"7\",\n  height: \"7\",\n  rx: \"1\",\n  key: \"1bkyp8\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"3.5\",\n  key: \"w3z12y\"\n}]]);\nexport { Shapes as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key", "x", "y", "width", "height", "rx", "cx", "cy", "r"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\shapes.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shapes\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOC4zIDEwYS43LjcgMCAwIDEtLjYyNi0xLjA3OUwxMS40IDNhLjcuNyAwIDAgMSAxLjE5OC0uMDQzTDE2LjMgOC45YS43LjcgMCAwIDEtLjU3MiAxLjFaIiAvPgogIDxyZWN0IHg9IjMiIHk9IjE0IiB3aWR0aD0iNyIgaGVpZ2h0PSI3IiByeD0iMSIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTcuNSIgcj0iMy41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/shapes\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shapes = createLucideIcon('Shapes', [\n  [\n    'path',\n    {\n      d: 'M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z',\n      key: '1bo67w',\n    },\n  ],\n  ['rect', { x: '3', y: '14', width: '7', height: '7', rx: '1', key: '1bkyp8' }],\n  ['circle', { cx: '17.5', cy: '17.5', r: '3.5', key: 'w3z12y' }],\n]);\n\nexport default Shapes;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAMC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAU;EAAEM,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOR,GAAK;AAAA,CAAU,EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}