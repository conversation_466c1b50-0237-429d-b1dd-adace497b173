.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
  border-right: 1px solid rgba(0, 255, 136, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, #00ff88 50%, transparent 100%);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.sidebar-logo svg {
  color: #00ff88;
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-main {
  font-size: 16px;
  font-weight: 800;
  color: #00ff88;
  letter-spacing: 1px;
  line-height: 1;
}

.logo-sub {
  font-size: 10px;
  font-weight: 600;
  color: #666;
  letter-spacing: 2px;
  margin-top: 2px;
}

.admin-status {
  display: flex;
  justify-content: center;
}

.admin-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-active {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.admin-inactive {
  background: rgba(255, 165, 2, 0.2);
  color: #ffa502;
  border: 1px solid rgba(255, 165, 2, 0.3);
}

.sidebar-nav {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #888;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #00ff88;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-item:hover:not(.disabled) {
  background: rgba(0, 255, 136, 0.1);
  color: #fff;
  transform: translateX(4px);
}

.nav-item.active {
  background: rgba(0, 255, 136, 0.15);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-item.advanced {
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.nav-item.advanced:hover:not(.disabled) {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.5);
}

.nav-icon {
  position: relative;
  flex-shrink: 0;
}

.advanced-badge {
  position: absolute;
  top: -6px;
  right: -8px;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #000;
  font-size: 8px;
  font-weight: 700;
  padding: 2px 4px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.nav-content {
  flex: 1;
  min-width: 0;
}

.nav-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-description {
  font-size: 11px;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lock-icon {
  flex-shrink: 0;
  opacity: 0.5;
}

.sidebar-footer {
  padding: 20px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(0, 255, 136, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 136, 0.2);
}

.stat-item svg {
  color: #00ff88;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-label {
  font-size: 11px;
  color: #888;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 13px;
  font-weight: 700;
  color: #00ff88;
}

.version-info {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.version {
  font-size: 12px;
  font-weight: 600;
  color: #00ff88;
  margin-bottom: 2px;
}

.build {
  font-size: 10px;
  color: #666;
}
