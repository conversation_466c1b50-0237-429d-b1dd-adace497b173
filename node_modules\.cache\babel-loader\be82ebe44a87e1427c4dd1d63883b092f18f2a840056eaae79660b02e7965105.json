{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BrainCog = createLucideIcon(\"BrainCog\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"path\", {\n  d: \"M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08A2.5 2.5 0 0 0 12 19.5a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 12 4.5\",\n  key: \"1f4le0\"\n}], [\"path\", {\n  d: \"m15.7 10.4-.9.4\",\n  key: \"ayzo6p\"\n}], [\"path\", {\n  d: \"m9.2 13.2-.9.4\",\n  key: \"1uzb3g\"\n}], [\"path\", {\n  d: \"m13.6 15.7-.4-.9\",\n  key: \"11ifqf\"\n}], [\"path\", {\n  d: \"m10.8 9.2-.4-.9\",\n  key: \"1pmk2v\"\n}], [\"path\", {\n  d: \"m15.7 13.5-.9-.4\",\n  key: \"7ng02m\"\n}], [\"path\", {\n  d: \"m9.2 10.9-.9-.4\",\n  key: \"1x66zd\"\n}], [\"path\", {\n  d: \"m10.5 15.7.4-.9\",\n  key: \"3js94g\"\n}], [\"path\", {\n  d: \"m13.1 9.2.4-.9\",\n  key: \"18n7mc\"\n}]]);\nexport { BrainCog as default };", "map": {"version": 3, "names": ["BrainCog", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\brain-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BrainCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgogIDxwYXRoIGQ9Ik0xMiA0LjVhMi41IDIuNSAwIDAgMC00Ljk2LS40NiAyLjUgMi41IDAgMCAwLTEuOTggMyAyLjUgMi41IDAgMCAwLTEuMzIgNC4yNCAzIDMgMCAwIDAgLjM0IDUuNTggMi41IDIuNSAwIDAgMCAyLjk2IDMuMDhBMi41IDIuNSAwIDAgMCAxMiAxOS41YTIuNSAyLjUgMCAwIDAgNC45Ni40NCAyLjUgMi41IDAgMCAwIDIuOTYtMy4wOCAzIDMgMCAwIDAgLjM0LTUuNTggMi41IDIuNSAwIDAgMC0xLjMyLTQuMjQgMi41IDIuNSAwIDAgMC0xLjk4LTNBMi41IDIuNSAwIDAgMCAxMiA0LjUiIC8+CiAgPHBhdGggZD0ibTE1LjcgMTAuNC0uOS40IiAvPgogIDxwYXRoIGQ9Im05LjIgMTMuMi0uOS40IiAvPgogIDxwYXRoIGQ9Im0xMy42IDE1LjctLjQtLjkiIC8+CiAgPHBhdGggZD0ibTEwLjggOS4yLS40LS45IiAvPgogIDxwYXRoIGQ9Im0xNS43IDEzLjUtLjktLjQiIC8+CiAgPHBhdGggZD0ibTkuMiAxMC45LS45LS40IiAvPgogIDxwYXRoIGQ9Im0xMC41IDE1LjcuNC0uOSIgLz4KICA8cGF0aCBkPSJtMTMuMSA5LjIuNC0uOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/brain-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BrainCog = createLucideIcon('BrainCog', [\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  [\n    'path',\n    {\n      d: 'M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08A2.5 2.5 0 0 0 12 19.5a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 12 4.5',\n      key: '1f4le0',\n    },\n  ],\n  ['path', { d: 'm15.7 10.4-.9.4', key: 'ayzo6p' }],\n  ['path', { d: 'm9.2 13.2-.9.4', key: '1uzb3g' }],\n  ['path', { d: 'm13.6 15.7-.4-.9', key: '11ifqf' }],\n  ['path', { d: 'm10.8 9.2-.4-.9', key: '1pmk2v' }],\n  ['path', { d: 'm15.7 13.5-.9-.4', key: '7ng02m' }],\n  ['path', { d: 'm9.2 10.9-.9-.4', key: '1x66zd' }],\n  ['path', { d: 'm10.5 15.7.4-.9', key: '3js94g' }],\n  ['path', { d: 'm13.1 9.2.4-.9', key: '18n7mc' }],\n]);\n\nexport default BrainCog;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}