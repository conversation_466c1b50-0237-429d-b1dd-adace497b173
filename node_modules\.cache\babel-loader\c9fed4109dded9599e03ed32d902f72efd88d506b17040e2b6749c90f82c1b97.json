{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Heading4 = createLucideIcon(\"Heading4\", [[\"path\", {\n  d: \"M4 12h8\",\n  key: \"17cfdx\"\n}], [\"path\", {\n  d: \"M4 18V6\",\n  key: \"1rz3zl\"\n}], [\"path\", {\n  d: \"M12 18V6\",\n  key: \"zqpxq5\"\n}], [\"path\", {\n  d: \"M17 10v4h4\",\n  key: \"13sv97\"\n}], [\"path\", {\n  d: \"M21 10v8\",\n  key: \"1kdml4\"\n}]]);\nexport { Heading4 as default };", "map": {"version": 3, "names": ["Heading4", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\heading-4.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Heading4\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMmg4IiAvPgogIDxwYXRoIGQ9Ik00IDE4VjYiIC8+CiAgPHBhdGggZD0iTTEyIDE4VjYiIC8+CiAgPHBhdGggZD0iTTE3IDEwdjRoNCIgLz4KICA8cGF0aCBkPSJNMjEgMTB2OCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/heading-4\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heading4 = createLucideIcon('Heading4', [\n  ['path', { d: 'M4 12h8', key: '17cfdx' }],\n  ['path', { d: 'M4 18V6', key: '1rz3zl' }],\n  ['path', { d: 'M12 18V6', key: 'zqpxq5' }],\n  ['path', { d: 'M17 10v4h4', key: '13sv97' }],\n  ['path', { d: 'M21 10v8', key: '1kdml4' }],\n]);\n\nexport default Heading4;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}