{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Biohazard = createLucideIcon(\"Biohazard\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"11.9\",\n  r: \"2\",\n  key: \"e8h31w\"\n}], [\"path\", {\n  d: \"M6.7 3.4c-.9 2.5 0 5.2 2.2 6.7C6.5 9 3.7 9.6 2 11.6\",\n  key: \"17bolr\"\n}], [\"path\", {\n  d: \"m8.9 10.1 1.4.8\",\n  key: \"15ezny\"\n}], [\"path\", {\n  d: \"M17.3 3.4c.9 2.5 0 5.2-2.2 6.7 2.4-1.2 5.2-.6 6.9 1.5\",\n  key: \"wtwa5u\"\n}], [\"path\", {\n  d: \"m15.1 10.1-1.4.8\",\n  key: \"1r0b28\"\n}], [\"path\", {\n  d: \"M16.7 20.8c-2.6-.4-4.6-2.6-4.7-5.3-.2 2.6-2.1 4.8-4.7 5.2\",\n  key: \"m7qszh\"\n}], [\"path\", {\n  d: \"M12 13.9v1.6\",\n  key: \"zfyyim\"\n}], [\"path\", {\n  d: \"M13.5 5.4c-1-.2-2-.2-3 0\",\n  key: \"1bi9q0\"\n}], [\"path\", {\n  d: \"M17 16.4c.7-.7 1.2-1.6 1.5-2.5\",\n  key: \"1rhjqw\"\n}], [\"path\", {\n  d: \"M5.5 13.9c.3.9.8 1.8 1.5 2.5\",\n  key: \"8gsud3\"\n}]]);\nexport { Biohazard as default };", "map": {"version": 3, "names": ["Biohazard", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\biohazard.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Biohazard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjExLjkiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTYuNyAzLjRjLS45IDIuNSAwIDUuMiAyLjIgNi43QzYuNSA5IDMuNyA5LjYgMiAxMS42IiAvPgogIDxwYXRoIGQ9Im04LjkgMTAuMSAxLjQuOCIgLz4KICA8cGF0aCBkPSJNMTcuMyAzLjRjLjkgMi41IDAgNS4yLTIuMiA2LjcgMi40LTEuMiA1LjItLjYgNi45IDEuNSIgLz4KICA8cGF0aCBkPSJtMTUuMSAxMC4xLTEuNC44IiAvPgogIDxwYXRoIGQ9Ik0xNi43IDIwLjhjLTIuNi0uNC00LjYtMi42LTQuNy01LjMtLjIgMi42LTIuMSA0LjgtNC43IDUuMiIgLz4KICA8cGF0aCBkPSJNMTIgMTMuOXYxLjYiIC8+CiAgPHBhdGggZD0iTTEzLjUgNS40Yy0xLS4yLTItLjItMyAwIiAvPgogIDxwYXRoIGQ9Ik0xNyAxNi40Yy43LS43IDEuMi0xLjYgMS41LTIuNSIgLz4KICA8cGF0aCBkPSJNNS41IDEzLjljLjMuOS44IDEuOCAxLjUgMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/biohazard\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Biohazard = createLucideIcon('Biohazard', [\n  ['circle', { cx: '12', cy: '11.9', r: '2', key: 'e8h31w' }],\n  ['path', { d: 'M6.7 3.4c-.9 2.5 0 5.2 2.2 6.7C6.5 9 3.7 9.6 2 11.6', key: '17bolr' }],\n  ['path', { d: 'm8.9 10.1 1.4.8', key: '15ezny' }],\n  ['path', { d: 'M17.3 3.4c.9 2.5 0 5.2-2.2 6.7 2.4-1.2 5.2-.6 6.9 1.5', key: 'wtwa5u' }],\n  ['path', { d: 'm15.1 10.1-1.4.8', key: '1r0b28' }],\n  ['path', { d: 'M16.7 20.8c-2.6-.4-4.6-2.6-4.7-5.3-.2 2.6-2.1 4.8-4.7 5.2', key: 'm7qszh' }],\n  ['path', { d: 'M12 13.9v1.6', key: 'zfyyim' }],\n  ['path', { d: 'M13.5 5.4c-1-.2-2-.2-3 0', key: '1bi9q0' }],\n  ['path', { d: 'M17 16.4c.7-.7 1.2-1.6 1.5-2.5', key: '1rhjqw' }],\n  ['path', { d: 'M5.5 13.9c.3.9.8 1.8 1.5 2.5', key: '8gsud3' }],\n]);\n\nexport default Biohazard;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAQC,CAAG;EAAKC,GAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,qDAAuD;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpF,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,uDAAyD;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtF,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}