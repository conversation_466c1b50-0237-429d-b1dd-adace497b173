{"format": 1, "restore": {"C:\\rodeypremium\\RodeyPremiumTweaker.csproj": {}}, "projects": {"C:\\rodeypremium\\RodeyPremiumTweaker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\rodeypremium\\RodeyPremiumTweaker.csproj", "projectName": "RodeyPremiumTweaker", "projectPath": "C:\\rodeypremium\\RodeyPremiumTweaker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\rodeypremium\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[7.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[6.0.31, 6.0.31]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.410\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}