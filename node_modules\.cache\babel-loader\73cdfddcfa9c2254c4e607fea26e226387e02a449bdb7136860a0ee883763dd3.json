{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\BiosTweaks.js\";\nimport React from 'react';\nimport { Settings2, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BiosTweaks = ({\n  isAdmin\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '16px',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#fff'\n        },\n        children: /*#__PURE__*/_jsxDEV(Settings2, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            color: '#ff6b35',\n            fontSize: '28px',\n            fontWeight: '700',\n            margin: '0 0 4px 0'\n          },\n          children: \"BIOS/UEFI Tweaks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#888',\n            fontSize: '14px',\n            margin: '0'\n          },\n          children: \"Hardware-level optimizations for maximum performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(26, 26, 26, 0.8)',\n        border: '1px solid rgba(255, 107, 53, 0.2)',\n        borderRadius: '12px',\n        padding: '24px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 107, 53, 0.1)',\n          border: '1px solid rgba(255, 107, 53, 0.3)',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#ff6b35',\n            margin: '0 0 8px 0'\n          },\n          children: \"\\u26A0\\uFE0F ADVANCED FEATURE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#888',\n            margin: '0',\n            fontSize: '14px'\n          },\n          children: \"BIOS tweaks require extreme caution and can potentially damage your system if applied incorrectly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#fff',\n          marginBottom: '16px'\n        },\n        children: \"BIOS Optimization Coming Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#888',\n          marginBottom: '20px'\n        },\n        children: \"Hardware-level tweaks, memory timings, and UEFI optimizations will be available here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        background: 'rgba(255, 165, 2, 0.1)',\n        border: '1px solid rgba(255, 165, 2, 0.3)',\n        borderRadius: '8px',\n        padding: '12px 16px',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px',\n        color: '#ffa502'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Administrator privileges required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = BiosTweaks;\nexport default BiosTweaks;\nvar _c;\n$RefreshReg$(_c, \"BiosTweaks\");", "map": {"version": 3, "names": ["React", "Settings2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "BiosTweaks", "isAdmin", "style", "padding", "children", "display", "alignItems", "gap", "marginBottom", "width", "height", "background", "borderRadius", "justifyContent", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "margin", "border", "textAlign", "position", "bottom", "right", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/BiosTweaks.js"], "sourcesContent": ["import React from 'react';\nimport { Settings2, AlertTriangle } from 'lucide-react';\n\nconst BiosTweaks = ({ isAdmin }) => {\n  return (\n    <div style={{ padding: '20px' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>\n        <div style={{ \n          width: '48px', \n          height: '48px', \n          background: 'linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#fff'\n        }}>\n          <Settings2 size={32} />\n        </div>\n        <div>\n          <h1 style={{ color: '#ff6b35', fontSize: '28px', fontWeight: '700', margin: '0 0 4px 0' }}>\n            BIOS/UEFI Tweaks\n          </h1>\n          <p style={{ color: '#888', fontSize: '14px', margin: '0' }}>\n            Hardware-level optimizations for maximum performance\n          </p>\n        </div>\n      </div>\n      \n      <div style={{ \n        background: 'rgba(26, 26, 26, 0.8)',\n        border: '1px solid rgba(255, 107, 53, 0.2)',\n        borderRadius: '12px',\n        padding: '24px',\n        textAlign: 'center'\n      }}>\n        <div style={{ \n          background: 'rgba(255, 107, 53, 0.1)',\n          border: '1px solid rgba(255, 107, 53, 0.3)',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '20px'\n        }}>\n          <h3 style={{ color: '#ff6b35', margin: '0 0 8px 0' }}>⚠️ ADVANCED FEATURE</h3>\n          <p style={{ color: '#888', margin: '0', fontSize: '14px' }}>\n            BIOS tweaks require extreme caution and can potentially damage your system if applied incorrectly.\n          </p>\n        </div>\n        \n        <h2 style={{ color: '#fff', marginBottom: '16px' }}>BIOS Optimization Coming Soon</h2>\n        <p style={{ color: '#888', marginBottom: '20px' }}>\n          Hardware-level tweaks, memory timings, and UEFI optimizations will be available here.\n        </p>\n      </div>\n\n      {!isAdmin && (\n        <div style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          background: 'rgba(255, 165, 2, 0.1)',\n          border: '1px solid rgba(255, 165, 2, 0.3)',\n          borderRadius: '8px',\n          padding: '12px 16px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          color: '#ffa502'\n        }}>\n          <AlertTriangle size={20} />\n          <span>Administrator privileges required</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default BiosTweaks;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAClC,oBACEF,OAAA;IAAKG,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BL,OAAA;MAAKG,KAAK,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE,MAAM;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACvFL,OAAA;QAAKG,KAAK,EAAE;UACVO,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE,mDAAmD;UAC/DC,YAAY,EAAE,MAAM;UACpBP,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBO,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE;QACT,CAAE;QAAAV,QAAA,eACAL,OAAA,CAACH,SAAS;UAACmB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACNpB,OAAA;QAAAK,QAAA,gBACEL,OAAA;UAAIG,KAAK,EAAE;YAAEY,KAAK,EAAE,SAAS;YAAEM,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAY,CAAE;UAAAlB,QAAA,EAAC;QAE3F;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGG,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEM,QAAQ,EAAE,MAAM;YAAEE,MAAM,EAAE;UAAI,CAAE;UAAAlB,QAAA,EAAC;QAE5D;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpB,OAAA;MAAKG,KAAK,EAAE;QACVS,UAAU,EAAE,uBAAuB;QACnCY,MAAM,EAAE,mCAAmC;QAC3CX,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfqB,SAAS,EAAE;MACb,CAAE;MAAApB,QAAA,gBACAL,OAAA;QAAKG,KAAK,EAAE;UACVS,UAAU,EAAE,yBAAyB;UACrCY,MAAM,EAAE,mCAAmC;UAC3CX,YAAY,EAAE,KAAK;UACnBT,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE;QAChB,CAAE;QAAAJ,QAAA,gBACAL,OAAA;UAAIG,KAAK,EAAE;YAAEY,KAAK,EAAE,SAAS;YAAEQ,MAAM,EAAE;UAAY,CAAE;UAAAlB,QAAA,EAAC;QAAmB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EpB,OAAA;UAAGG,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEQ,MAAM,EAAE,GAAG;YAAEF,QAAQ,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAE5D;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpB,OAAA;QAAIG,KAAK,EAAE;UAAEY,KAAK,EAAE,MAAM;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAA6B;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtFpB,OAAA;QAAGG,KAAK,EAAE;UAAEY,KAAK,EAAE,MAAM;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnD;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEL,CAAClB,OAAO,iBACPF,OAAA;MAAKG,KAAK,EAAE;QACVuB,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbhB,UAAU,EAAE,wBAAwB;QACpCY,MAAM,EAAE,kCAAkC;QAC1CX,YAAY,EAAE,KAAK;QACnBT,OAAO,EAAE,WAAW;QACpBE,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVO,KAAK,EAAE;MACT,CAAE;MAAAV,QAAA,gBACAL,OAAA,CAACF,aAAa;QAACkB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BpB,OAAA;QAAAK,QAAA,EAAM;MAAiC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACS,EAAA,GAxEI5B,UAAU;AA0EhB,eAAeA,UAAU;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}