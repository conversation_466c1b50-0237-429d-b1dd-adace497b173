using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RodeyPremiumTweaker.Controls
{
    public partial class SettingsControl : UserControl
    {
        public SettingsControl()
        {
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.BackColor = Color.FromArgb(18, 18, 18);
            this.Size = new Size(800, 900);
            this.AutoScroll = true;

            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "⚙️ SETTINGS",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(128, 128, 128),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var descLabel = new Label
            {
                Text = "Application settings and configuration options",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 60)
            };

            // Settings Panel
            var settingsPanel = new Panel
            {
                BackColor = Color.FromArgb(26, 26, 26),
                Location = new Point(20, 100),
                Size = new Size(760, 400),
                BorderStyle = BorderStyle.FixedSingle
            };

            var settingsLabel = new Label
            {
                Text = "⚙️ APPLICATION SETTINGS",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(128, 128, 128),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            // Auto-start checkbox
            var autoStartCheckBox = new CheckBox
            {
                Text = "Start with Windows",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 50),
                Size = new Size(200, 25),
                Checked = false
            };

            // Minimize to tray checkbox
            var minimizeToTrayCheckBox = new CheckBox
            {
                Text = "Minimize to system tray",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 80),
                Size = new Size(200, 25),
                Checked = true
            };

            // Auto-optimize checkbox
            var autoOptimizeCheckBox = new CheckBox
            {
                Text = "Auto-optimize on startup",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 110),
                Size = new Size(200, 25),
                Checked = false
            };

            // Check for updates checkbox
            var checkUpdatesCheckBox = new CheckBox
            {
                Text = "Check for updates automatically",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 140),
                Size = new Size(250, 25),
                Checked = true
            };

            // Theme selection
            var themeLabel = new Label
            {
                Text = "Theme:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 180),
                Size = new Size(50, 25)
            };

            var themeComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(40, 40, 40),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(70, 180),
                Size = new Size(150, 25)
            };
            themeComboBox.Items.AddRange(new[] { "Dark (Default)", "Light", "Gaming RGB" });
            themeComboBox.SelectedIndex = 0;

            // About section
            var aboutLabel = new Label
            {
                Text = "ABOUT RODEY PREMIUM TWEAKER:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(128, 128, 128),
                AutoSize = true,
                Location = new Point(10, 230)
            };

            var aboutDetailsLabel = new Label
            {
                Text = "Version: 1.0.0 Professional\n" +
                       "Build: 2024.01.15\n" +
                       "Developer: Rodey Premium\n" +
                       "License: Premium Edition\n" +
                       "\n" +
                       "© 2024 Rodey Premium. All rights reserved.\n" +
                       "Professional Windows optimization software.",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(10, 260),
                Size = new Size(740, 120)
            };

            settingsPanel.Controls.AddRange(new Control[] 
            { 
                settingsLabel, 
                autoStartCheckBox, 
                minimizeToTrayCheckBox, 
                autoOptimizeCheckBox, 
                checkUpdatesCheckBox,
                themeLabel,
                themeComboBox,
                aboutLabel,
                aboutDetailsLabel
            });

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                descLabel,
                settingsPanel
            });
        }
    }
}
