{"ast": null, "code": "import { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\nfunction getOriginIndex(from, total) {\n  if (from === \"first\") {\n    return 0;\n  } else {\n    const lastIndex = total - 1;\n    return from === \"last\" ? lastIndex : lastIndex / 2;\n  }\n}\nfunction stagger(duration = 0.1, {\n  startDelay = 0,\n  from = 0,\n  ease\n} = {}) {\n  return (i, total) => {\n    const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n    const distance = Math.abs(fromIndex - i);\n    let delay = duration * distance;\n    if (ease) {\n      const maxDelay = total * duration;\n      const easingFunction = easingDefinitionToFunction(ease);\n      delay = easingFunction(delay / maxDelay) * maxDelay;\n    }\n    return startDelay + delay;\n  };\n}\nexport { getOriginIndex, stagger };", "map": {"version": 3, "names": ["easingDefinitionToFunction", "getOriginIndex", "from", "total", "lastIndex", "stagger", "duration", "startDelay", "ease", "i", "fromIndex", "distance", "Math", "abs", "delay", "max<PERSON><PERSON><PERSON>", "easingFunction"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/animation/utils/stagger.mjs"], "sourcesContent": ["import { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\n\nfunction getOriginIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction stagger(duration = 0.1, { startDelay = 0, from = 0, ease } = {}) {\n    return (i, total) => {\n        const fromIndex = typeof from === \"number\" ? from : getOriginIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (ease) {\n            const maxDelay = total * duration;\n            const easingFunction = easingDefinitionToFunction(ease);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return startDelay + delay;\n    };\n}\n\nexport { getOriginIndex, stagger };\n"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,4BAA4B;AAEvE,SAASC,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAID,IAAI,KAAK,OAAO,EAAE;IAClB,OAAO,CAAC;EACZ,CAAC,MACI;IACD,MAAME,SAAS,GAAGD,KAAK,GAAG,CAAC;IAC3B,OAAOD,IAAI,KAAK,MAAM,GAAGE,SAAS,GAAGA,SAAS,GAAG,CAAC;EACtD;AACJ;AACA,SAASC,OAAOA,CAACC,QAAQ,GAAG,GAAG,EAAE;EAAEC,UAAU,GAAG,CAAC;EAAEL,IAAI,GAAG,CAAC;EAAEM;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EACtE,OAAO,CAACC,CAAC,EAAEN,KAAK,KAAK;IACjB,MAAMO,SAAS,GAAG,OAAOR,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGD,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC/E,MAAMQ,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,SAAS,GAAGD,CAAC,CAAC;IACxC,IAAIK,KAAK,GAAGR,QAAQ,GAAGK,QAAQ;IAC/B,IAAIH,IAAI,EAAE;MACN,MAAMO,QAAQ,GAAGZ,KAAK,GAAGG,QAAQ;MACjC,MAAMU,cAAc,GAAGhB,0BAA0B,CAACQ,IAAI,CAAC;MACvDM,KAAK,GAAGE,cAAc,CAACF,KAAK,GAAGC,QAAQ,CAAC,GAAGA,QAAQ;IACvD;IACA,OAAOR,UAAU,GAAGO,KAAK;EAC7B,CAAC;AACL;AAEA,SAASb,cAAc,EAAEI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}