{"ast": null, "code": "import { noop } from './noop.mjs';\nlet warning = noop;\nlet invariant = noop;\nif (process.env.NODE_ENV !== \"production\") {\n  warning = (check, message) => {\n    if (!check && typeof console !== \"undefined\") {\n      console.warn(message);\n    }\n  };\n  invariant = (check, message) => {\n    if (!check) {\n      throw new Error(message);\n    }\n  };\n}\nexport { invariant, warning };", "map": {"version": 3, "names": ["noop", "warning", "invariant", "process", "env", "NODE_ENV", "check", "message", "console", "warn", "Error"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/utils/errors.mjs"], "sourcesContent": ["import { noop } from './noop.mjs';\n\nlet warning = noop;\nlet invariant = noop;\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAEjC,IAAIC,OAAO,GAAGD,IAAI;AAClB,IAAIE,SAAS,GAAGF,IAAI;AACpB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACvCJ,OAAO,GAAGA,CAACK,KAAK,EAAEC,OAAO,KAAK;IAC1B,IAAI,CAACD,KAAK,IAAI,OAAOE,OAAO,KAAK,WAAW,EAAE;MAC1CA,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;IACzB;EACJ,CAAC;EACDL,SAAS,GAAGA,CAACI,KAAK,EAAEC,OAAO,KAAK;IAC5B,IAAI,CAACD,KAAK,EAAE;MACR,MAAM,IAAII,KAAK,CAACH,OAAO,CAAC;IAC5B;EACJ,CAAC;AACL;AAEA,SAASL,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}