using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RodeyPremiumTweaker.Core;

namespace RodeyPremiumTweaker.Controls
{
    public partial class CpuOptimizationControl : UserControl
    {
        private readonly SystemOptimizer _optimizer;
        private Button _optimizeButton;
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private ListBox _resultsListBox;
        private ComboBox _profileComboBox;

        public CpuOptimizationControl(SystemOptimizer optimizer)
        {
            _optimizer = optimizer;
            _optimizer.StatusChanged += OnStatusChanged;
            _optimizer.ProgressChanged += OnProgressChanged;

            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.BackColor = Color.White;
            this.Size = new Size(780, 600);
            this.AutoScroll = false;

            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            // Simple title
            var titleLabel = new Label
            {
                Text = "CPU Optimization",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(30, 30)
            };

            var descLabel = new Label
            {
                Text = "Optimize your CPU for better performance",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(108, 117, 125),
                AutoSize = true,
                Location = new Point(30, 65)
            };

            // Simple status card
            var statusCard = new Panel
            {
                BackColor = Color.FromArgb(248, 249, 250),
                Location = new Point(30, 110),
                Size = new Size(700, 80),
                BorderStyle = BorderStyle.FixedSingle
            };

            var statusLabel = new Label
            {
                Text = "CPU Status: Ready for optimization",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(73, 80, 87),
                AutoSize = true,
                Location = new Point(20, 30)
            };

            statusCard.Controls.Add(statusLabel);

            // Optimization Profiles Panel
            var profilesPanel = new Panel
            {
                BackColor = Color.FromArgb(26, 26, 26),
                Location = new Point(20, 240),
                Size = new Size(760, 120),
                BorderStyle = BorderStyle.FixedSingle
            };

            var profilesLabel = new Label
            {
                Text = "🎯 CPU OPTIMIZATION PROFILES",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 107, 53),
                AutoSize = true,
                Location = new Point(10, 10)
            };

            _profileComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(40, 40, 40),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Location = new Point(10, 35),
                Size = new Size(740, 25)
            };

            _profileComboBox.Items.AddRange(new[]
            {
                "🔥 EXTREME CPU PERFORMANCE - 1,247 Tweaks (+100-200% Performance)",
                "🚀 GAMING CPU OPTIMIZATION - 892 Tweaks (+50-100% Gaming Performance)",
                "⚡ MAXIMUM OVERCLOCK READY - 1,456 Tweaks (Extreme Overclocking)",
                "💎 PROFESSIONAL WORKSTATION - 734 Tweaks (Content Creation)",
                "🎮 ESPORTS COMPETITIVE - 623 Tweaks (Low Latency Gaming)"
            });
            _profileComboBox.SelectedIndex = 0;

            var profileDescLabel = new Label
            {
                Text = "Selected: EXTREME CPU PERFORMANCE\n" +
                       "• Disable ALL CPU Core Parking (All 12 Cores)\n" +
                       "• Set CPU to Maximum Performance State\n" +
                       "• Disable CPU Throttling & C-States\n" +
                       "• Optimize CPU Scheduling & Priority",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(10, 70),
                Size = new Size(740, 40)
            };

            profilesPanel.Controls.AddRange(new Control[] { profilesLabel, _profileComboBox, profileDescLabel });

            // Optimize button
            _optimizeButton = new Button
            {
                Text = "🔥 APPLY EXTREME CPU OPTIMIZATIONS (1,247 TWEAKS)",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.Black,
                BackColor = Color.FromArgb(255, 107, 53),
                FlatStyle = FlatStyle.Flat,
                Size = new Size(760, 50),
                Location = new Point(20, 380),
                Cursor = Cursors.Hand
            };
            _optimizeButton.FlatAppearance.BorderSize = 0;
            _optimizeButton.Click += OptimizeButton_Click;

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(20, 450),
                Size = new Size(760, 25),
                Style = ProgressBarStyle.Continuous,
                Visible = false,
                ForeColor = Color.FromArgb(255, 107, 53)
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "🔥 Ready to apply 1,247 extreme CPU performance optimizations",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 107, 53),
                AutoSize = true,
                Location = new Point(20, 485)
            };

            // Results section
            var resultsLabel = new Label
            {
                Text = "🔥 CPU OPTIMIZATION RESULTS & APPLIED TWEAKS:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 107, 53),
                AutoSize = true,
                Location = new Point(20, 520)
            };

            _resultsListBox = new ListBox
            {
                Font = new Font("Consolas", 9),
                BackColor = Color.FromArgb(26, 26, 26),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Location = new Point(20, 550),
                Size = new Size(760, 300),
                ScrollAlwaysVisible = true
            };

            // Pre-populate with CPU optimization details
            _resultsListBox.Items.AddRange(new[]
            {
                "🔥 READY TO APPLY 1,247 EXTREME CPU OPTIMIZATIONS:",
                "",
                "🚀 CORE PARKING OPTIMIZATIONS (347 tweaks):",
                "  ✓ Disable CPU Core Parking (All 12 Cores)",
                "  ✓ Set Minimum Processor State to 100%",
                "  ✓ Set Maximum Processor State to 100%",
                "  ✓ Disable CPU Idle States (C-States)",
                "  ✓ Force All Cores to Maximum Performance",
                "",
                "⚡ CPU PRIORITY & SCHEDULING (234 tweaks):",
                "  ✓ Set CPU Priority to High Performance",
                "  ✓ Optimize CPU Scheduling Algorithm",
                "  ✓ Set Win32PrioritySeparation to 38",
                "  ✓ Optimize IRQ Priority Settings",
                "  ✓ Set CPU Affinity for Gaming Processes",
                "",
                "🔥 POWER MANAGEMENT DESTRUCTION (456 tweaks):",
                "  ✓ Disable ALL CPU Power Saving Features",
                "  ✓ Set Ultimate Performance Power Plan",
                "  ✓ Disable CPU Frequency Scaling",
                "  ✓ Force Maximum CPU Clock Speed",
                "  ✓ Disable Thermal Throttling",
                "",
                "💎 ADVANCED CPU TWEAKS (210 tweaks):",
                "  ✓ Optimize CPU Cache Settings",
                "  ✓ Set CPU Timer Resolution to 0.5ms",
                "  ✓ Disable CPU Speculation Mitigations",
                "  ✓ Optimize CPU Branch Prediction",
                "  ✓ Set Maximum CPU Performance State",
                "",
                "Click 'APPLY EXTREME CPU OPTIMIZATIONS' to unlock maximum CPU performance!"
            });

            // Progress bar
            _progressBar = new ProgressBar
            {
                Location = new Point(30, 280),
                Size = new Size(400, 20),
                Visible = false
            };

            // Status label
            _statusLabel = new Label
            {
                Text = "Ready",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(108, 117, 125),
                AutoSize = true,
                Location = new Point(30, 310)
            };

            // Add all controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                descLabel,
                statusCard,
                _optimizeButton,
                _progressBar,
                _statusLabel
            });
        }

        private async void OptimizeButton_Click(object sender, EventArgs e)
        {
            try
            {
                _optimizeButton.Enabled = false;
                _progressBar.Visible = true;
                _progressBar.Value = 0;
                _resultsListBox.Items.Clear();

                var result = await _optimizer.OptimizeCpu();

                if (result.Success)
                {
                    _resultsListBox.Items.Add($"✅ {result.Message}");
                    _resultsListBox.Items.Add($"🚀 Estimated Performance Gain: {result.EstimatedFpsGain}");
                    _resultsListBox.Items.Add($"⏰ Applied at: {result.AppliedAt:HH:mm:ss}");
                }
                else
                {
                    _resultsListBox.Items.Add($"❌ {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _resultsListBox.Items.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _optimizeButton.Enabled = true;
                _progressBar.Visible = false;
            }
        }

        private void OnStatusChanged(object sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _statusLabel.Text = status));
            }
            else
            {
                _statusLabel.Text = status;
            }
        }

        private void OnProgressChanged(object sender, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => _progressBar.Value = Math.Min(progress, 100)));
            }
            else
            {
                _progressBar.Value = Math.Min(progress, 100);
            }
        }
    }
}
