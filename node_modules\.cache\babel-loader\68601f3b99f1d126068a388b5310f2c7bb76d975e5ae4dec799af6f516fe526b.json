{"ast": null, "code": "/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = v => type => type.test(v);\nexport { testValueType };", "map": {"version": 3, "names": ["testValueType", "v", "type", "test"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/render/dom/value-types/test.mjs"], "sourcesContent": ["/**\n * Tests a provided value against a ValueType\n */\nconst testValueType = (v) => (type) => type.test(v);\n\nexport { testValueType };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,aAAa,GAAIC,CAAC,IAAMC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC;AAEnD,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}