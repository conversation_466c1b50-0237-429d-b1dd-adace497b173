{"ast": null, "code": "import { mix } from './mix.mjs';\nimport { invariant } from './errors.mjs';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\nimport { hex } from '../value/types/color/hex.mjs';\nimport { rgba } from '../value/types/color/rgba.mjs';\nimport { hsla } from '../value/types/color/hsla.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n  const fromExpo = from * from;\n  return Math.sqrt(Math.max(0, v * (to * to - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = v => colorTypes.find(type => type.test(v));\nfunction asRGBA(color) {\n  const type = getColorType(color);\n  invariant(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n  let model = type.parse(color);\n  if (type === hsla) {\n    // TODO Remove this cast - needed since Framer Motion's stricter typing\n    model = hslaToRgba(model);\n  }\n  return model;\n}\nconst mixColor = (from, to) => {\n  const fromRGBA = asRGBA(from);\n  const toRGBA = asRGBA(to);\n  const blended = {\n    ...fromRGBA\n  };\n  return v => {\n    blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n    blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n    blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n    blended.alpha = mix(fromRGBA.alpha, toRGBA.alpha, v);\n    return rgba.transform(blended);\n  };\n};\nexport { mixColor, mixLinearColor };", "map": {"version": 3, "names": ["mix", "invariant", "hslaToRgba", "hex", "rgba", "hsla", "mixLinearColor", "from", "to", "v", "fromExpo", "Math", "sqrt", "max", "colorTypes", "getColorType", "find", "type", "test", "asRGBA", "color", "Boolean", "model", "parse", "mixColor", "fromRGBA", "toRGBA", "blended", "red", "green", "blue", "alpha", "transform"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/utils/mix-color.mjs"], "sourcesContent": ["import { mix } from './mix.mjs';\nimport { invariant } from './errors.mjs';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\nimport { hex } from '../value/types/color/hex.mjs';\nimport { rgba } from '../value/types/color/rgba.mjs';\nimport { hsla } from '../value/types/color/hsla.mjs';\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    return Math.sqrt(Math.max(0, v * (to * to - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    invariant(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n    let model = type.parse(color);\n    if (type === hsla) {\n        // TODO Remove this cast - needed since Framer Motion's stricter typing\n        model = hslaToRgba(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = mix(fromRGBA.alpha, toRGBA.alpha, v);\n        return rgba.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,GAAG,QAAQ,8BAA8B;AAClD,SAASC,IAAI,QAAQ,+BAA+B;AACpD,SAASC,IAAI,QAAQ,+BAA+B;;AAEpD;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,CAAC,KAAK;EACpC,MAAMC,QAAQ,GAAGH,IAAI,GAAGA,IAAI;EAC5B,OAAOI,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,IAAID,EAAE,GAAGA,EAAE,GAAGE,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC;AACtE,CAAC;AACD,MAAMI,UAAU,GAAG,CAACX,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;AACpC,MAAMU,YAAY,GAAIN,CAAC,IAAKK,UAAU,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAACT,CAAC,CAAC,CAAC;AACnE,SAASU,MAAMA,CAACC,KAAK,EAAE;EACnB,MAAMH,IAAI,GAAGF,YAAY,CAACK,KAAK,CAAC;EAChCnB,SAAS,CAACoB,OAAO,CAACJ,IAAI,CAAC,EAAE,IAAIG,KAAK,sEAAsE,CAAC;EACzG,IAAIE,KAAK,GAAGL,IAAI,CAACM,KAAK,CAACH,KAAK,CAAC;EAC7B,IAAIH,IAAI,KAAKZ,IAAI,EAAE;IACf;IACAiB,KAAK,GAAGpB,UAAU,CAACoB,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AAChB;AACA,MAAME,QAAQ,GAAGA,CAACjB,IAAI,EAAEC,EAAE,KAAK;EAC3B,MAAMiB,QAAQ,GAAGN,MAAM,CAACZ,IAAI,CAAC;EAC7B,MAAMmB,MAAM,GAAGP,MAAM,CAACX,EAAE,CAAC;EACzB,MAAMmB,OAAO,GAAG;IAAE,GAAGF;EAAS,CAAC;EAC/B,OAAQhB,CAAC,IAAK;IACVkB,OAAO,CAACC,GAAG,GAAGtB,cAAc,CAACmB,QAAQ,CAACG,GAAG,EAAEF,MAAM,CAACE,GAAG,EAAEnB,CAAC,CAAC;IACzDkB,OAAO,CAACE,KAAK,GAAGvB,cAAc,CAACmB,QAAQ,CAACI,KAAK,EAAEH,MAAM,CAACG,KAAK,EAAEpB,CAAC,CAAC;IAC/DkB,OAAO,CAACG,IAAI,GAAGxB,cAAc,CAACmB,QAAQ,CAACK,IAAI,EAAEJ,MAAM,CAACI,IAAI,EAAErB,CAAC,CAAC;IAC5DkB,OAAO,CAACI,KAAK,GAAG/B,GAAG,CAACyB,QAAQ,CAACM,KAAK,EAAEL,MAAM,CAACK,KAAK,EAAEtB,CAAC,CAAC;IACpD,OAAOL,IAAI,CAAC4B,SAAS,CAACL,OAAO,CAAC;EAClC,CAAC;AACL,CAAC;AAED,SAASH,QAAQ,EAAElB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}