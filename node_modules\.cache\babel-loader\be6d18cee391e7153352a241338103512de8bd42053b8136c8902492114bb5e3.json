{"ast": null, "code": "import { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence, {\n  defaultTransition = {},\n  ...sequenceTransition\n} = {}, scope) {\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numElements = 0) => {\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n        delay = 0,\n        times = defaultOffset(valueKeyframesAsList),\n        type = \"keyframes\",\n        ...remainingTransition\n      } = valueTransition;\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numElements) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      if (numKeyframes <= 2 && type === \"spring\") {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = {\n          ...remainingTransition\n        };\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration !== null && duration !== void 0 ? duration : duration = defaultDuration;\n      const startTime = currentTime + calculatedDelay;\n      const targetTime = startTime + duration;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      /**\n       * Find all the elements specified in the definition and parse value\n       * keyframes from their timeline definitions.\n       */\n      const elements = resolveElements(subject, scope, elementCache);\n      const numElements = elements.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const element = elements[elementIndex];\n        const subjectSequence = getSubjectSequence(element, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = {\n        ...defaultTransition,\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset,\n        ...sequenceTransition\n      };\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] ? {\n    ...transition,\n    ...transition[key]\n  } : {\n    ...transition\n  };\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["createGeneratorEasing", "resolveElements", "defaultOffset", "fillOffset", "progress", "secondsToMilliseconds", "isMotionValue", "calcNextTime", "addKeyframes", "compareByTime", "defaultSegmentEasing", "createAnimationsFromSequence", "sequence", "defaultTransition", "sequenceTransition", "scope", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "length", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "undefined", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numElements", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "remainingTransition", "ease", "calculatedDelay", "numKeyframes", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "springEasing", "startTime", "targetTime", "remainder", "unshift", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "elements", "element", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "sort", "valueOffset", "valueEasing", "value", "easing", "push", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numElements = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numElements)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            if (numKeyframes <= 2 && type === \"spring\") {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration !== null && duration !== void 0 ? duration : (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            const targetTime = startTime + duration;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            /**\n             * Find all the elements specified in the definition and parse value\n             * keyframes from their timeline definitions.\n             */\n            const elements = resolveElements(subject, scope, elementCache);\n            const numElements = elements.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const element = elements[elementIndex];\n                const subjectSequence = getSubjectSequence(element, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition[key]\n        ? { ...transition, ...transition[key] }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,SAASC,4BAA4BA,CAACC,QAAQ,EAAE;EAAEC,iBAAiB,GAAG,CAAC,CAAC;EAAE,GAAGC;AAAmB,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,EAAE;EAC3G,MAAMC,eAAe,GAAGH,iBAAiB,CAACI,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,CAAC,CAAC;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,QAAQ,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,OAAO,GAAGhB,QAAQ,CAACc,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;MAC7BN,UAAU,CAACO,GAAG,CAACD,OAAO,EAAEJ,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACM,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BN,UAAU,CAACO,GAAG,CAACD,OAAO,CAACI,IAAI,EAAEzB,YAAY,CAACiB,WAAW,EAAEI,OAAO,CAACK,EAAE,EAAEV,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACY,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKI,SAAS,EAAE;MAC7Bb,WAAW,GAAGjB,YAAY,CAACiB,WAAW,EAAEY,UAAU,CAACH,EAAE,EAAEV,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIgB,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAGA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAEC,YAAY,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,KAAK;MAChH,MAAMC,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;QAAEO,KAAK,GAAG,CAAC;QAAEC,KAAK,GAAG9C,aAAa,CAAC2C,oBAAoB,CAAC;QAAEI,IAAI,GAAG,WAAW;QAAE,GAAGC;MAAoB,CAAC,GAAGT,eAAe;MAC9H,IAAI;QAAEU,IAAI,GAAGtC,iBAAiB,CAACsC,IAAI,IAAI,SAAS;QAAElC;MAAS,CAAC,GAAGwB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMW,eAAe,GAAG,OAAOL,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMM,YAAY,GAAGR,oBAAoB,CAAClB,MAAM;MAChD,IAAI0B,YAAY,IAAI,CAAC,IAAIJ,IAAI,KAAK,QAAQ,EAAE;QACxC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIK,aAAa,GAAG,GAAG;QACvB,IAAID,YAAY,KAAK,CAAC,IAClBE,sBAAsB,CAACV,oBAAoB,CAAC,EAAE;UAC9C,MAAMW,KAAK,GAAGX,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/DS,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAG;UAAE,GAAGT;QAAoB,CAAC;QACnD,IAAIjC,QAAQ,KAAKoB,SAAS,EAAE;UACxBsB,gBAAgB,CAAC1C,QAAQ,GAAGZ,qBAAqB,CAACY,QAAQ,CAAC;QAC/D;QACA,MAAM2C,YAAY,GAAG5D,qBAAqB,CAAC2D,gBAAgB,EAAEL,aAAa,CAAC;QAC3EH,IAAI,GAAGS,YAAY,CAACT,IAAI;QACxBlC,QAAQ,GAAG2C,YAAY,CAAC3C,QAAQ;MACpC;MACAA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAIA,QAAQ,GAAGD,eAAgB;MAClF,MAAM6C,SAAS,GAAGrC,WAAW,GAAG4B,eAAe;MAC/C,MAAMU,UAAU,GAAGD,SAAS,GAAG5C,QAAQ;MACvC;AACZ;AACA;MACY,IAAI+B,KAAK,CAACrB,MAAM,KAAK,CAAC,IAAIqB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMe,SAAS,GAAGf,KAAK,CAACrB,MAAM,GAAGkB,oBAAoB,CAAClB,MAAM;MAC5DoC,SAAS,GAAG,CAAC,IAAI5D,UAAU,CAAC6C,KAAK,EAAEe,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYlB,oBAAoB,CAAClB,MAAM,KAAK,CAAC,IAC7BkB,oBAAoB,CAACmB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACYxD,YAAY,CAACkC,aAAa,EAAEG,oBAAoB,EAAEM,IAAI,EAAEH,KAAK,EAAEa,SAAS,EAAEC,UAAU,CAAC;MACrFxB,WAAW,GAAGmB,IAAI,CAACQ,GAAG,CAACb,eAAe,GAAGnC,QAAQ,EAAEqB,WAAW,CAAC;MAC/Db,aAAa,GAAGgC,IAAI,CAACQ,GAAG,CAACH,UAAU,EAAErC,aAAa,CAAC;IACvD,CAAC;IACD,IAAInB,aAAa,CAAC4B,OAAO,CAAC,EAAE;MACxB,MAAMgC,eAAe,GAAGC,kBAAkB,CAACjC,OAAO,EAAEd,SAAS,CAAC;MAC9DmB,oBAAoB,CAACJ,SAAS,EAAEC,UAAU,EAAEgC,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAMG,QAAQ,GAAGpE,eAAe,CAACiC,OAAO,EAAEnB,KAAK,EAAEM,YAAY,CAAC;MAC9D,MAAMuB,WAAW,GAAGyB,QAAQ,CAAC1C,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAIgB,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGC,WAAW,EAAED,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgBR,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAMkC,OAAO,GAAGD,QAAQ,CAAC1B,YAAY,CAAC;QACtC,MAAMuB,eAAe,GAAGC,kBAAkB,CAACG,OAAO,EAAElD,SAAS,CAAC;QAC9D,KAAK,MAAMmD,GAAG,IAAIpC,SAAS,EAAE;UACzBI,oBAAoB,CAACJ,SAAS,CAACoC,GAAG,CAAC,EAAEC,kBAAkB,CAACpC,UAAU,EAAEmC,GAAG,CAAC,EAAEH,gBAAgB,CAACG,GAAG,EAAEL,eAAe,CAAC,EAAEvB,YAAY,EAAEC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACArB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIc,WAAW;EAC9B;EACA;AACJ;AACA;EACIlB,SAAS,CAACqD,OAAO,CAAC,CAACC,cAAc,EAAEJ,OAAO,KAAK;IAC3C,KAAK,MAAMC,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAMhC,aAAa,GAAGgC,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACY7B,aAAa,CAACiC,IAAI,CAAClE,aAAa,CAAC;MACjC,MAAM0B,SAAS,GAAG,EAAE;MACpB,MAAMyC,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,aAAa,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEO,EAAE;UAAE6C,KAAK;UAAEC;QAAO,CAAC,GAAGrC,aAAa,CAAChB,CAAC,CAAC;QAC9CS,SAAS,CAAC6C,IAAI,CAACF,KAAK,CAAC;QACrBF,WAAW,CAACI,IAAI,CAAC5E,QAAQ,CAAC,CAAC,EAAEqB,aAAa,EAAEQ,EAAE,CAAC,CAAC;QAChD4C,WAAW,CAACG,IAAI,CAACD,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACZ,OAAO,CAAC,CAAC,CAAC;QACtB7B,SAAS,CAAC6B,OAAO,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/B0C,WAAW,CAACb,OAAO,CAACtD,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIkE,WAAW,CAACA,WAAW,CAACjD,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3CiD,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC;QACnB7C,SAAS,CAAC6C,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAAC9D,oBAAoB,CAAC+D,GAAG,CAACX,OAAO,CAAC,EAAE;QACpCpD,oBAAoB,CAACW,GAAG,CAACyC,OAAO,EAAE;UAC9BnC,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAM8C,UAAU,GAAGhE,oBAAoB,CAACiE,GAAG,CAACb,OAAO,CAAC;MACpDY,UAAU,CAAC/C,SAAS,CAACoC,GAAG,CAAC,GAAGpC,SAAS;MACrC+C,UAAU,CAAC9C,UAAU,CAACmC,GAAG,CAAC,GAAG;QACzB,GAAG1D,iBAAiB;QACpBI,QAAQ,EAAEQ,aAAa;QACvB0B,IAAI,EAAE0B,WAAW;QACjB7B,KAAK,EAAE4B,WAAW;QAClB,GAAG9D;MACP,CAAC;IACL;EACJ,CAAC,CAAC;EACF,OAAOI,oBAAoB;AAC/B;AACA,SAASiD,kBAAkBA,CAACjC,OAAO,EAAEd,SAAS,EAAE;EAC5C,CAACA,SAAS,CAAC6D,GAAG,CAAC/C,OAAO,CAAC,IAAId,SAAS,CAACS,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOd,SAAS,CAAC+D,GAAG,CAACjD,OAAO,CAAC;AACjC;AACA,SAASkC,gBAAgBA,CAACpC,IAAI,EAAEZ,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACY,IAAI,CAAC,EAChBZ,SAAS,CAACY,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOZ,SAAS,CAACY,IAAI,CAAC;AAC1B;AACA,SAASc,eAAeA,CAACX,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAASqC,kBAAkBA,CAACpC,UAAU,EAAEmC,GAAG,EAAE;EACzC,OAAOnC,UAAU,CAACmC,GAAG,CAAC,GAChB;IAAE,GAAGnC,UAAU;IAAE,GAAGA,UAAU,CAACmC,GAAG;EAAE,CAAC,GACrC;IAAE,GAAGnC;EAAW,CAAC;AAC3B;AACA,MAAMgD,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAM9B,sBAAsB,GAAIpB,SAAS,IAAKA,SAAS,CAACmD,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAASzE,4BAA4B,EAAE6D,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}