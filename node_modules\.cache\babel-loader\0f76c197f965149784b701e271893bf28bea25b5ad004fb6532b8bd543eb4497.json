{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\Settings.js\";\nimport React from 'react';\nimport { Settings as SettingsIcon } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: '#00ff88'\n      },\n      children: \"Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#888'\n      },\n      children: \"Application settings coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "Settings", "SettingsIcon", "jsxDEV", "_jsxDEV", "style", "padding", "textAlign", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/Settings.js"], "sourcesContent": ["import React from 'react';\nimport { Settings as SettingsIcon } from 'lucide-react';\n\nconst Settings = () => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1 style={{ color: '#00ff88' }}>Settings</h1>\n      <p style={{ color: '#888' }}>Application settings coming soon...</p>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,IAAIC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMH,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEG,OAAA;IAAKC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDJ,OAAA;MAAIC,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAU,CAAE;MAAAD,QAAA,EAAC;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC9CT,OAAA;MAAGC,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAAmC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIb,QAAQ;AASd,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}