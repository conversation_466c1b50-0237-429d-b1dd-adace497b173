{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\MemoryOptimization.js\";\nimport React from 'react';\nimport { MemoryStick, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MemoryOptimization = ({\n  isAdmin,\n  systemInfo\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '16px',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '48px',\n          height: '48px',\n          background: 'linear-gradient(135deg, #00ff88 0%, #00cc6a 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#000'\n        },\n        children: /*#__PURE__*/_jsxDEV(MemoryStick, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            color: '#00ff88',\n            fontSize: '28px',\n            fontWeight: '700',\n            margin: '0 0 4px 0'\n          },\n          children: \"Memory Optimization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#888',\n            fontSize: '14px',\n            margin: '0'\n          },\n          children: \"RAM optimization and memory management tweaks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(26, 26, 26, 0.8)',\n        border: '1px solid rgba(255, 255, 255, 0.1)',\n        borderRadius: '12px',\n        padding: '24px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#fff',\n          marginBottom: '16px'\n        },\n        children: \"Memory Optimization Coming Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#888',\n          marginBottom: '20px'\n        },\n        children: \"Advanced memory tweaks, RAM optimization, and cache management will be available here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), (systemInfo === null || systemInfo === void 0 ? void 0 : systemInfo.memory) && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(0, 255, 136, 0.1)',\n          border: '1px solid rgba(0, 255, 136, 0.2)',\n          borderRadius: '8px',\n          padding: '16px',\n          marginTop: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#00ff88',\n            margin: '0 0 8px 0'\n          },\n          children: [\"Total RAM: \", (systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1), \" GB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#888',\n            margin: '0'\n          },\n          children: [\"Available: \", (systemInfo.memory.free / 1024 / 1024 / 1024).toFixed(1), \" GB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        background: 'rgba(255, 165, 2, 0.1)',\n        border: '1px solid rgba(255, 165, 2, 0.3)',\n        borderRadius: '8px',\n        padding: '12px 16px',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px',\n        color: '#ffa502'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Administrator privileges required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = MemoryOptimization;\nexport default MemoryOptimization;\nvar _c;\n$RefreshReg$(_c, \"MemoryOptimization\");", "map": {"version": 3, "names": ["React", "MemoryStick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "MemoryOptimization", "isAdmin", "systemInfo", "style", "padding", "children", "display", "alignItems", "gap", "marginBottom", "width", "height", "background", "borderRadius", "justifyContent", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "margin", "border", "textAlign", "memory", "marginTop", "total", "toFixed", "free", "position", "bottom", "right", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/MemoryOptimization.js"], "sourcesContent": ["import React from 'react';\nimport { MemoryStick, AlertTriangle } from 'lucide-react';\n\nconst MemoryOptimization = ({ isAdmin, systemInfo }) => {\n  return (\n    <div style={{ padding: '20px' }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>\n        <div style={{ \n          width: '48px', \n          height: '48px', \n          background: 'linear-gradient(135deg, #00ff88 0%, #00cc6a 100%)',\n          borderRadius: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#000'\n        }}>\n          <MemoryStick size={32} />\n        </div>\n        <div>\n          <h1 style={{ color: '#00ff88', fontSize: '28px', fontWeight: '700', margin: '0 0 4px 0' }}>\n            Memory Optimization\n          </h1>\n          <p style={{ color: '#888', fontSize: '14px', margin: '0' }}>\n            RAM optimization and memory management tweaks\n          </p>\n        </div>\n      </div>\n      \n      <div style={{ \n        background: 'rgba(26, 26, 26, 0.8)',\n        border: '1px solid rgba(255, 255, 255, 0.1)',\n        borderRadius: '12px',\n        padding: '24px',\n        textAlign: 'center'\n      }}>\n        <h2 style={{ color: '#fff', marginBottom: '16px' }}>Memory Optimization Coming Soon</h2>\n        <p style={{ color: '#888', marginBottom: '20px' }}>\n          Advanced memory tweaks, RAM optimization, and cache management will be available here.\n        </p>\n        \n        {systemInfo?.memory && (\n          <div style={{ \n            background: 'rgba(0, 255, 136, 0.1)',\n            border: '1px solid rgba(0, 255, 136, 0.2)',\n            borderRadius: '8px',\n            padding: '16px',\n            marginTop: '16px'\n          }}>\n            <h3 style={{ color: '#00ff88', margin: '0 0 8px 0' }}>\n              Total RAM: {(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)} GB\n            </h3>\n            <p style={{ color: '#888', margin: '0' }}>\n              Available: {(systemInfo.memory.free / 1024 / 1024 / 1024).toFixed(1)} GB\n            </p>\n          </div>\n        )}\n      </div>\n\n      {!isAdmin && (\n        <div style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          background: 'rgba(255, 165, 2, 0.1)',\n          border: '1px solid rgba(255, 165, 2, 0.3)',\n          borderRadius: '8px',\n          padding: '12px 16px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          color: '#ffa502'\n        }}>\n          <AlertTriangle size={20} />\n          <span>Administrator privileges required</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MemoryOptimization;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAW,CAAC,KAAK;EACtD,oBACEH,OAAA;IAAKI,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BN,OAAA;MAAKI,KAAK,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE,MAAM;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACvFN,OAAA;QAAKI,KAAK,EAAE;UACVO,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE,mDAAmD;UAC/DC,YAAY,EAAE,MAAM;UACpBP,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBO,cAAc,EAAE,QAAQ;UACxBC,KAAK,EAAE;QACT,CAAE;QAAAV,QAAA,eACAN,OAAA,CAACH,WAAW;UAACoB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACNrB,OAAA;QAAAM,QAAA,gBACEN,OAAA;UAAII,KAAK,EAAE;YAAEY,KAAK,EAAE,SAAS;YAAEM,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAY,CAAE;UAAAlB,QAAA,EAAC;QAE3F;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrB,OAAA;UAAGI,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEM,QAAQ,EAAE,MAAM;YAAEE,MAAM,EAAE;UAAI,CAAE;UAAAlB,QAAA,EAAC;QAE5D;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrB,OAAA;MAAKI,KAAK,EAAE;QACVS,UAAU,EAAE,uBAAuB;QACnCY,MAAM,EAAE,oCAAoC;QAC5CX,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfqB,SAAS,EAAE;MACb,CAAE;MAAApB,QAAA,gBACAN,OAAA;QAAII,KAAK,EAAE;UAAEY,KAAK,EAAE,MAAM;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAA+B;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxFrB,OAAA;QAAGI,KAAK,EAAE;UAAEY,KAAK,EAAE,MAAM;UAAEN,YAAY,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnD;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAEH,CAAAlB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwB,MAAM,kBACjB3B,OAAA;QAAKI,KAAK,EAAE;UACVS,UAAU,EAAE,wBAAwB;UACpCY,MAAM,EAAE,kCAAkC;UAC1CX,YAAY,EAAE,KAAK;UACnBT,OAAO,EAAE,MAAM;UACfuB,SAAS,EAAE;QACb,CAAE;QAAAtB,QAAA,gBACAN,OAAA;UAAII,KAAK,EAAE;YAAEY,KAAK,EAAE,SAAS;YAAEQ,MAAM,EAAE;UAAY,CAAE;UAAAlB,QAAA,GAAC,aACzC,EAAC,CAACH,UAAU,CAACwB,MAAM,CAACE,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACxE;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrB,OAAA;UAAGI,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEQ,MAAM,EAAE;UAAI,CAAE;UAAAlB,QAAA,GAAC,aAC7B,EAAC,CAACH,UAAU,CAACwB,MAAM,CAACI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KACvE;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACnB,OAAO,iBACPF,OAAA;MAAKI,KAAK,EAAE;QACV4B,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbrB,UAAU,EAAE,wBAAwB;QACpCY,MAAM,EAAE,kCAAkC;QAC1CX,YAAY,EAAE,KAAK;QACnBT,OAAO,EAAE,WAAW;QACpBE,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,KAAK;QACVO,KAAK,EAAE;MACT,CAAE;MAAAV,QAAA,gBACAN,OAAA,CAACF,aAAa;QAACmB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BrB,OAAA;QAAAM,QAAA,EAAM;MAAiC;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACc,EAAA,GA5EIlC,kBAAkB;AA8ExB,eAAeA,kBAAkB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}