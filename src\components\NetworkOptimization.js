import React from 'react';
import { Wifi, AlertTriangle } from 'lucide-react';

const NetworkOptimization = ({ isAdmin }) => {
  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px' }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          background: 'linear-gradient(135deg, #00ff88 0%, #00cc6a 100%)',
          borderRadius: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#000'
        }}>
          <Wifi size={32} />
        </div>
        <div>
          <h1 style={{ color: '#00ff88', fontSize: '28px', fontWeight: '700', margin: '0 0 4px 0' }}>
            Network Optimization
          </h1>
          <p style={{ color: '#888', fontSize: '14px', margin: '0' }}>
            Internet latency reduction and network performance tweaks
          </p>
        </div>
      </div>
      
      <div style={{ 
        background: 'rgba(26, 26, 26, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <h2 style={{ color: '#fff', marginBottom: '16px' }}>Network Optimization Coming Soon</h2>
        <p style={{ color: '#888', marginBottom: '20px' }}>
          Advanced network tweaks, latency reduction, and TCP optimization will be available here.
        </p>
      </div>

      {!isAdmin && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          background: 'rgba(255, 165, 2, 0.1)',
          border: '1px solid rgba(255, 165, 2, 0.3)',
          borderRadius: '8px',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: '#ffa502'
        }}>
          <AlertTriangle size={20} />
          <span>Administrator privileges required</span>
        </div>
      )}
    </div>
  );
};

export default NetworkOptimization;
