{"ast": null, "code": "import { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\nfunction useInstantLayoutTransition() {\n  return startTransition;\n}\nfunction startTransition(callback) {\n  if (!rootProjectionNode.current) return;\n  rootProjectionNode.current.isUpdating = false;\n  rootProjectionNode.current.blockUpdate();\n  callback && callback();\n}\nexport { useInstantLayoutTransition };", "map": {"version": 3, "names": ["rootProjectionNode", "useInstantLayoutTransition", "startTransition", "callback", "current", "isUpdating", "blockUpdate"], "sources": ["C:/rodeypremium/node_modules/framer-motion/dist/es/projection/use-instant-layout-transition.mjs"], "sourcesContent": ["import { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\n\nfunction useInstantLayoutTransition() {\n    return startTransition;\n}\nfunction startTransition(callback) {\n    if (!rootProjectionNode.current)\n        return;\n    rootProjectionNode.current.isUpdating = false;\n    rootProjectionNode.current.blockUpdate();\n    callback && callback();\n}\n\nexport { useInstantLayoutTransition };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,+BAA+B;AAElE,SAASC,0BAA0BA,CAAA,EAAG;EAClC,OAAOC,eAAe;AAC1B;AACA,SAASA,eAAeA,CAACC,QAAQ,EAAE;EAC/B,IAAI,CAACH,kBAAkB,CAACI,OAAO,EAC3B;EACJJ,kBAAkB,CAACI,OAAO,CAACC,UAAU,GAAG,KAAK;EAC7CL,kBAAkB,CAACI,OAAO,CAACE,WAAW,CAAC,CAAC;EACxCH,QAAQ,IAAIA,QAAQ,CAAC,CAAC;AAC1B;AAEA,SAASF,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}