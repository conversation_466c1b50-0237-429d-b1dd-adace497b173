{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst KeyRound = createLucideIcon(\"KeyRound\", [[\"path\", {\n  d: \"M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z\",\n  key: \"167ctg\"\n}], [\"circle\", {\n  cx: \"16.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  key: \"1kog09\"\n}]]);\nexport { KeyRound as default };", "map": {"version": 3, "names": ["KeyRound", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\key-round.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name KeyRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxOHYzYzAgLjYuNCAxIDEgMWg0di0zaDN2LTNoMmwxLjQtMS40YTYuNSA2LjUgMCAxIDAtNC00WiIgLz4KICA8Y2lyY2xlIGN4PSIxNi41IiBjeT0iNy41IiByPSIuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/key-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst KeyRound = createLucideIcon('KeyRound', [\n  ['path', { d: 'M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z', key: '167ctg' }],\n  ['circle', { cx: '16.5', cy: '7.5', r: '.5', key: '1kog09' }],\n]);\n\nexport default KeyRound;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,6DAA+D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAOC,CAAG;EAAMH,GAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}