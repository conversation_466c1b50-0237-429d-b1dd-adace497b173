using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Threading;
using System.Threading.Tasks;

namespace RodeyPremiumTweaker.Core
{
    public class PerformanceMonitor
    {
        private readonly System.Threading.Timer _monitoringTimer;
        private readonly List<PerformanceData> _performanceHistory = new();
        private readonly object _lockObject = new();

        public event EventHandler<PerformanceData> PerformanceUpdated;

        private PerformanceCounter _cpuCounter;
        private PerformanceCounter _ramCounter;
        private PerformanceCounter _diskCounter;

        public PerformanceData CurrentPerformance { get; private set; } = new();
        public bool IsMonitoring { get; private set; }

        public PerformanceMonitor()
        {
            InitializeCounters();
            _monitoringTimer = new System.Threading.Timer(UpdatePerformanceData, null, Timeout.Infinite, Timeout.Infinite);
        }

        private void InitializeCounters()
        {
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _ramCounter = new PerformanceCounter("Memory", "Available MBytes");
                _diskCounter = new PerformanceCounter("PhysicalDisk", "% Disk Time", "_Total");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to initialize performance counters: {ex.Message}");
            }
        }

        public void StartMonitoring()
        {
            if (!IsMonitoring)
            {
                IsMonitoring = true;
                _monitoringTimer.Change(0, 1000); // Update every second
            }
        }

        public void StopMonitoring()
        {
            if (IsMonitoring)
            {
                IsMonitoring = false;
                _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);
            }
        }

        private async void UpdatePerformanceData(object state)
        {
            try
            {
                var data = await GetCurrentPerformanceData();

                lock (_lockObject)
                {
                    CurrentPerformance = data;
                    _performanceHistory.Add(data);

                    // Keep only last 60 seconds of data
                    if (_performanceHistory.Count > 60)
                    {
                        _performanceHistory.RemoveAt(0);
                    }
                }

                PerformanceUpdated?.Invoke(this, data);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating performance data: {ex.Message}");
            }
        }

        private async Task<PerformanceData> GetCurrentPerformanceData()
        {
            return await Task.Run(() =>
            {
                var data = new PerformanceData
                {
                    Timestamp = DateTime.Now
                };

                try
                {
                    // CPU Usage
                    data.CpuUsage = _cpuCounter?.NextValue() ?? 0;

                    // RAM Usage
                    var availableRam = _ramCounter?.NextValue() ?? 0;
                    var totalRam = GetTotalPhysicalMemory();
                    data.RamUsage = totalRam > 0 ? ((totalRam - availableRam) / totalRam) * 100 : 0;
                    data.RamAvailable = availableRam;
                    data.RamTotal = totalRam;

                    // Disk Usage
                    data.DiskUsage = _diskCounter?.NextValue() ?? 0;

                    // GPU Usage (if available)
                    data.GpuUsage = GetGpuUsage();

                    // Temperature data
                    data.CpuTemperature = GetCpuTemperature();
                    data.GpuTemperature = GetGpuTemperature();

                    // FPS data (if game is running)
                    data.CurrentFps = GetCurrentFps();

                    // Process count
                    data.ProcessCount = Process.GetProcesses().Length;

                    // Network usage
                    data.NetworkUsage = GetNetworkUsage();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error collecting performance data: {ex.Message}");
                }

                return data;
            });
        }

        private float GetTotalPhysicalMemory()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var totalBytes = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                    return totalBytes / (1024f * 1024f); // Convert to MB
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting total physical memory: {ex.Message}");
            }
            return 0;
        }

        private float GetGpuUsage()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PerfRawData_GPUPerformanceCounters_GPUEngine");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var utilization = obj["UtilizationPercentage"];
                    if (utilization != null)
                    {
                        return Convert.ToSingle(utilization);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting GPU usage: {ex.Message}");
            }
            return 0;
        }

        private float GetCpuTemperature()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("root\\WMI", "SELECT * FROM MSAcpi_ThermalZoneTemperature");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var temp = Convert.ToDouble(obj["CurrentTemperature"]);
                    return (float)((temp - 2732) / 10.0); // Convert from Kelvin to Celsius
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting CPU temperature: {ex.Message}");
            }
            return 0;
        }

        private float GetGpuTemperature()
        {
            try
            {
                // This would require specific GPU vendor APIs (NVIDIA, AMD)
                // For now, return 0 as placeholder
                return 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting GPU temperature: {ex.Message}");
                return 0;
            }
        }

        private int GetCurrentFps()
        {
            try
            {
                // This would require hooking into DirectX/OpenGL or using external tools
                // For now, return 0 as placeholder
                return 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting current FPS: {ex.Message}");
                return 0;
            }
        }

        private float GetNetworkUsage()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PerfRawData_Tcpip_NetworkInterface WHERE Name != 'Loopback Pseudo-Interface 1'");
                float totalBytes = 0;
                foreach (ManagementObject obj in searcher.Get())
                {
                    var bytesTotal = Convert.ToUInt64(obj["BytesTotalPerSec"]);
                    totalBytes += bytesTotal;
                }
                return totalBytes / (1024f * 1024f); // Convert to MB/s
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network usage: {ex.Message}");
                return 0;
            }
        }

        public List<PerformanceData> GetPerformanceHistory()
        {
            lock (_lockObject)
            {
                return _performanceHistory.ToList();
            }
        }

        public SystemInfo GetSystemInfo()
        {
            var systemInfo = new SystemInfo();

            try
            {
                // CPU Information
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        systemInfo.CpuName = obj["Name"]?.ToString();
                        systemInfo.CpuCores = Convert.ToInt32(obj["NumberOfCores"]);
                        systemInfo.CpuThreads = Convert.ToInt32(obj["NumberOfLogicalProcessors"]);
                        systemInfo.CpuSpeed = Convert.ToInt32(obj["MaxClockSpeed"]);
                        break;
                    }
                }

                // GPU Information
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var name = obj["Name"]?.ToString();
                        if (!string.IsNullOrEmpty(name) && !name.Contains("Microsoft"))
                        {
                            systemInfo.GpuName = name;
                            systemInfo.GpuMemory = Convert.ToInt64(obj["AdapterRAM"] ?? 0) / (1024 * 1024); // Convert to MB
                            break;
                        }
                    }
                }

                // RAM Information
                systemInfo.RamTotal = GetTotalPhysicalMemory();

                // OS Information
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        systemInfo.OsName = obj["Caption"]?.ToString();
                        systemInfo.OsVersion = obj["Version"]?.ToString();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting system info: {ex.Message}");
            }

            return systemInfo;
        }

        public void Dispose()
        {
            StopMonitoring();
            _monitoringTimer?.Dispose();
            _cpuCounter?.Dispose();
            _ramCounter?.Dispose();
            _diskCounter?.Dispose();
        }
    }

    public class PerformanceData
    {
        public DateTime Timestamp { get; set; }
        public float CpuUsage { get; set; }
        public float RamUsage { get; set; }
        public float RamAvailable { get; set; }
        public float RamTotal { get; set; }
        public float DiskUsage { get; set; }
        public float GpuUsage { get; set; }
        public float CpuTemperature { get; set; }
        public float GpuTemperature { get; set; }
        public int CurrentFps { get; set; }
        public int ProcessCount { get; set; }
        public float NetworkUsage { get; set; }
    }

    public class SystemInfo
    {
        public string CpuName { get; set; } = "Unknown";
        public int CpuCores { get; set; }
        public int CpuThreads { get; set; }
        public int CpuSpeed { get; set; }
        public string GpuName { get; set; } = "Unknown";
        public long GpuMemory { get; set; }
        public float RamTotal { get; set; }
        public string OsName { get; set; } = "Unknown";
        public string OsVersion { get; set; } = "Unknown";
    }
}
