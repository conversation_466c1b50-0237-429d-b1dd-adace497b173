{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst LandPlot = createLucideIcon(\"LandPlot\", [[\"path\", {\n  d: \"m12 8 6-3-6-3v10\",\n  key: \"mvpnpy\"\n}], [\"path\", {\n  d: \"m8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12\",\n  key: \"ek95tt\"\n}], [\"path\", {\n  d: \"m6.49 12.85 11.02 6.3\",\n  key: \"1kt42w\"\n}], [\"path\", {\n  d: \"M17.51 12.85 6.5 19.15\",\n  key: \"v55bdg\"\n}]]);\nexport { LandPlot as default };", "map": {"version": 3, "names": ["LandPlot", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\land-plot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LandPlot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgOCA2LTMtNi0zdjEwIiAvPgogIDxwYXRoIGQ9Im04IDExLjk5LTUuNSAzLjE0YTEgMSAwIDAgMCAwIDEuNzRsOC41IDQuODZhMiAyIDAgMCAwIDIgMGw4LjUtNC44NmExIDEgMCAwIDAgMC0xLjc0TDE2IDEyIiAvPgogIDxwYXRoIGQ9Im02LjQ5IDEyLjg1IDExLjAyIDYuMyIgLz4KICA8cGF0aCBkPSJNMTcuNTEgMTIuODUgNi41IDE5LjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/land-plot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LandPlot = createLucideIcon('LandPlot', [\n  ['path', { d: 'm12 8 6-3-6-3v10', key: 'mvpnpy' }],\n  [\n    'path',\n    {\n      d: 'm8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12',\n      key: 'ek95tt',\n    },\n  ],\n  ['path', { d: 'm6.49 12.85 11.02 6.3', key: '1kt42w' }],\n  ['path', { d: 'M17.51 12.85 6.5 19.15', key: 'v55bdg' }],\n]);\n\nexport default LandPlot;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}