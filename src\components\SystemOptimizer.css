.system-optimizer {
  padding: 0;
  height: 100%;
  overflow-y: auto;
}

.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
}

.header-text h1 {
  font-size: 28px;
  font-weight: 700;
  color: #00ff88;
  margin-bottom: 4px;
}

.header-text p {
  color: #888;
  font-size: 14px;
}

.optimizer-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Profile Selection */
.profile-section h2 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.profile-card {
  background: rgba(26, 26, 26, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--profile-color);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.profile-card.selected {
  border-color: var(--profile-color);
  background: rgba(26, 26, 26, 0.9);
}

.profile-card.selected::before {
  opacity: 1;
}

.profile-icon {
  width: 40px;
  height: 40px;
  background: var(--profile-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  margin-bottom: 12px;
}

.profile-content h3 {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
}

.profile-content p {
  color: #888;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.profile-gain {
  color: var(--profile-color);
  font-size: 14px;
  font-weight: 600;
}

.profile-tweaks {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: #888;
}

/* Controls Section */
.controls-section {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.main-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.optimize-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  color: #000;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.optimize-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
}

.optimize-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.restore-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.restore-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(0, 255, 136, 0.3);
}

.restore-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Progress Section */
.progress-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.current-step {
  color: #00ff88;
  font-size: 14px;
  font-weight: 500;
}

.progress-percent {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88 0%, #00cc6a 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.step-indicators {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.step-indicator {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.step-indicator.completed {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.step-indicator.current {
  background: #00ff88;
  color: #000;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Results Section */
.results-section {
  background: rgba(0, 255, 136, 0.05);
  border: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 12px;
  padding: 24px;
}

.results-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.success-icon {
  color: #00ff88;
}

.results-header h2 {
  color: #00ff88;
  font-size: 20px;
  font-weight: 600;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.result-card {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.result-card h3 {
  color: #888;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.result-value {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
}

.result-value.gain {
  color: #00ff88;
}

.category-results h3 {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.category-name {
  font-size: 11px;
  color: #888;
  font-weight: 500;
}

.category-improvement {
  font-size: 12px;
  color: #00ff88;
  font-weight: 600;
}

/* Profile Details */
.profile-details h2 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.tweaks-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 8px;
}

.tweak-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 13px;
  color: #ccc;
}

.tweak-item svg {
  color: #00ff88;
  flex-shrink: 0;
}

/* Admin Warning */
.admin-warning {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 165, 2, 0.1);
  border: 1px solid rgba(255, 165, 2, 0.3);
  border-radius: 12px;
  padding: 16px;
  max-width: 350px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: #ffa502;
}

.admin-warning svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.admin-warning strong {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

.admin-warning p {
  font-size: 13px;
  line-height: 1.4;
  opacity: 0.9;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
  
  .main-controls {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .category-grid {
    grid-template-columns: 1fr;
  }
  
  .tweaks-list {
    grid-template-columns: 1fr;
  }
}
