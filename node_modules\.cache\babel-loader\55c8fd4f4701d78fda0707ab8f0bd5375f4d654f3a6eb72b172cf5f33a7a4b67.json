{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst AlignHorizontalDistributeStart = createLucideIcon(\"AlignHorizontalDistributeStart\", [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"4\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"1wwnby\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"14\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1fe6j6\"\n}], [\"path\", {\n  d: \"M4 2v20\",\n  key: \"gtpd5x\"\n}], [\"path\", {\n  d: \"M14 2v20\",\n  key: \"tg6bpw\"\n}]]);\nexport { AlignHorizontalDistributeStart as default };", "map": {"version": 3, "names": ["AlignHorizontalDistributeStart", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\align-horizontal-distribute-start.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignHorizontalDistributeStart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iNCIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjE0IiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNNCAydjIwIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-horizontal-distribute-start\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalDistributeStart = createLucideIcon('AlignHorizontalDistributeStart', [\n  ['rect', { width: '6', height: '14', x: '4', y: '5', rx: '2', key: '1wwnby' }],\n  ['rect', { width: '6', height: '10', x: '14', y: '7', rx: '2', key: '1fe6j6' }],\n  ['path', { d: 'M4 2v20', key: 'gtpd5x' }],\n  ['path', { d: 'M14 2v20', key: 'tg6bpw' }],\n]);\n\nexport default AlignHorizontalDistributeStart;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,8BAAA,GAAiCC,gBAAA,CAAiB,gCAAkC,GACxF,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}