{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\KernelOptimization.js\";\nimport React from 'react';\nimport { Shield } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KernelOptimization = ({\n  isAdmin\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      style: {\n        color: '#ff6b35'\n      },\n      children: \"Kernel Optimization\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#888'\n      },\n      children: \"Kernel-level optimizations coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = KernelOptimization;\nexport default KernelOptimization;\nvar _c;\n$RefreshReg$(_c, \"KernelOptimization\");", "map": {"version": 3, "names": ["React", "Shield", "jsxDEV", "_jsxDEV", "KernelOptimization", "isAdmin", "style", "padding", "textAlign", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/KernelOptimization.js"], "sourcesContent": ["import React from 'react';\nimport { Shield } from 'lucide-react';\n\nconst KernelOptimization = ({ isAdmin }) => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1 style={{ color: '#ff6b35' }}>Kernel Optimization</h1>\n      <p style={{ color: '#888' }}>Kernel-level optimizations coming soon...</p>\n    </div>\n  );\n};\n\nexport default KernelOptimization;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC1C,oBACEF,OAAA;IAAKG,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnDN,OAAA;MAAIG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAU,CAAE;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzDX,OAAA;MAAGG,KAAK,EAAE;QAAEI,KAAK,EAAE;MAAO,CAAE;MAAAD,QAAA,EAAC;IAAyC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvE,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIX,kBAAkB;AASxB,eAAeA,kBAAkB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}