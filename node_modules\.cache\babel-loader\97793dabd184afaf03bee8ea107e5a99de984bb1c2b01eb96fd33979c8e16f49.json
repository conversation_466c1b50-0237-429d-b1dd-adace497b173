{"ast": null, "code": "/**\n * @license lucide-react v0.300.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Candy = createLucideIcon(\"<PERSON>\", [[\"path\", {\n  d: \"m9.5 7.5-2 2a4.95 4.95 0 1 0 7 7l2-2a4.95 4.95 0 1 0-7-7Z\",\n  key: \"ue6khb\"\n}], [\"path\", {\n  d: \"M14 6.5v10\",\n  key: \"5xnk7c\"\n}], [\"path\", {\n  d: \"M10 7.5v10\",\n  key: \"1uew51\"\n}], [\"path\", {\n  d: \"m16 7 1-5 1.37.68A3 3 0 0 0 19.7 3H21v1.3c0 .********** 1.33L22 7l-5 1\",\n  key: \"b9cp6k\"\n}], [\"path\", {\n  d: \"m8 17-1 5-1.37-.68A3 3 0 0 0 4.3 21H3v-1.3a3 3 0 0 0-.32-1.33L2 17l5-1\",\n  key: \"5lney8\"\n}]]);\nexport { Candy as default };", "map": {"version": 3, "names": ["<PERSON>", "createLucideIcon", "d", "key"], "sources": ["C:\\rodeypremium\\node_modules\\lucide-react\\src\\icons\\candy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Candy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/candy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Candy = createLucideIcon('Candy', [\n  ['path', { d: 'm9.5 7.5-2 2a4.95 4.95 0 1 0 7 7l2-2a4.95 4.95 0 1 0-7-7Z', key: 'ue6khb' }],\n  ['path', { d: 'M14 6.5v10', key: '5xnk7c' }],\n  ['path', { d: 'M10 7.5v10', key: '1uew51' }],\n  [\n    'path',\n    { d: 'm16 7 1-5 1.37.68A3 3 0 0 0 19.7 3H21v1.3c0 .********** 1.33L22 7l-5 1', key: 'b9cp6k' },\n  ],\n  [\n    'path',\n    { d: 'm8 17-1 5-1.37-.68A3 3 0 0 0 4.3 21H3v-1.3a3 3 0 0 0-.32-1.33L2 17l5-1', key: '5lney8' },\n  ],\n]);\n\nexport default Candy;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EAAED,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,EACA,CACE,QACA;EAAED,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}