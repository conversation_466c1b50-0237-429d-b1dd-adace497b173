{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\TitleBar.js\";\nimport React from 'react';\nimport { Minus, Square, X, Shield } from 'lucide-react';\nimport './TitleBar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TitleBar = () => {\n  const handleMinimize = () => {\n    window.electronAPI.minimizeWindow();\n  };\n  const handleMaximize = () => {\n    window.electronAPI.maximizeWindow();\n  };\n  const handleClose = () => {\n    window.electronAPI.closeWindow();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"title-bar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"title-bar-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-icon\",\n        children: /*#__PURE__*/_jsxDEV(Shield, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"title-main\",\n          children: \"Rodey Premium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"title-sub\",\n          children: \"Tweaker\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"title-bar-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"performance-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"perf-dot perf-active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"System Optimized\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"title-bar-right\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"title-bar-button minimize\",\n        onClick: handleMinimize,\n        title: \"Minimize\",\n        children: /*#__PURE__*/_jsxDEV(Minus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"title-bar-button maximize\",\n        onClick: handleMaximize,\n        title: \"Maximize\",\n        children: /*#__PURE__*/_jsxDEV(Square, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"title-bar-button close\",\n        onClick: handleClose,\n        title: \"Close\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = TitleBar;\nexport default TitleBar;\nvar _c;\n$RefreshReg$(_c, \"TitleBar\");", "map": {"version": 3, "names": ["React", "Minus", "Square", "X", "Shield", "jsxDEV", "_jsxDEV", "TitleBar", "handleMinimize", "window", "electronAPI", "minimizeWindow", "handleMaximize", "maximizeWindow", "handleClose", "closeWindow", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/TitleBar.js"], "sourcesContent": ["import React from 'react';\nimport { Minus, Square, X, Shield } from 'lucide-react';\nimport './TitleBar.css';\n\nconst TitleBar = () => {\n  const handleMinimize = () => {\n    window.electronAPI.minimizeWindow();\n  };\n\n  const handleMaximize = () => {\n    window.electronAPI.maximizeWindow();\n  };\n\n  const handleClose = () => {\n    window.electronAPI.closeWindow();\n  };\n\n  return (\n    <div className=\"title-bar\">\n      <div className=\"title-bar-left\">\n        <div className=\"app-icon\">\n          <Shield size={20} />\n        </div>\n        <div className=\"app-title\">\n          <span className=\"title-main\">Rodey Premium</span>\n          <span className=\"title-sub\">Tweaker</span>\n        </div>\n      </div>\n\n      <div className=\"title-bar-center\">\n        <div className=\"performance-indicator\">\n          <div className=\"perf-dot perf-active\"></div>\n          <span>System Optimized</span>\n        </div>\n      </div>\n\n      <div className=\"title-bar-right\">\n        <button \n          className=\"title-bar-button minimize\"\n          onClick={handleMinimize}\n          title=\"Minimize\"\n        >\n          <Minus size={16} />\n        </button>\n        <button \n          className=\"title-bar-button maximize\"\n          onClick={handleMaximize}\n          title=\"Maximize\"\n        >\n          <Square size={14} />\n        </button>\n        <button \n          className=\"title-bar-button close\"\n          onClick={handleClose}\n          title=\"Close\"\n        >\n          <X size={16} />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default TitleBar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,MAAM,EAAEC,CAAC,EAAEC,MAAM,QAAQ,cAAc;AACvD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BC,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC,CAAC;EACrC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BH,MAAM,CAACC,WAAW,CAACG,cAAc,CAAC,CAAC;EACrC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBL,MAAM,CAACC,WAAW,CAACK,WAAW,CAAC,CAAC;EAClC,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBX,OAAA;MAAKU,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BX,OAAA;QAAKU,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBX,OAAA,CAACF,MAAM;UAACc,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACNhB,OAAA;QAAKU,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBX,OAAA;UAAMU,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjDhB,OAAA;UAAMU,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKU,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BX,OAAA;QAAKU,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCX,OAAA;UAAKU,SAAS,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5ChB,OAAA;UAAAW,QAAA,EAAM;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKU,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BX,OAAA;QACEU,SAAS,EAAC,2BAA2B;QACrCO,OAAO,EAAEf,cAAe;QACxBgB,KAAK,EAAC,UAAU;QAAAP,QAAA,eAEhBX,OAAA,CAACL,KAAK;UAACiB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACThB,OAAA;QACEU,SAAS,EAAC,2BAA2B;QACrCO,OAAO,EAAEX,cAAe;QACxBY,KAAK,EAAC,UAAU;QAAAP,QAAA,eAEhBX,OAAA,CAACJ,MAAM;UAACgB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACThB,OAAA;QACEU,SAAS,EAAC,wBAAwB;QAClCO,OAAO,EAAET,WAAY;QACrBU,KAAK,EAAC,OAAO;QAAAP,QAAA,eAEbX,OAAA,CAACH,CAAC;UAACe,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAzDIlB,QAAQ;AA2Dd,eAAeA,QAAQ;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}