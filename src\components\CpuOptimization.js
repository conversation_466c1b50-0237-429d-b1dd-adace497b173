import React, { useState, useEffect } from 'react';
import { 
  Cpu, 
  Zap, 
  Thermometer, 
  Activity, 
  Settings, 
  Play, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import './CpuOptimization.css';

const CpuOptimization = ({ isAdmin, systemInfo }) => {
  const [cpuTweaks, setCpuTweaks] = useState({
    powerPlan: false,
    coreParking: false,
    threadScheduling: false,
    cpuPriority: false,
    turboBoost: false,
    cStates: false,
    prefetcher: false,
    spectre: false
  });

  const [cpuMetrics, setCpuMetrics] = useState({
    temperature: 45,
    usage: 12,
    frequency: 3200,
    voltage: 1.25,
    powerDraw: 65
  });

  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);

  const cpuOptimizations = [
    {
      id: 'powerPlan',
      title: 'High Performance Power Plan',
      description: 'Set Windows to High Performance mode for maximum CPU performance',
      impact: 'High',
      category: 'Power Management',
      riskLevel: 'Low',
      estimatedGain: '+15-25% performance'
    },
    {
      id: 'coreParking',
      title: 'Disable CPU Core Parking',
      description: 'Prevent Windows from parking CPU cores to maintain consistent performance',
      impact: 'High',
      category: 'Core Management',
      riskLevel: 'Low',
      estimatedGain: '+10-20% in multi-threaded tasks'
    },
    {
      id: 'threadScheduling',
      title: 'Optimize Thread Scheduling',
      description: 'Improve CPU thread distribution and scheduling algorithms',
      impact: 'Medium',
      category: 'Scheduling',
      riskLevel: 'Low',
      estimatedGain: '*****% in gaming'
    },
    {
      id: 'cpuPriority',
      title: 'CPU Priority Optimization',
      description: 'Set optimal CPU priority for foreground applications',
      impact: 'Medium',
      category: 'Priority',
      riskLevel: 'Low',
      estimatedGain: '*****% responsiveness'
    },
    {
      id: 'turboBoost',
      title: 'Intel Turbo Boost / AMD Precision Boost',
      description: 'Optimize boost algorithms for maximum single-core performance',
      impact: 'High',
      category: 'Boost Technology',
      riskLevel: 'Medium',
      estimatedGain: '+20-35% single-core performance'
    },
    {
      id: 'cStates',
      title: 'Disable C-States',
      description: 'Prevent CPU from entering sleep states for consistent performance',
      impact: 'High',
      category: 'Power States',
      riskLevel: 'Medium',
      estimatedGain: '+10-25% latency reduction'
    },
    {
      id: 'prefetcher',
      title: 'Hardware Prefetcher Optimization',
      description: 'Optimize CPU prefetcher settings for better cache performance',
      impact: 'Medium',
      category: 'Cache',
      riskLevel: 'Medium',
      estimatedGain: '*****% memory performance'
    },
    {
      id: 'spectre',
      title: 'Disable Spectre/Meltdown Mitigations',
      description: 'Disable security mitigations for maximum performance (security risk)',
      impact: 'Very High',
      category: 'Security',
      riskLevel: 'High',
      estimatedGain: '+15-30% performance'
    }
  ];

  const handleTweakToggle = (tweakId) => {
    if (!isAdmin) return;
    
    setCpuTweaks(prev => ({
      ...prev,
      [tweakId]: !prev[tweakId]
    }));
  };

  const applyOptimizations = async () => {
    if (!isAdmin) return;
    
    setIsOptimizing(true);
    setOptimizationProgress(0);

    const enabledTweaks = Object.entries(cpuTweaks)
      .filter(([_, enabled]) => enabled)
      .map(([id, _]) => id);

    for (let i = 0; i < enabledTweaks.length; i++) {
      const tweakId = enabledTweaks[i];
      
      try {
        // Simulate applying tweak
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Here you would call the actual optimization functions
        // await window.electronAPI.applyCpuTweak(tweakId);
        
        setOptimizationProgress(((i + 1) / enabledTweaks.length) * 100);
      } catch (error) {
        console.error(`Failed to apply ${tweakId}:`, error);
      }
    }

    setIsOptimizing(false);
    
    // Show success message
    await window.electronAPI.showMessageBox({
      type: 'info',
      title: 'CPU Optimization Complete',
      message: `Successfully applied ${enabledTweaks.length} CPU optimizations!`,
      buttons: ['OK']
    });
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'Low': return '#00ff88';
      case 'Medium': return '#ffa502';
      case 'High': return '#ff4757';
      default: return '#888';
    }
  };

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'Very High': return '#ff6b35';
      case 'High': return '#00ff88';
      case 'Medium': return '#74b9ff';
      case 'Low': return '#888';
      default: return '#888';
    }
  };

  return (
    <div className="cpu-optimization">
      <div className="page-header">
        <div className="header-content">
          <div className="header-icon">
            <Cpu size={32} />
          </div>
          <div className="header-text">
            <h1>CPU Optimization</h1>
            <p>Advanced processor tweaks and performance optimizations</p>
          </div>
        </div>
        
        {systemInfo?.cpu && (
          <div className="cpu-info-card">
            <h3>{systemInfo.cpu.brand}</h3>
            <div className="cpu-specs">
              <span>{systemInfo.cpu.cores} cores</span>
              <span>•</span>
              <span>{systemInfo.cpu.physicalCores} physical</span>
              <span>•</span>
              <span>{systemInfo.cpu.speed} GHz</span>
            </div>
          </div>
        )}
      </div>

      <div className="cpu-content">
        {/* CPU Metrics */}
        <div className="metrics-section">
          <h2>Real-time CPU Metrics</h2>
          <div className="metrics-grid">
            <div className="metric-card">
              <div className="metric-icon temp">
                <Thermometer size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{cpuMetrics.temperature}°C</div>
                <div className="metric-label">Temperature</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon usage">
                <Activity size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{cpuMetrics.usage}%</div>
                <div className="metric-label">Usage</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon freq">
                <BarChart3 size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{cpuMetrics.frequency} MHz</div>
                <div className="metric-label">Frequency</div>
              </div>
            </div>
            
            <div className="metric-card">
              <div className="metric-icon power">
                <Zap size={20} />
              </div>
              <div className="metric-content">
                <div className="metric-value">{cpuMetrics.powerDraw}W</div>
                <div className="metric-label">Power Draw</div>
              </div>
            </div>
          </div>
        </div>

        {/* Optimization Controls */}
        <div className="optimizations-section">
          <div className="section-header">
            <h2>CPU Optimizations</h2>
            <button 
              className="apply-btn"
              onClick={applyOptimizations}
              disabled={!isAdmin || isOptimizing || Object.values(cpuTweaks).every(v => !v)}
            >
              {isOptimizing ? (
                <>
                  <Activity size={16} className="spinning" />
                  Optimizing... {Math.round(optimizationProgress)}%
                </>
              ) : (
                <>
                  <Play size={16} />
                  Apply Selected Tweaks
                </>
              )}
            </button>
          </div>

          <div className="tweaks-grid">
            {cpuOptimizations.map((tweak) => (
              <div key={tweak.id} className={`tweak-card ${cpuTweaks[tweak.id] ? 'enabled' : ''}`}>
                <div className="tweak-header">
                  <div className="tweak-title">
                    <h3>{tweak.title}</h3>
                    <div className="tweak-badges">
                      <span 
                        className="impact-badge"
                        style={{ backgroundColor: getImpactColor(tweak.impact) }}
                      >
                        {tweak.impact} Impact
                      </span>
                      <span 
                        className="risk-badge"
                        style={{ backgroundColor: getRiskColor(tweak.riskLevel) }}
                      >
                        {tweak.riskLevel} Risk
                      </span>
                    </div>
                  </div>
                  
                  <label className="toggle">
                    <input
                      type="checkbox"
                      checked={cpuTweaks[tweak.id]}
                      onChange={() => handleTweakToggle(tweak.id)}
                      disabled={!isAdmin}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
                
                <p className="tweak-description">{tweak.description}</p>
                
                <div className="tweak-details">
                  <div className="detail-item">
                    <span className="detail-label">Category:</span>
                    <span className="detail-value">{tweak.category}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Estimated Gain:</span>
                    <span className="detail-value gain">{tweak.estimatedGain}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {!isAdmin && (
        <div className="admin-required">
          <AlertTriangle size={20} />
          <span>Administrator privileges required for CPU optimizations</span>
        </div>
      )}
    </div>
  );
};

export default CpuOptimization;
