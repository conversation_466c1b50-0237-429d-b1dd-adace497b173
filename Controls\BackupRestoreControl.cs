using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RodeyPremiumTweaker.Controls
{
    public partial class BackupRestoreControl : UserControl
    {
        public BackupRestoreControl()
        {
            InitializeComponent();
            SetupLayout();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.BackColor = Color.FromArgb(8, 8, 8);
            this.Size = new Size(1200, 900);
            this.ResumeLayout(false);
        }

        private void SetupLayout()
        {
            var titleLabel = new Label
            {
                Text = "💾 BACKUP & RESTORE",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.FromArgb(138, 43, 226),
                AutoSize = true,
                Location = new Point(30, 30)
            };

            var descLabel = new Label
            {
                Text = "BACKUP SYSTEM SETTINGS BEFORE OPTIMIZATION",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.FromArgb(200, 200, 200),
                AutoSize = true,
                Location = new Point(30, 80)
            };

            this.Controls.AddRange(new Control[] { titleLabel, descLabel });
        }
    }
}
