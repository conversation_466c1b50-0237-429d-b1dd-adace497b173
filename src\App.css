/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #0a0a0a;
  color: #ffffff;
  overflow: hidden;
  user-select: none;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  background: rgba(15, 15, 15, 0.8);
  backdrop-filter: blur(10px);
}

.page-container {
  padding: 20px;
  height: 100%;
}

/* Admin Warning */
.admin-warning {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
  border-radius: 12px;
  padding: 16px;
  max-width: 350px;
  box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.warning-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.warning-text strong {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

.warning-text p {
  font-size: 12px;
  opacity: 0.9;
  line-height: 1.4;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 26, 26, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.5);
}

/* Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  color: #000;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background: linear-gradient(135deg, #333 0%, #555 100%);
  color: #fff;
}

.btn-secondary:hover {
  box-shadow: 0 8px 25px rgba(85, 85, 85, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: #fff;
}

.btn-danger:hover {
  box-shadow: 0 8px 25px rgba(255, 71, 87, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #ffa502 0%, #ff9500 100%);
  color: #000;
}

.btn-warning:hover {
  box-shadow: 0 8px 25px rgba(255, 165, 2, 0.3);
}

/* Card Styles */
.card {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: rgba(0, 255, 136, 0.3);
  box-shadow: 0 8px 32px rgba(0, 255, 136, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #00ff88;
}

.card-content {
  color: #ccc;
  line-height: 1.6;
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: 20px;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1200px) {
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .grid-3,
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #00ff88;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(26, 26, 26, 0.8);
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #00ff88;
  box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(26, 26, 26, 0.8);
  color: #fff;
  font-size: 14px;
  cursor: pointer;
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #333;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
  background-color: #00ff88;
}

.toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88 0%, #00cc6a 100%);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-success {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.status-warning {
  background: rgba(255, 165, 2, 0.2);
  color: #ffa502;
}

.status-error {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
}

.status-info {
  background: rgba(116, 185, 255, 0.2);
  color: #74b9ff;
}
