{"ast": null, "code": "var _jsxFileName = \"C:\\\\rodeypremium\\\\src\\\\components\\\\RegistryTweaks.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Wrench, Search, Filter, Play, AlertTriangle, CheckCircle, TrendingUp, Activity, Shield, Zap, Monitor, Cpu, HardDrive, Wifi, Settings } from 'lucide-react';\nimport './RegistryTweaks.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RegistryTweaks = ({\n  isAdmin\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedTweaks, setSelectedTweaks] = useState({});\n  const [isApplying, setIsApplying] = useState(false);\n  const [appliedTweaks, setAppliedTweaks] = useState(new Set());\n  const tweakCategories = {\n    all: {\n      name: 'All Tweaks',\n      icon: Wrench,\n      color: '#00ff88'\n    },\n    gaming: {\n      name: 'Gaming Performance',\n      icon: Zap,\n      color: '#00ff88'\n    },\n    system: {\n      name: 'System Performance',\n      icon: Activity,\n      color: '#74b9ff'\n    },\n    network: {\n      name: 'Network Optimization',\n      icon: Wifi,\n      color: '#a29bfe'\n    },\n    security: {\n      name: 'Security & Privacy',\n      icon: Shield,\n      color: '#ff6b35'\n    },\n    visual: {\n      name: 'Visual Effects',\n      icon: Monitor,\n      color: '#ffa502'\n    },\n    cpu: {\n      name: 'CPU Optimization',\n      icon: Cpu,\n      color: '#ff4757'\n    },\n    memory: {\n      name: 'Memory Management',\n      icon: HardDrive,\n      color: '#2ed573'\n    },\n    startup: {\n      name: 'Startup & Boot',\n      icon: Settings,\n      color: '#ff9ff3'\n    }\n  };\n  const registryTweaks = [\n  // Gaming Performance Tweaks\n  {\n    id: 'disable_game_bar',\n    name: 'Disable Windows Game Bar',\n    description: 'Disables Xbox Game Bar for better gaming performance',\n    category: 'gaming',\n    impact: 'High',\n    risk: 'Low',\n    fpsGain: '+5-15 FPS',\n    registry: 'HKEY_CURRENT_USER\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\GameDVR',\n    value: 'AppCaptureEnabled',\n    data: '0'\n  }, {\n    id: 'disable_fullscreen_opt',\n    name: 'Disable Fullscreen Optimizations',\n    description: 'Prevents Windows from optimizing fullscreen applications',\n    category: 'gaming',\n    impact: 'High',\n    risk: 'Low',\n    fpsGain: '+10-25 FPS',\n    registry: 'HKEY_CURRENT_USER\\\\System\\\\GameConfigStore',\n    value: 'GameDVR_Enabled',\n    data: '0'\n  }, {\n    id: 'gpu_scheduling',\n    name: 'Hardware Accelerated GPU Scheduling',\n    description: 'Enables HAGS for reduced CPU overhead',\n    category: 'gaming',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: '+3-12 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\GraphicsDrivers',\n    value: 'HwSchMode',\n    data: '2'\n  }, {\n    id: 'game_mode',\n    name: 'Enable Game Mode',\n    description: 'Optimizes system resources for gaming',\n    category: 'gaming',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: '+2-8 FPS',\n    registry: 'HKEY_CURRENT_USER\\\\SOFTWARE\\\\Microsoft\\\\GameBar',\n    value: 'AutoGameModeEnabled',\n    data: '1'\n  },\n  // System Performance Tweaks\n  {\n    id: 'disable_superfetch',\n    name: 'Disable Superfetch/SysMain',\n    description: 'Disables Windows prefetching service',\n    category: 'system',\n    impact: 'High',\n    risk: 'Medium',\n    fpsGain: '+8-20 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\SysMain',\n    value: 'Start',\n    data: '4'\n  }, {\n    id: 'disable_windows_search',\n    name: 'Disable Windows Search Indexing',\n    description: 'Stops background file indexing',\n    category: 'system',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: '+3-10 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\WSearch',\n    value: 'Start',\n    data: '4'\n  }, {\n    id: 'priority_separation',\n    name: 'Optimize CPU Priority',\n    description: 'Sets CPU priority for foreground applications',\n    category: 'system',\n    impact: 'High',\n    risk: 'Low',\n    fpsGain: '+5-15 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\PriorityControl',\n    value: 'Win32PrioritySeparation',\n    data: '38'\n  },\n  // Network Optimization\n  {\n    id: 'tcp_chimney',\n    name: 'Enable TCP Chimney Offload',\n    description: 'Offloads TCP processing to network adapter',\n    category: 'network',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: '+2-8 FPS (online games)',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters',\n    value: 'EnableTCPChimney',\n    data: '1'\n  }, {\n    id: 'nagle_algorithm',\n    name: 'Disable Nagle Algorithm',\n    description: 'Reduces network latency for gaming',\n    category: 'network',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: 'Reduced latency',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters\\\\Interfaces',\n    value: 'TcpAckFrequency',\n    data: '1'\n  },\n  // CPU Optimization\n  {\n    id: 'disable_core_parking',\n    name: 'Disable CPU Core Parking',\n    description: 'Prevents Windows from parking CPU cores',\n    category: 'cpu',\n    impact: 'Very High',\n    risk: 'Low',\n    fpsGain: '+15-30 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Power\\\\PowerSettings\\\\54533251-82be-4824-96c1-47b60b740d00\\\\0cc5b647-c1df-4637-891a-dec35c318583',\n    value: 'ValueMax',\n    data: '0'\n  }, {\n    id: 'cpu_idle_disable',\n    name: 'Disable CPU Idle States',\n    description: 'Prevents CPU from entering idle states',\n    category: 'cpu',\n    impact: 'High',\n    risk: 'Medium',\n    fpsGain: '+10-25 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Processor',\n    value: 'Capabilities',\n    data: '0x0007e066'\n  },\n  // Memory Management\n  {\n    id: 'large_system_cache',\n    name: 'Optimize System Cache',\n    description: 'Optimizes system cache for better performance',\n    category: 'memory',\n    impact: 'High',\n    risk: 'Low',\n    fpsGain: '+5-18 FPS',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Session Manager\\\\Memory Management',\n    value: 'LargeSystemCache',\n    data: '1'\n  }, {\n    id: 'clear_pagefile',\n    name: 'Disable Pagefile Clearing',\n    description: 'Speeds up shutdown by not clearing pagefile',\n    category: 'memory',\n    impact: 'Medium',\n    risk: 'Low',\n    fpsGain: 'Faster boot/shutdown',\n    registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Session Manager\\\\Memory Management',\n    value: 'ClearPageFileAtShutdown',\n    data: '0'\n  }];\n  const filteredTweaks = registryTweaks.filter(tweak => {\n    const matchesSearch = tweak.name.toLowerCase().includes(searchTerm.toLowerCase()) || tweak.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || tweak.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n  const handleTweakToggle = tweakId => {\n    if (!isAdmin) return;\n    setSelectedTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n  const applySelectedTweaks = async () => {\n    if (!isAdmin) return;\n    setIsApplying(true);\n    const tweaksToApply = Object.entries(selectedTweaks).filter(([_, selected]) => selected).map(([id, _]) => id);\n    for (let i = 0; i < tweaksToApply.length; i++) {\n      const tweakId = tweaksToApply[i];\n      const tweak = registryTweaks.find(t => t.id === tweakId);\n      try {\n        // Simulate applying registry tweak\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // Here you would call the actual registry modification\n        // await window.electronAPI.writeRegistry(tweak.registry, tweak.value, tweak.data, 'REG_DWORD');\n\n        setAppliedTweaks(prev => new Set([...prev, tweakId]));\n      } catch (error) {\n        console.error(`Failed to apply ${tweak.name}:`, error);\n      }\n    }\n    setIsApplying(false);\n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'Registry Tweaks Applied',\n      message: `Successfully applied ${tweaksToApply.length} registry tweaks!\\nRestart may be required for some changes to take effect.`,\n      buttons: ['OK']\n    });\n  };\n  const getRiskColor = risk => {\n    switch (risk) {\n      case 'Low':\n        return '#00ff88';\n      case 'Medium':\n        return '#ffa502';\n      case 'High':\n        return '#ff4757';\n      default:\n        return '#888';\n    }\n  };\n  const getImpactColor = impact => {\n    switch (impact) {\n      case 'Very High':\n        return '#ff6b35';\n      case 'High':\n        return '#00ff88';\n      case 'Medium':\n        return '#74b9ff';\n      case 'Low':\n        return '#888';\n      default:\n        return '#888';\n    }\n  };\n  const selectedCount = Object.values(selectedTweaks).filter(Boolean).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"registry-tweaks\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-icon\",\n          children: /*#__PURE__*/_jsxDEV(Wrench, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Registry Tweaks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"2000+ advanced registry modifications for maximum performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: filteredTweaks.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Available Tweaks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: appliedTweaks.size\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Applied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-value\",\n            children: selectedCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-filter\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-box\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search tweaks...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"category-filter\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            children: Object.entries(tweakCategories).map(([key, category]) => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: key,\n              children: category.name\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"apply-btn\",\n        onClick: applySelectedTweaks,\n        disabled: !isAdmin || isApplying || selectedCount === 0,\n        children: isApplying ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Activity, {\n            size: 16,\n            className: \"spinning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), \"Applying \", selectedCount, \" Tweaks...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Play, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), \"Apply \", selectedCount, \" Selected Tweaks\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tweaks-grid\",\n      children: filteredTweaks.map(tweak => {\n        const isSelected = selectedTweaks[tweak.id];\n        const isApplied = appliedTweaks.has(tweak.id);\n        const categoryInfo = tweakCategories[tweak.category];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `tweak-card ${isSelected ? 'selected' : ''} ${isApplied ? 'applied' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tweak-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tweak-category\",\n                style: {\n                  color: categoryInfo.color\n                },\n                children: categoryInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: tweak.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tweak-badges\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"impact-badge\",\n                  style: {\n                    backgroundColor: getImpactColor(tweak.impact)\n                  },\n                  children: [tweak.impact, \" Impact\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"risk-badge\",\n                  style: {\n                    backgroundColor: getRiskColor(tweak.risk)\n                  },\n                  children: [tweak.risk, \" Risk\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tweak-controls\",\n              children: [isApplied && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"applied-indicator\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: isSelected,\n                  onChange: () => handleTweakToggle(tweak.id),\n                  disabled: !isAdmin || isApplied\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"tweak-description\",\n            children: tweak.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tweak-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Performance Gain:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value gain\",\n                children: tweak.fpsGain\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Registry Path:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value registry\",\n                children: tweak.registry\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Value:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: [tweak.value, \" = \", tweak.data]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)]\n        }, tweak.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-required\",\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Administrator privileges required for registry modifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 5\n  }, this);\n};\n_s(RegistryTweaks, \"r1bEFQXa6ElEq6ldnVV6tZSi99A=\");\n_c = RegistryTweaks;\nexport default RegistryTweaks;\nvar _c;\n$RefreshReg$(_c, \"RegistryTweaks\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Search", "Filter", "Play", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "TrendingUp", "Activity", "Shield", "Zap", "Monitor", "Cpu", "HardDrive", "Wifi", "Settings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RegistryTweaks", "isAdmin", "_s", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedTweaks", "setSelectedTweaks", "isApplying", "setIsApplying", "appliedTweaks", "setAppliedTweaks", "Set", "tweakCategories", "all", "name", "icon", "color", "gaming", "system", "network", "security", "visual", "cpu", "memory", "startup", "registryTweaks", "id", "description", "category", "impact", "risk", "fpsGain", "registry", "value", "data", "filteredTweaks", "filter", "tweak", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "handleTweakToggle", "tweakId", "prev", "applySelectedTweaks", "tweaksToApply", "Object", "entries", "_", "selected", "map", "i", "length", "find", "t", "Promise", "resolve", "setTimeout", "error", "console", "window", "electronAPI", "showMessageBox", "type", "title", "message", "buttons", "getRiskColor", "getImpactColor", "selectedCount", "values", "Boolean", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "e", "target", "key", "onClick", "disabled", "isSelected", "isApplied", "has", "categoryInfo", "style", "backgroundColor", "checked", "_c", "$RefreshReg$"], "sources": ["C:/rodeypremium/src/components/RegistryTweaks.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Wrench, \n  Search, \n  Filter, \n  Play, \n  AlertTriangle,\n  CheckCircle,\n  TrendingUp,\n  Activity,\n  Shield,\n  Zap,\n  Monitor,\n  Cpu,\n  HardDrive,\n  Wifi,\n  Settings\n} from 'lucide-react';\nimport './RegistryTweaks.css';\n\nconst RegistryTweaks = ({ isAdmin }) => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedTweaks, setSelectedTweaks] = useState({});\n  const [isApplying, setIsApplying] = useState(false);\n  const [appliedTweaks, setAppliedTweaks] = useState(new Set());\n\n  const tweakCategories = {\n    all: { name: 'All Tweaks', icon: Wrench, color: '#00ff88' },\n    gaming: { name: 'Gaming Performance', icon: Zap, color: '#00ff88' },\n    system: { name: 'System Performance', icon: Activity, color: '#74b9ff' },\n    network: { name: 'Network Optimization', icon: Wifi, color: '#a29bfe' },\n    security: { name: 'Security & Privacy', icon: Shield, color: '#ff6b35' },\n    visual: { name: 'Visual Effects', icon: Monitor, color: '#ffa502' },\n    cpu: { name: 'CPU Optimization', icon: Cpu, color: '#ff4757' },\n    memory: { name: 'Memory Management', icon: HardDrive, color: '#2ed573' },\n    startup: { name: 'Startup & Boot', icon: Settings, color: '#ff9ff3' }\n  };\n\n  const registryTweaks = [\n    // Gaming Performance Tweaks\n    {\n      id: 'disable_game_bar',\n      name: 'Disable Windows Game Bar',\n      description: 'Disables Xbox Game Bar for better gaming performance',\n      category: 'gaming',\n      impact: 'High',\n      risk: 'Low',\n      fpsGain: '+5-15 FPS',\n      registry: 'HKEY_CURRENT_USER\\\\SOFTWARE\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\GameDVR',\n      value: 'AppCaptureEnabled',\n      data: '0'\n    },\n    {\n      id: 'disable_fullscreen_opt',\n      name: 'Disable Fullscreen Optimizations',\n      description: 'Prevents Windows from optimizing fullscreen applications',\n      category: 'gaming',\n      impact: 'High',\n      risk: 'Low',\n      fpsGain: '+10-25 FPS',\n      registry: 'HKEY_CURRENT_USER\\\\System\\\\GameConfigStore',\n      value: 'GameDVR_Enabled',\n      data: '0'\n    },\n    {\n      id: 'gpu_scheduling',\n      name: 'Hardware Accelerated GPU Scheduling',\n      description: 'Enables HAGS for reduced CPU overhead',\n      category: 'gaming',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: '+3-12 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\GraphicsDrivers',\n      value: 'HwSchMode',\n      data: '2'\n    },\n    {\n      id: 'game_mode',\n      name: 'Enable Game Mode',\n      description: 'Optimizes system resources for gaming',\n      category: 'gaming',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: '+2-8 FPS',\n      registry: 'HKEY_CURRENT_USER\\\\SOFTWARE\\\\Microsoft\\\\GameBar',\n      value: 'AutoGameModeEnabled',\n      data: '1'\n    },\n    \n    // System Performance Tweaks\n    {\n      id: 'disable_superfetch',\n      name: 'Disable Superfetch/SysMain',\n      description: 'Disables Windows prefetching service',\n      category: 'system',\n      impact: 'High',\n      risk: 'Medium',\n      fpsGain: '+8-20 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\SysMain',\n      value: 'Start',\n      data: '4'\n    },\n    {\n      id: 'disable_windows_search',\n      name: 'Disable Windows Search Indexing',\n      description: 'Stops background file indexing',\n      category: 'system',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: '+3-10 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\WSearch',\n      value: 'Start',\n      data: '4'\n    },\n    {\n      id: 'priority_separation',\n      name: 'Optimize CPU Priority',\n      description: 'Sets CPU priority for foreground applications',\n      category: 'system',\n      impact: 'High',\n      risk: 'Low',\n      fpsGain: '+5-15 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\PriorityControl',\n      value: 'Win32PrioritySeparation',\n      data: '38'\n    },\n    \n    // Network Optimization\n    {\n      id: 'tcp_chimney',\n      name: 'Enable TCP Chimney Offload',\n      description: 'Offloads TCP processing to network adapter',\n      category: 'network',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: '+2-8 FPS (online games)',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters',\n      value: 'EnableTCPChimney',\n      data: '1'\n    },\n    {\n      id: 'nagle_algorithm',\n      name: 'Disable Nagle Algorithm',\n      description: 'Reduces network latency for gaming',\n      category: 'network',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: 'Reduced latency',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters\\\\Interfaces',\n      value: 'TcpAckFrequency',\n      data: '1'\n    },\n    \n    // CPU Optimization\n    {\n      id: 'disable_core_parking',\n      name: 'Disable CPU Core Parking',\n      description: 'Prevents Windows from parking CPU cores',\n      category: 'cpu',\n      impact: 'Very High',\n      risk: 'Low',\n      fpsGain: '+15-30 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Power\\\\PowerSettings\\\\54533251-82be-4824-96c1-47b60b740d00\\\\0cc5b647-c1df-4637-891a-dec35c318583',\n      value: 'ValueMax',\n      data: '0'\n    },\n    {\n      id: 'cpu_idle_disable',\n      name: 'Disable CPU Idle States',\n      description: 'Prevents CPU from entering idle states',\n      category: 'cpu',\n      impact: 'High',\n      risk: 'Medium',\n      fpsGain: '+10-25 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Processor',\n      value: 'Capabilities',\n      data: '0x0007e066'\n    },\n    \n    // Memory Management\n    {\n      id: 'large_system_cache',\n      name: 'Optimize System Cache',\n      description: 'Optimizes system cache for better performance',\n      category: 'memory',\n      impact: 'High',\n      risk: 'Low',\n      fpsGain: '+5-18 FPS',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Session Manager\\\\Memory Management',\n      value: 'LargeSystemCache',\n      data: '1'\n    },\n    {\n      id: 'clear_pagefile',\n      name: 'Disable Pagefile Clearing',\n      description: 'Speeds up shutdown by not clearing pagefile',\n      category: 'memory',\n      impact: 'Medium',\n      risk: 'Low',\n      fpsGain: 'Faster boot/shutdown',\n      registry: 'HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Control\\\\Session Manager\\\\Memory Management',\n      value: 'ClearPageFileAtShutdown',\n      data: '0'\n    }\n  ];\n\n  const filteredTweaks = registryTweaks.filter(tweak => {\n    const matchesSearch = tweak.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tweak.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || tweak.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleTweakToggle = (tweakId) => {\n    if (!isAdmin) return;\n    \n    setSelectedTweaks(prev => ({\n      ...prev,\n      [tweakId]: !prev[tweakId]\n    }));\n  };\n\n  const applySelectedTweaks = async () => {\n    if (!isAdmin) return;\n    \n    setIsApplying(true);\n    const tweaksToApply = Object.entries(selectedTweaks)\n      .filter(([_, selected]) => selected)\n      .map(([id, _]) => id);\n\n    for (let i = 0; i < tweaksToApply.length; i++) {\n      const tweakId = tweaksToApply[i];\n      const tweak = registryTweaks.find(t => t.id === tweakId);\n      \n      try {\n        // Simulate applying registry tweak\n        await new Promise(resolve => setTimeout(resolve, 500));\n        \n        // Here you would call the actual registry modification\n        // await window.electronAPI.writeRegistry(tweak.registry, tweak.value, tweak.data, 'REG_DWORD');\n        \n        setAppliedTweaks(prev => new Set([...prev, tweakId]));\n      } catch (error) {\n        console.error(`Failed to apply ${tweak.name}:`, error);\n      }\n    }\n\n    setIsApplying(false);\n    \n    await window.electronAPI.showMessageBox({\n      type: 'info',\n      title: 'Registry Tweaks Applied',\n      message: `Successfully applied ${tweaksToApply.length} registry tweaks!\\nRestart may be required for some changes to take effect.`,\n      buttons: ['OK']\n    });\n  };\n\n  const getRiskColor = (risk) => {\n    switch (risk) {\n      case 'Low': return '#00ff88';\n      case 'Medium': return '#ffa502';\n      case 'High': return '#ff4757';\n      default: return '#888';\n    }\n  };\n\n  const getImpactColor = (impact) => {\n    switch (impact) {\n      case 'Very High': return '#ff6b35';\n      case 'High': return '#00ff88';\n      case 'Medium': return '#74b9ff';\n      case 'Low': return '#888';\n      default: return '#888';\n    }\n  };\n\n  const selectedCount = Object.values(selectedTweaks).filter(Boolean).length;\n\n  return (\n    <div className=\"registry-tweaks\">\n      <div className=\"page-header\">\n        <div className=\"header-content\">\n          <div className=\"header-icon\">\n            <Wrench size={32} />\n          </div>\n          <div className=\"header-text\">\n            <h1>Registry Tweaks</h1>\n            <p>2000+ advanced registry modifications for maximum performance</p>\n          </div>\n        </div>\n        \n        <div className=\"stats-card\">\n          <div className=\"stat\">\n            <span className=\"stat-value\">{filteredTweaks.length}</span>\n            <span className=\"stat-label\">Available Tweaks</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{appliedTweaks.size}</span>\n            <span className=\"stat-label\">Applied</span>\n          </div>\n          <div className=\"stat\">\n            <span className=\"stat-value\">{selectedCount}</span>\n            <span className=\"stat-label\">Selected</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"controls-section\">\n        <div className=\"search-filter\">\n          <div className=\"search-box\">\n            <Search size={16} />\n            <input\n              type=\"text\"\n              placeholder=\"Search tweaks...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          \n          <div className=\"category-filter\">\n            <Filter size={16} />\n            <select \n              value={selectedCategory} \n              onChange={(e) => setSelectedCategory(e.target.value)}\n            >\n              {Object.entries(tweakCategories).map(([key, category]) => (\n                <option key={key} value={key}>{category.name}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        <button \n          className=\"apply-btn\"\n          onClick={applySelectedTweaks}\n          disabled={!isAdmin || isApplying || selectedCount === 0}\n        >\n          {isApplying ? (\n            <>\n              <Activity size={16} className=\"spinning\" />\n              Applying {selectedCount} Tweaks...\n            </>\n          ) : (\n            <>\n              <Play size={16} />\n              Apply {selectedCount} Selected Tweaks\n            </>\n          )}\n        </button>\n      </div>\n\n      <div className=\"tweaks-grid\">\n        {filteredTweaks.map((tweak) => {\n          const isSelected = selectedTweaks[tweak.id];\n          const isApplied = appliedTweaks.has(tweak.id);\n          const categoryInfo = tweakCategories[tweak.category];\n          \n          return (\n            <div \n              key={tweak.id} \n              className={`tweak-card ${isSelected ? 'selected' : ''} ${isApplied ? 'applied' : ''}`}\n            >\n              <div className=\"tweak-header\">\n                <div className=\"tweak-info\">\n                  <div className=\"tweak-category\" style={{ color: categoryInfo.color }}>\n                    {categoryInfo.name}\n                  </div>\n                  <h3>{tweak.name}</h3>\n                  <div className=\"tweak-badges\">\n                    <span \n                      className=\"impact-badge\"\n                      style={{ backgroundColor: getImpactColor(tweak.impact) }}\n                    >\n                      {tweak.impact} Impact\n                    </span>\n                    <span \n                      className=\"risk-badge\"\n                      style={{ backgroundColor: getRiskColor(tweak.risk) }}\n                    >\n                      {tweak.risk} Risk\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"tweak-controls\">\n                  {isApplied && (\n                    <div className=\"applied-indicator\">\n                      <CheckCircle size={16} />\n                    </div>\n                  )}\n                  <label className=\"toggle\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={() => handleTweakToggle(tweak.id)}\n                      disabled={!isAdmin || isApplied}\n                    />\n                    <span className=\"toggle-slider\"></span>\n                  </label>\n                </div>\n              </div>\n              \n              <p className=\"tweak-description\">{tweak.description}</p>\n              \n              <div className=\"tweak-details\">\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">Performance Gain:</span>\n                  <span className=\"detail-value gain\">{tweak.fpsGain}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">Registry Path:</span>\n                  <span className=\"detail-value registry\">{tweak.registry}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">Value:</span>\n                  <span className=\"detail-value\">{tweak.value} = {tweak.data}</span>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {!isAdmin && (\n        <div className=\"admin-required\">\n          <AlertTriangle size={20} />\n          <span>Administrator privileges required for registry modifications</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default RegistryTweaks;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,QAAQ,QACH,cAAc;AACrB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;EAE7D,MAAMC,eAAe,GAAG;IACtBC,GAAG,EAAE;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAEpC,MAAM;MAAEqC,KAAK,EAAE;IAAU,CAAC;IAC3DC,MAAM,EAAE;MAAEH,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE3B,GAAG;MAAE4B,KAAK,EAAE;IAAU,CAAC;IACnEE,MAAM,EAAE;MAAEJ,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE7B,QAAQ;MAAE8B,KAAK,EAAE;IAAU,CAAC;IACxEG,OAAO,EAAE;MAAEL,IAAI,EAAE,sBAAsB;MAAEC,IAAI,EAAEvB,IAAI;MAAEwB,KAAK,EAAE;IAAU,CAAC;IACvEI,QAAQ,EAAE;MAAEN,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE5B,MAAM;MAAE6B,KAAK,EAAE;IAAU,CAAC;IACxEK,MAAM,EAAE;MAAEP,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE1B,OAAO;MAAE2B,KAAK,EAAE;IAAU,CAAC;IACnEM,GAAG,EAAE;MAAER,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAEzB,GAAG;MAAE0B,KAAK,EAAE;IAAU,CAAC;IAC9DO,MAAM,EAAE;MAAET,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAExB,SAAS;MAAEyB,KAAK,EAAE;IAAU,CAAC;IACxEQ,OAAO,EAAE;MAAEV,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAEtB,QAAQ;MAAEuB,KAAK,EAAE;IAAU;EACtE,CAAC;EAED,MAAMS,cAAc,GAAG;EACrB;EACA;IACEC,EAAE,EAAE,kBAAkB;IACtBZ,IAAI,EAAE,0BAA0B;IAChCa,WAAW,EAAE,sDAAsD;IACnEC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,0EAA0E;IACpFC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,wBAAwB;IAC5BZ,IAAI,EAAE,kCAAkC;IACxCa,WAAW,EAAE,0DAA0D;IACvEC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,4CAA4C;IACtDC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,gBAAgB;IACpBZ,IAAI,EAAE,qCAAqC;IAC3Ca,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,yEAAyE;IACnFC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,WAAW;IACfZ,IAAI,EAAE,kBAAkB;IACxBa,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,UAAU;IACnBC,QAAQ,EAAE,iDAAiD;IAC3DC,KAAK,EAAE,qBAAqB;IAC5BC,IAAI,EAAE;EACR,CAAC;EAED;EACA;IACER,EAAE,EAAE,oBAAoB;IACxBZ,IAAI,EAAE,4BAA4B;IAClCa,WAAW,EAAE,sCAAsC;IACnDC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,kEAAkE;IAC5EC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,wBAAwB;IAC5BZ,IAAI,EAAE,iCAAiC;IACvCa,WAAW,EAAE,gCAAgC;IAC7CC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,kEAAkE;IAC5EC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,qBAAqB;IACzBZ,IAAI,EAAE,uBAAuB;IAC7Ba,WAAW,EAAE,+CAA+C;IAC5DC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,yEAAyE;IACnFC,KAAK,EAAE,yBAAyB;IAChCC,IAAI,EAAE;EACR,CAAC;EAED;EACA;IACER,EAAE,EAAE,aAAa;IACjBZ,IAAI,EAAE,4BAA4B;IAClCa,WAAW,EAAE,4CAA4C;IACzDC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,yBAAyB;IAClCC,QAAQ,EAAE,4EAA4E;IACtFC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,iBAAiB;IACrBZ,IAAI,EAAE,yBAAyB;IAC/Ba,WAAW,EAAE,oCAAoC;IACjDC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE,wFAAwF;IAClGC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC;EAED;EACA;IACER,EAAE,EAAE,sBAAsB;IAC1BZ,IAAI,EAAE,0BAA0B;IAChCa,WAAW,EAAE,yCAAyC;IACtDC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,0JAA0J;IACpKC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,kBAAkB;IACtBZ,IAAI,EAAE,yBAAyB;IAC/Ba,WAAW,EAAE,wCAAwC;IACrDC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,mEAAmE;IAC7EC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACR,CAAC;EAED;EACA;IACER,EAAE,EAAE,oBAAoB;IACxBZ,IAAI,EAAE,uBAAuB;IAC7Ba,WAAW,EAAE,+CAA+C;IAC5DC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,4FAA4F;IACtGC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACER,EAAE,EAAE,gBAAgB;IACpBZ,IAAI,EAAE,2BAA2B;IACjCa,WAAW,EAAE,6CAA6C;IAC1DC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,sBAAsB;IAC/BC,QAAQ,EAAE,4FAA4F;IACtGC,KAAK,EAAE,yBAAyB;IAChCC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,cAAc,GAAGV,cAAc,CAACW,MAAM,CAACC,KAAK,IAAI;IACpD,MAAMC,aAAa,GAAGD,KAAK,CAACvB,IAAI,CAACyB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IAC5DF,KAAK,CAACV,WAAW,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC;IACvF,MAAME,eAAe,GAAGtC,gBAAgB,KAAK,KAAK,IAAIkC,KAAK,CAACT,QAAQ,KAAKzB,gBAAgB;IACzF,OAAOmC,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,OAAO,IAAK;IACrC,IAAI,CAAC5C,OAAO,EAAE;IAEdO,iBAAiB,CAACsC,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC9C,OAAO,EAAE;IAEdS,aAAa,CAAC,IAAI,CAAC;IACnB,MAAMsC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAAC3C,cAAc,CAAC,CACjD+B,MAAM,CAAC,CAAC,CAACa,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,CAAC,CACnCC,GAAG,CAAC,CAAC,CAACzB,EAAE,EAAEuB,CAAC,CAAC,KAAKvB,EAAE,CAAC;IAEvB,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,aAAa,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMT,OAAO,GAAGG,aAAa,CAACM,CAAC,CAAC;MAChC,MAAMf,KAAK,GAAGZ,cAAc,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKiB,OAAO,CAAC;MAExD,IAAI;QACF;QACA,MAAM,IAAIa,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;QAEtD;QACA;;QAEA/C,gBAAgB,CAACkC,IAAI,IAAI,IAAIjC,GAAG,CAAC,CAAC,GAAGiC,IAAI,EAAED,OAAO,CAAC,CAAC,CAAC;MACvD,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mBAAmBtB,KAAK,CAACvB,IAAI,GAAG,EAAE6C,KAAK,CAAC;MACxD;IACF;IAEAnD,aAAa,CAAC,KAAK,CAAC;IAEpB,MAAMqD,MAAM,CAACC,WAAW,CAACC,cAAc,CAAC;MACtCC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,yBAAyB;MAChCC,OAAO,EAAE,wBAAwBpB,aAAa,CAACO,MAAM,6EAA6E;MAClIc,OAAO,EAAE,CAAC,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAItC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMuC,cAAc,GAAIxC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,MAAM;MACzB;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGvB,MAAM,CAACwB,MAAM,CAAClE,cAAc,CAAC,CAAC+B,MAAM,CAACoC,OAAO,CAAC,CAACnB,MAAM;EAE1E,oBACE1D,OAAA;IAAK8E,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B/E,OAAA;MAAK8E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/E,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/E,OAAA,CAAChB,MAAM;YAACgG,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNpF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/E,OAAA;YAAA+E,QAAA,EAAI;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBpF,OAAA;YAAA+E,QAAA,EAAG;UAA6D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/E,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEvC,cAAc,CAACkB;UAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DpF,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNpF,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/E,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEjE,aAAa,CAACkE;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxDpF,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNpF,OAAA;UAAK8E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/E,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEJ;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDpF,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA;MAAK8E,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/E,OAAA,CAACf,MAAM;YAAC+F,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBpF,OAAA;YACEqE,IAAI,EAAC,MAAM;YACXgB,WAAW,EAAC,kBAAkB;YAC9B/C,KAAK,EAAEhC,UAAW;YAClBgF,QAAQ,EAAGC,CAAC,IAAKhF,aAAa,CAACgF,CAAC,CAACC,MAAM,CAAClD,KAAK;UAAE;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpF,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/E,OAAA,CAACd,MAAM;YAAC8F,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBpF,OAAA;YACEsC,KAAK,EAAE9B,gBAAiB;YACxB8E,QAAQ,EAAGC,CAAC,IAAK9E,mBAAmB,CAAC8E,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;YAAAyC,QAAA,EAEpD3B,MAAM,CAACC,OAAO,CAACpC,eAAe,CAAC,CAACuC,GAAG,CAAC,CAAC,CAACiC,GAAG,EAAExD,QAAQ,CAAC,kBACnDjC,OAAA;cAAkBsC,KAAK,EAAEmD,GAAI;cAAAV,QAAA,EAAE9C,QAAQ,CAACd;YAAI,GAA/BsE,GAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpF,OAAA;QACE8E,SAAS,EAAC,WAAW;QACrBY,OAAO,EAAExC,mBAAoB;QAC7ByC,QAAQ,EAAE,CAACvF,OAAO,IAAIQ,UAAU,IAAI+D,aAAa,KAAK,CAAE;QAAAI,QAAA,EAEvDnE,UAAU,gBACTZ,OAAA,CAAAE,SAAA;UAAA6E,QAAA,gBACE/E,OAAA,CAACT,QAAQ;YAACyF,IAAI,EAAE,EAAG;YAACF,SAAS,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAClC,EAACT,aAAa,EAAC,YAC1B;QAAA,eAAE,CAAC,gBAEH3E,OAAA,CAAAE,SAAA;UAAA6E,QAAA,gBACE/E,OAAA,CAACb,IAAI;YAAC6F,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UACZ,EAACT,aAAa,EAAC,kBACvB;QAAA,eAAE;MACH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpF,OAAA;MAAK8E,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBvC,cAAc,CAACgB,GAAG,CAAEd,KAAK,IAAK;QAC7B,MAAMkD,UAAU,GAAGlF,cAAc,CAACgC,KAAK,CAACX,EAAE,CAAC;QAC3C,MAAM8D,SAAS,GAAG/E,aAAa,CAACgF,GAAG,CAACpD,KAAK,CAACX,EAAE,CAAC;QAC7C,MAAMgE,YAAY,GAAG9E,eAAe,CAACyB,KAAK,CAACT,QAAQ,CAAC;QAEpD,oBACEjC,OAAA;UAEE8E,SAAS,EAAE,cAAcc,UAAU,GAAG,UAAU,GAAG,EAAE,IAAIC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UAAAd,QAAA,gBAEtF/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/E,OAAA;gBAAK8E,SAAS,EAAC,gBAAgB;gBAACkB,KAAK,EAAE;kBAAE3E,KAAK,EAAE0E,YAAY,CAAC1E;gBAAM,CAAE;gBAAA0D,QAAA,EAClEgB,YAAY,CAAC5E;cAAI;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNpF,OAAA;gBAAA+E,QAAA,EAAKrC,KAAK,CAACvB;cAAI;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBpF,OAAA;gBAAK8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B/E,OAAA;kBACE8E,SAAS,EAAC,cAAc;kBACxBkB,KAAK,EAAE;oBAAEC,eAAe,EAAEvB,cAAc,CAAChC,KAAK,CAACR,MAAM;kBAAE,CAAE;kBAAA6C,QAAA,GAExDrC,KAAK,CAACR,MAAM,EAAC,SAChB;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPpF,OAAA;kBACE8E,SAAS,EAAC,YAAY;kBACtBkB,KAAK,EAAE;oBAAEC,eAAe,EAAExB,YAAY,CAAC/B,KAAK,CAACP,IAAI;kBAAE,CAAE;kBAAA4C,QAAA,GAEpDrC,KAAK,CAACP,IAAI,EAAC,OACd;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpF,OAAA;cAAK8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC5Bc,SAAS,iBACR7F,OAAA;gBAAK8E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChC/E,OAAA,CAACX,WAAW;kBAAC2F,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACN,eACDpF,OAAA;gBAAO8E,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACvB/E,OAAA;kBACEqE,IAAI,EAAC,UAAU;kBACf6B,OAAO,EAAEN,UAAW;kBACpBN,QAAQ,EAAEA,CAAA,KAAMvC,iBAAiB,CAACL,KAAK,CAACX,EAAE,CAAE;kBAC5C4D,QAAQ,EAAE,CAACvF,OAAO,IAAIyF;gBAAU;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACFpF,OAAA;kBAAM8E,SAAS,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpF,OAAA;YAAG8E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAErC,KAAK,CAACV;UAAW;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExDpF,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B/E,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/E,OAAA;gBAAM8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDpF,OAAA;gBAAM8E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAErC,KAAK,CAACN;cAAO;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNpF,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/E,OAAA;gBAAM8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDpF,OAAA;gBAAM8E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAErC,KAAK,CAACL;cAAQ;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNpF,OAAA;cAAK8E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/E,OAAA;gBAAM8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CpF,OAAA;gBAAM8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAErC,KAAK,CAACJ,KAAK,EAAC,KAAG,EAACI,KAAK,CAACH,IAAI;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1DD1C,KAAK,CAACX,EAAE;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2DV,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL,CAAChF,OAAO,iBACPJ,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/E,OAAA,CAACZ,aAAa;QAAC4F,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BpF,OAAA;QAAA+E,QAAA,EAAM;MAA4D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA5ZIF,cAAc;AAAAgG,EAAA,GAAdhG,cAAc;AA8ZpB,eAAeA,cAAc;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}